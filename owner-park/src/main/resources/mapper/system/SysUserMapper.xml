<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.harry.modular.system.mapper.SysUserMapper">

    <select id="selectByUsername" resultType="cn.harry.modular.system.domain.SysUser">
        select *
        from sys_user
        where username = #{username}
          and valid = 1
    </select>
</mapper>
