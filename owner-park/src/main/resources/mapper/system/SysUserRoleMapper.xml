<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.harry.modular.system.mapper.SysUserRoleMapper">
    <insert id="insertUserAndUserRole">
        insert into sys_user_role (user_id,role_id) values
        <foreach collection="roleIdList" item="roleId" separator=",">
            (#{userId},#{roleId})
        </foreach>
    </insert>

    <select id="listRoleKeyByUserId" resultType="java.lang.String">
        SELECT DISTINCT r.role_key
        FROM sys_user_role ur
                 LEFT JOIN sys_role r ON r.id = ur.role_id
        WHERE r.valid = 1 AND ur.user_id = #{userId}
    </select>
    <select id="getMaximumDataScope" resultType="java.lang.Integer">
        SELECT
        min(data_scope)
        FROM
        sys_role
        <where>
            <choose>
                <when test="roles!=null and roles.size>0">
                   AND  valid = 1 AND role_key IN
                    <foreach collection="roles" item="role" separator="," open="(" close=")">
                        #{role}
                    </foreach>
                </when>
                <otherwise>
                    id = -1
                </otherwise>
            </choose>
        </where>
    </select>
</mapper>
