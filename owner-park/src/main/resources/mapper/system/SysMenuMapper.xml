<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.harry.modular.system.mapper.SysMenuMapper">

    <select id="getMenuPermission" resultType="java.lang.String">
        SELECT
            DISTINCT p.`permission`
        FROM
            sys_user_role ar
                LEFT JOIN sys_role r ON ar.role_id = r.id
                LEFT JOIN sys_role_menu rp ON r.id = rp.role_id
                LEFT JOIN sys_menu p ON rp.menu_id = p.id
        WHERE  ar.user_id = #{userId}
          AND p.`permission` IS NOT NULL
          AND p.valid = 1
          AND r.valid = 1
    </select>
    <select id="getRoutersByUserId" resultType="cn.harry.modular.system.domain.SysMenu">
        SELECT DISTINCT
            m.id,
            m.pid,
            m.NAME,
            uri,
            m.type,
            m.icon,
            m.sort,
            m.path,
            m.always_show,
            m.keep_alive,
            m.params
        FROM
            sys_menu m
                LEFT JOIN sys_role_menu rm ON m.id = rm.menu_id
                LEFT JOIN sys_user_role ur ON rm.role_id = ur.role_id
        WHERE
            ur.user_id = #{userId}
          AND m.type IN ( '0', '1' ,'3')
          AND m.valid = 1
        ORDER BY
            m.pid,
            m.sort
    </select>
    <select id="getPermissionList" resultType="cn.harry.modular.system.domain.SysMenu">
        SELECT
            p.*
        FROM
            sys_user_role ar
                LEFT JOIN sys_role r ON ar.role_id = r.id
                LEFT JOIN sys_role_menu rp ON r.id = rp.role_id
                LEFT JOIN sys_menu p ON rp.menu_id = p.id
        WHERE  ar.user_id = #{userId}
        ORDER BY p.sort
    </select>
</mapper>
