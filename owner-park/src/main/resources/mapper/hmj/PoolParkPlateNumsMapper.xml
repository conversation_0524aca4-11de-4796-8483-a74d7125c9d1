<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.harry.modular.hmj.mapper.PoolParkPlateNumsMapper">

    <resultMap id="BaseResultMap" type="cn.harry.modular.hmj.domain.PoolParkPlateNums">
            <id property="id" column="id" />
            <result property="garageId" column="garage_id" />
            <result property="poolName" column="pool_name" />
            <result property="chargeType" column="charge_type" />
            <result property="fullMode" column="full_mode" />
            <result property="totalNum" column="total_num" />
            <result property="validNum" column="valid_num" />
            <result property="parkedNum" column="parked_num" />
            <result property="emptyNum" column="empty_num" />
            <result property="plateNums" column="plate_nums" />
            <result property="hourseAddr" column="hourse_addr" />
            <result property="pname" column="pname" />
            <result property="mobile" column="mobile" />
            <result property="freeTime" column="free_time" />
            <result property="syncStatus" column="sync_status" />
            <result property="syncLastDate" column="sync_last_date" />
    </resultMap>

    <sql id="Base_Column_List">
        id,garage_id,pool_name,charge_type,full_mode,total_num,valid_num,
        parked_num,empty_num,plate_nums,hourse_addr,pname,
        mobile,free_time,sync_status,sync_last_date
    </sql>
</mapper>
