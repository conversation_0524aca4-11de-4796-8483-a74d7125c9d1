<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.harry.modular.hmj.mapper.CarInfoMapper">

    <resultMap id="BaseResultMap" type="cn.harry.modular.hmj.domain.CarInfo">
            <id property="id" column="id" />
            <result property="plateNum" column="plate_num" />
            <result property="plateColor" column="plate_color" />
            <result property="cardNo" column="card_no" />
            <result property="parkNum" column="park_num" />
            <result property="cardTypeId" column="card_type_id" />
            <result property="carTypeId" column="car_type_id" />
            <result property="gname" column="gname" />
            <result property="pname" column="pname" />
            <result property="mobile" column="mobile" />
            <result property="addr" column="addr" />
            <result property="beginDate" column="begin_date" />
            <result property="endDate" column="end_date" />
            <result property="balance" column="balance" />
            <result property="authPgName" column="auth_pg_name" />
            <result property="lastInDate" column="last_in_date" />
            <result property="lastOutDate" column="last_out_date" />
            <result property="realName" column="real_name" />
            <result property="remark" column="remark" />
            <result property="ioState" column="io_state" />
            <result property="createDate" column="create_date" />
            <result property="lastUpdateDate" column="last_update_date" />
    </resultMap>

    <sql id="Base_Column_List">
        id,plate_num,plate_color,card_no,park_num,card_type_id,
        car_type_id,gname,pname,mobile,addr,
        begin_date,end_date,balance,auth_pg_name,last_in_date,
        last_out_date,real_name,remark,io_state,create_date,
        last_update_date
    </sql>
</mapper>
