<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.harry.modular.hmj.mapper.UserRegistrationMapper">

    <resultMap id="BaseResultMap" type="cn.harry.modular.hmj.domain.UserRegistration">
            <id property="id" column="id" />
            <result property="pname" column="pname" />
            <result property="mobile" column="mobile" />
            <result property="addr" column="addr" />
            <result property="ownerCertificate" column="owner_certificate" />
            <result property="plateNum" column="plate_num" />
            <result property="parkNum" column="park_num" />
            <result property="billingType" column="billing_type" />
            <result property="beginDate" column="begin_date" />
            <result property="endDate" column="end_date" />
            <result property="shouldPay" column="should_pay" />
            <result property="status" column="status" />
            <result property="createTime" column="create_time" />
            <result property="createBy" column="create_by" />
            <result property="modifyTime" column="modify_time" />
            <result property="modifyBy" column="modify_by" />
            <result property="valid" column="valid" />
    </resultMap>

    <sql id="Base_Column_List">
        id,pname,mobile,addr,owner_certificate,plate_num,
        park_num,billing_type,begin_date,end_date,should_pay,
        status,create_time,create_by,modify_time,modify_by,
        valid
    </sql>
</mapper>
