<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.harry.modular.hmj.mapper.OwnerMapper">

    <!-- 定义结果映射 -->
    <resultMap id="OwnerVOResultMap" type="cn.harry.modular.hmj.vo.OwnerVO">
        <id property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="nickName" column="nick_name"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="sex" column="sex"/>
        <result property="status" column="status"/>
        <result property="deptId" column="dept_id"/>
        <result property="createTime" column="create_time"/>
        <!-- 车位信息集合映射 -->
        <collection property="parkingList" ofType="cn.harry.modular.hmj.vo.OwnerVO$ParkingInfo">
            <id property="id" column="parking_id"/>
            <result property="parkName" column="park_name"/>
            <result property="poolName" column="pool_name"/>
            <result property="pgname" column="pgname"/>
            <result property="parkType" column="park_type"/>
            <result property="plateNums" column="plate_nums"/>
            <result property="emptyNum" column="empty_num"/>
            <result property="beginDateStr" column="begin_date_str"/>
            <result property="endDateStr" column="end_date_str"/>
        </collection>
    </resultMap>

    <select id="getPage" resultMap="OwnerVOResultMap">
        SELECT
            u.id,
            u.username,
            u.nick_name,
            u.phone,
            u.email,
            u.sex,
            u.status,
            u.dept_id,
            u.create_time,
            p.id AS parking_id,
            p.park_name,
            p.pool_name,
            p.pgname,
            p.park_type,
            p.plate_nums,
            p.empty_num,
            DATE_FORMAT(FROM_UNIXTIME(p.begin_date), '%Y-%m-%d') AS begin_date_str,
            DATE_FORMAT(FROM_UNIXTIME(p.end_date), '%Y-%m-%d') AS end_date_str
        FROM
            sys_user u
            LEFT JOIN t_pool_park p ON u.id = p.user_id
        WHERE
         u.valid = 1
        <if test="vo.username != null and vo.username.trim() neq ''">
            AND u.username LIKE CONCAT('%',#{vo.username},'%')
        </if>
        <if test="vo.status != null and vo.status.trim() neq ''">
            AND u.status = #{vo.status}
        </if>
        <if test="vo.phone != null and vo.phone.trim() neq ''">
            AND u.phone  LIKE CONCAT('%',#{vo.phone},'%')
        </if>
        ORDER BY u.id, p.id
    </select>
    <select id="getUserById" resultMap="OwnerVOResultMap">
        SELECT
            u.id,
            u.username,
            u.nick_name,
            u.phone,
            u.email,
            u.status,
            u.dept_id,
            u.create_time,
            p.id AS parking_id,
            p.park_name,
            p.pool_name,
            p.pgname,
            p.park_type,
            p.plate_nums,
            p.empty_num,
            DATE_FORMAT(FROM_UNIXTIME(p.begin_date), '%Y-%m-%d %H:%i:%s') AS begin_date_str,
            DATE_FORMAT(FROM_UNIXTIME(p.end_date), '%Y-%m-%d %H:%i:%s') AS end_date_str
        FROM
            sys_user u
            LEFT JOIN t_pool_park p ON u.id = p.user_id
        WHERE
            u.id = #{id}
            AND u.valid = 1
        ORDER BY p.id
    </select>

</mapper>
