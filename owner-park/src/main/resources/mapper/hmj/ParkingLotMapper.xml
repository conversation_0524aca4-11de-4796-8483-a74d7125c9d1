<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.harry.modular.hmj.mapper.ParkingLotMapper">

    <resultMap id="BaseResultMap" type="cn.harry.modular.hmj.domain.ParkingLot">
            <id property="id" column="id" />
            <result property="pname" column="pname" />
            <result property="parea" column="parea" />
            <result property="city" column="city" />
            <result property="address" column="address" />
            <result property="lng" column="lng" />
            <result property="lat" column="lat" />
            <result property="parkTotalCount" column="park_total_count" />
            <result property="parkEmptyCount" column="park_empty_count" />
            <result property="parkEmptyUpdateDate" column="park_empty_update_date" />
            <result property="isOnline" column="is_online" />
            <result property="outDelay" column="out_delay" />
            <result property="commKey" column="comm_key" />
    </resultMap>

    <sql id="Base_Column_List">
        id,pname,parea,city,address,lng,
        lat,park_total_count,park_empty_count,park_empty_update_date,is_online,
        out_delay,comm_key
    </sql>
</mapper>
