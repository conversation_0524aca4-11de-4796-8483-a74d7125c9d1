<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.harry.modular.hmj.mapper.PoolParkMapper">

    <resultMap id="BaseResultMap" type="cn.harry.modular.hmj.domain.PoolPark">
            <id property="id" column="id" />
            <result property="poolName" column="pool_name" />
            <result property="pgname" column="pgname" />
            <result property="parkName" column="park_name" />
            <result property="parkType" column="park_type" />
            <result property="beginDate" column="begin_date" />
            <result property="endDate" column="end_date" />
            <result property="createDate" column="create_date" />
            <result property="lastUpdateDate" column="last_update_date" />
    </resultMap>

    <sql id="Base_Column_List">
        id,pool_name,pgname,park_name,park_type,begin_date,
        end_date,create_date,last_update_date
    </sql>
</mapper>
