package ${packageName}.${moduleName}.${subpackageName};

import lombok.Getter;
import lombok.Setter;
#if(${hasLocalDateTime})
import java.time.LocalDateTime;
#end
#if(${hasBigDecimal})
import java.math.BigDecimal;
#end
import com.baomidou.mybatisplus.annotation.TableName;
import cn.harry.common.base.BaseEntity;
/**
 * $!{businessName}实体对象
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Getter
@Setter
@TableName("${tableName}")
public class ${entityName} extends BaseEntity {

    private static final long serialVersionUID = 1L;

#foreach($fieldConfig in ${fieldConfigs})
    #if(!$fieldConfig.fieldName.equals("id") && !$fieldConfig.fieldName.equals("createTime") && !$fieldConfig.fieldName.equals("createBy")&& !$fieldConfig.fieldName.equals("modifyTime")&& !$fieldConfig.fieldName.equals("modifyBy"))
        #if("$!fieldConfig.fieldComment" != "")
    /**
     * ${fieldConfig.fieldComment}
     */
        #end
    private ${fieldConfig.fieldType} ${fieldConfig.fieldName};
    #end
#end
}
