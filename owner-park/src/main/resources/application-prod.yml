spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************************************************************************
    username: hmj
    password: StkJazdS75MNf2CL
  cache:
    # 自定义是否启动字段 结合 ConditionalOnProperty 使用
    enabled: true
    # 缓存类型 redis、none(不使用缓存)
    type: redis
    # 缓存时间(单位：ms)
    redis:
      time-to-live: 3600000
      # 缓存null值，防止缓存穿透
      cache-null-values: true
  data:
    redis:
      host: 127.0.0.1 # Redis服务器地址
      database: 1 # Redis数据库索引（默认为0）
      port: 6379 # Redis服务器连接端口
      password: # Redis服务器连接密码（默认为空）
  # 邮件配置
  mail:
    host: smtp.qq.com # 邮件服务器配置
    port: 587
    username: <EMAIL> # 发件人邮箱
    password: 123456 # 这里配置的是授权码
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
    # 邮件发送者
    from: <EMAIL>

#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: cn.harry.*.domain
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: none
      # 逻辑删除全局属性名(驼峰和下划线都支持)
      logic-delete-field: valid
      logic-delete-value: 0 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 1 # 逻辑未删除值(默认为 0)
    banner: false
  #原生配置
  configuration:
    # 驼峰下划线转换
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# springdoc配置： https://springdoc.org/properties.html
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: alpha
    tags-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: '认证中心'
      paths-to-match: "/**"
      packages-to-scan:
        - cn.harry.modular.auth.controller
    - group: '系统管理'
      paths-to-match: "/**"
      packages-to-scan:
        - cn.harry.modular.system.controller
  default-flat-param-object: true

# 文件存储配置
oss:
  # OSS 类型 (目前支持aliyun、minio)
  type: aliyun
  # MinIO 对象存储服务
  minio:
    # 服务Endpoint
    endpoint: http://minio.tech-harry.cn:19000
    # 访问凭据
    access-key: JJIpvdYEbAXhlzfC6a1q
    # 凭据密钥
    secret-key: EJn8xjywn0BI3KnOW5okGxbkpJGSkC6aN9AF1Dn1
    # 存储桶名称
    bucket-name: harry
    # (可选)自定义域名，如果配置了域名，生成的文件URL是域名格式，未配置则URL则是IP格式
    custom-domain: https://minio.tech-harry.cn
  # 阿里云OSS对象存储服务
  aliyun:
    # 服务Endpoint
    endpoint: oss-cn-hangzhou.aliyuncs.com
    # 访问凭据
    access-key-id: LTAI5tETaKuMNR3zZ63oKjta
    # 凭据密钥
    access-key-secret: ******************************
    # 存储桶名称
    bucket-name: bhlinka

sms:
  aliyun:
    enable: false  #如果active为false时 则不发送短信
    accessKeyId: XXX
    accessKeySecret: XXX
    regionId: cn-shanghai
    signName: Harry技术
    templateCodes:
      # 注册（预留）
      register: SMS_xxx
      # 登录（预留）
      login: SMS_xxx
      # 修改密码
      changePassword: SMS_xxx

# knife4j 接口文档配置
knife4j:
  # 是否开启 Knife4j 增强功能
  enable: true  # 设置为 true 表示开启增强功能
  # 生产环境配置
  production: false  # 设置为 true 表示在生产环境中不显示文档，为 false 表示显示文档（通常在开发环境中使用）
  setting:
    language: zh_cn

# 验证码配置
captcha:
  # 验证码类型 circle-圆圈干扰验证码|gif-Gif验证码|line-干扰线验证码|shear-扭曲干扰验证码
  type: circle
  # 验证码宽度
  width: 130
  # 验证码高度
  height: 48
  # 验证码干扰元素个数
  interfere-count: 2
  # 文本透明度(0.0-1.0)
  text-alpha: 0.8
  # 验证码字符配置
  code:
    # 验证码字符类型 math-算术|random-随机字符
    type: math
    # 验证码字符长度，type=算术时，表示运算位数(1:个位数运算 2:十位数运算)；type=随机字符时，表示字符个数
    length: 1
  # 验证码字体
  font:
    # 字体名称 Dialog|DialogInput|Monospaced|Serif|SansSerif
    name: SansSerif
    # 字体样式 0-普通|1-粗体|2-斜体
    weight: 1
    # 字体大小
    size: 20
  # 验证码有效期(秒)
  expire-seconds: 120

wx:
  miniapp:
    appid: wxead3e1f683bc1865 #微信小程序的appid
    secret: 82d5f487303d6401e88464f537e691f5 #微信小程序的Secret
    token: #微信小程序消息服务器配置的token
    aesKey: #微信小程序消息服务器配置的EncodingAESKey
    msgDataFormat: JSON
  pay:
    appId: wx3f92e56506b1f80c #微信公众号或者小程序等的appid
    mchId: 1622289613 #微信支付商户号
    mchKey: gqxRExKlLGF6oBfuTEycdGZfKbd8pDyU #微信支付商户密钥
    subAppId: wxead3e1f683bc1865 #服务商模式下的子商户公众账号ID（默认）
    subMchId: 1721255719 #服务商模式下的子商户号（默认）
    keyPath: classpath:/wxpaycrt/1622289613_20220303_cert/apiclient_cert.p12 # p12证书的位置，可以指定绝对路径，也可以指定类路径（以classpath:开头）
    privateKeyPath: classpath:/wxpaycrt/1622289613_20220303_cert/apiclient_key.pem
    privateCertPath: classpath:/wxpaycrt/1622289613_20220303_cert/apiclient_cert.pem
    apiV3Key: DNECKcaL7QxloKhpPVfQesMEJLvfIRqI
    notifyDomain: https://hmj.bhlinka.com/prod-api/

# 日志配置
logging:
  config: classpath:logback-spring.xml
  file:
    path: ../logs/owner-park
  level:
    root: info
    cn.harry: debug
