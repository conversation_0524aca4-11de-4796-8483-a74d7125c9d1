spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************************************************
    username: root
    password: root
  cache:
    # 自定义是否启动字段 结合 ConditionalOnProperty 使用
    enabled: true
    # 缓存类型 redis、none(不使用缓存)
    type: redis
    # 缓存时间(单位：ms)
    redis:
      time-to-live: 3600000
      # 缓存null值，防止缓存穿透
      cache-null-values: true
  data:
    redis:
      host: 127.0.0.1 # Redis服务器地址
      database: 0 # Redis数据库索引（默认为0）
      port: 6379 # Redis服务器连接端口
      password: # Redis服务器连接密码（默认为空）
  # 邮件配置
  mail:
    host: smtp.qq.com # 邮件服务器配置
    port: 587
    username: <EMAIL> # 发件人邮箱
    password: 123456 # 这里配置的是授权码
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
    # 邮件发送者
    from: <EMAIL>

# 文件存储配置
oss:
  # OSS 类型 (目前支持aliyun、minio)
  type: minio
  # MinIO 对象存储服务
  minio:
    # 服务Endpoint
    endpoint: http://minio.tech-harry.cn:19000
    # 访问凭据
    access-key: JJIpvdYEbAXhlzfC6a1q
    # 凭据密钥
    secret-key: EJn8xjywn0BI3KnOW5okGxbkpJGSkC6aN9AF1Dn1
    # 存储桶名称
    bucket-name: harry
    # (可选)自定义域名，如果配置了域名，生成的文件URL是域名格式，未配置则URL则是IP格式
    custom-domain: https://minio.tech-harry.cn
  # 阿里云OSS对象存储服务
  aliyun:
    # 服务Endpoint
    endpoint: oss-cn-hangzhou.aliyuncs.com
    # 访问凭据
    access-key-id: LTAI5tETaKuMNR3zZ63oKjta
    # 凭据密钥
    access-key-secret: ******************************
    # 存储桶名称
    bucket-name: bhlinka

sms:
  aliyun:
    enable: false  #如果active为false时 则不发送短信
    accessKeyId: XXX
    accessKeySecret: XXX
    regionId: cn-shanghai
    signName: Harry技术
    templateCodes:
      # 注册（预留）
      register: SMS_xxx
      # 登录（预留）
      login: SMS_xxx
      # 修改密码
      changePassword: SMS_xxx

wx:
  miniapp:
    appid: wxead3e1f683bc1865 #微信小程序的appid
    secret: 82d5f487303d6401e88464f537e691f5 #微信小程序的Secret
    token: #微信小程序消息服务器配置的token
    aesKey: #微信小程序消息服务器配置的EncodingAESKey
    msgDataFormat: JSON
  pay:
    appId: wx3f92e56506b1f80c #微信公众号或者小程序等的appid
    mchId: 1622289613 #微信支付商户号
    mchKey: gqxRExKlLGF6oBfuTEycdGZfKbd8pDyU #微信支付商户密钥
    subAppId: wxead3e1f683bc1865 #服务商模式下的子商户公众账号ID（默认）
    subMchId: 1721255719 #服务商模式下的子商户号（默认）
    keyPath: classpath:/wxpaycrt/1622289613_20220303_cert/apiclient_cert.p12 # p12证书的位置，可以指定绝对路径，也可以指定类路径（以classpath:开头）
    privateKeyPath: classpath:/wxpaycrt/1622289613_20220303_cert/apiclient_key.pem
    privateCertPath: classpath:/wxpaycrt/1622289613_20220303_cert/apiclient_cert.pem
    apiV3Key: DNECKcaL7QxloKhpPVfQesMEJLvfIRqI
    notifyDomain: https://kunliangapi.tech-harry.cn


# knife4j 接口文档配置
knife4j:
  # 是否开启 Knife4j 增强功能
  enable: true  # 设置为 true 表示开启增强功能
  # 生产环境配置
  production: false  # 设置为 true 表示在生产环境中不显示文档，为 false 表示显示文档（通常在开发环境中使用）
  setting:
    language: zh_cn

# 日志配置
logging:
  config: classpath:logback-spring.xml
  file:
    path: ../logs/harry
  level:
    root: info
    cn.harry: debug
