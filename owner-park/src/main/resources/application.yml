server:
  port: 9998

spring:
  profiles:
    active: dev # 环境 dev test prod

#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: cn.harry.*.domain
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: none
      # 逻辑删除全局属性名(驼峰和下划线都支持)
      logic-delete-field: valid
      logic-delete-value: 0 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 1 # 逻辑未删除值(默认为 0)
    banner: false
  #原生配置
  configuration:
    # 驼峰下划线转换
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 安全配置
security:
  jwt:
    # JWT 秘钥
    key: www.tech-harry.cn
    # JWT 有效期(单位：秒)
    ttl: 604800
  # 白名单列表
  ignore-urls:
    - /v3/api-docs/**
    - /doc.html
    - /swagger-resources/**
    - /webjars/**
    - /swagger-ui/**
    - /swagger-ui.html
    - /auth/login
    - /auth/captcha
    - /ws/**
    - /deliyun/**
    - /auth/miniapp/*/login
    - /sys/dict/data/*/options
    - /applet/wxpay/notify/order

# springdoc配置： https://springdoc.org/properties.html
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: alpha
    tags-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: '认证中心'
      paths-to-match: "/**"
      packages-to-scan:
        - cn.harry.modular.auth.controller
    - group: '系统管理'
      paths-to-match: "/**"
      packages-to-scan:
        - cn.harry.modular.system.controller
    - group: '代码生成'
      paths-to-match: "/**"
      packages-to-scan:
        - cn.harry.modular.codegen.controller
    - group: '文件管理'
      paths-to-match: "/**"
      packages-to-scan:
        - cn.harry.modular.oss.controller
    - group: '德立云API'
      paths-to-match: "/**"
      packages-to-scan:
          - cn.harry.modular.deliyun.controller
  default-flat-param-object: true

# knife4j 接口文档配置
knife4j:
  # 是否开启 Knife4j 增强功能
  enable: true  # 设置为 true 表示开启增强功能
  # 生产环境配置
  production: false  # 设置为 true 表示在生产环境中不显示文档，为 false 表示显示文档（通常在开发环境中使用）
  setting:
    language: zh_cn

# 验证码配置
captcha:
  # 验证码类型 circle-圆圈干扰验证码|gif-Gif验证码|line-干扰线验证码|shear-扭曲干扰验证码
  type: circle
  # 验证码宽度
  width: 130
  # 验证码高度
  height: 48
  # 验证码干扰元素个数
  interfere-count: 2
  # 文本透明度(0.0-1.0)
  text-alpha: 0.8
  # 验证码字符配置
  code:
    # 验证码字符类型 math-算术|random-随机字符
    type: math
    # 验证码字符长度，type=算术时，表示运算位数(1:个位数运算 2:十位数运算)；type=随机字符时，表示字符个数
    length: 1
  # 验证码字体
  font:
    # 字体名称 Dialog|DialogInput|Monospaced|Serif|SansSerif
    name: SansSerif
    # 字体样式 0-普通|1-粗体|2-斜体
    weight: 1
    # 字体大小
    size: 20
  # 验证码有效期(秒)
  expire-seconds: 120

# 日志配置
logging:
  config: classpath:logback-spring.xml
  file:
    path: ../logs/owner-park
  level:
    root: info
    cn.harry: debug

# 代码生成器配置
codegen:
  # 下载代码文件名称
  downloadFileName: harry-code
  # 后端项目名称
  backendAppName: harry
  # 前端项目名称
  frontendAppName: harry-vue
  # 默认配置
  defaultConfig:
    author: harry
    moduleName: system
  # 排除数据表
  excludeTables:
    - gen_config
    - gen_field_config
  ## 模板配置
  templateConfigs:
    API:
      templatePath: codegen/api.ts.vm
      subpackageName: api
      extension: .ts
    VIEW:
      templatePath: codegen/index.vue.vm
      subpackageName: views
      extension: .vue
    Controller:
      templatePath: codegen/controller.java.vm
      subpackageName: controller
    Service:
      templatePath: codegen/service.java.vm
      subpackageName: service
    ServiceImpl:
      templatePath: codegen/serviceImpl.java.vm
      subpackageName: service.impl
    Mapper:
      templatePath: codegen/mapper.java.vm
      subpackageName: mapper
    MapperXml:
      templatePath: codegen/mapper.xml.vm
      subpackageName: mapper
      extension: .xml
    Entity:
      templatePath: codegen/entity.java.vm
      subpackageName: domain

