package cn.harry.common.utils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * Redis分布式锁工具类
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RedisLockUtil {

    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 分布式锁前缀
     */
    private static final String LOCK_PREFIX = "lock:";

    /**
     * 默认锁过期时间（秒）
     */
    private static final long DEFAULT_EXPIRE_TIME = 300; // 5分钟

    /**
     * 释放锁的Lua脚本
     */
    private static final String UNLOCK_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "    return redis.call('del', KEYS[1]) " +
        "else " +
        "    return 0 " +
        "end";

    /**
     * 尝试获取分布式锁
     *
     * @param lockKey    锁的key
     * @param lockValue  锁的value（用于释放锁时验证）
     * @param expireTime 锁的过期时间（秒）
     * @return 是否获取成功
     */
    public boolean tryLock(String lockKey, String lockValue, long expireTime) {
        try {
            String key = LOCK_PREFIX + lockKey;
            Boolean result = redisTemplate.opsForValue().setIfAbsent(key, lockValue, expireTime, TimeUnit.SECONDS);
            if (Boolean.TRUE.equals(result)) {
                log.debug("获取分布式锁成功，lockKey: {}, lockValue: {}", lockKey, lockValue);
                return true;
            } else {
                log.debug("获取分布式锁失败，lockKey: {}, lockValue: {}", lockKey, lockValue);
                return false;
            }
        } catch (Exception e) {
            log.error("获取分布式锁异常，lockKey: {}, lockValue: {}", lockKey, lockValue, e);
            return false;
        }
    }

    /**
     * 尝试获取分布式锁（使用默认过期时间）
     *
     * @param lockKey   锁的key
     * @param lockValue 锁的value
     * @return 是否获取成功
     */
    public boolean tryLock(String lockKey, String lockValue) {
        return tryLock(lockKey, lockValue, DEFAULT_EXPIRE_TIME);
    }

    /**
     * 释放分布式锁
     *
     * @param lockKey   锁的key
     * @param lockValue 锁的value
     * @return 是否释放成功
     */
    public boolean unlock(String lockKey, String lockValue) {
        try {
            String key = LOCK_PREFIX + lockKey;
            DefaultRedisScript<Long> script = new DefaultRedisScript<>();
            script.setScriptText(UNLOCK_SCRIPT);
            script.setResultType(Long.class);
            
            Long result = redisTemplate.execute(script, Collections.singletonList(key), lockValue);
            if (result != null && result == 1L) {
                log.debug("释放分布式锁成功，lockKey: {}, lockValue: {}", lockKey, lockValue);
                return true;
            } else {
                log.debug("释放分布式锁失败，lockKey: {}, lockValue: {}", lockKey, lockValue);
                return false;
            }
        } catch (Exception e) {
            log.error("释放分布式锁异常，lockKey: {}, lockValue: {}", lockKey, lockValue, e);
            return false;
        }
    }

    /**
     * 检查锁是否存在
     *
     * @param lockKey 锁的key
     * @return 锁是否存在
     */
    public boolean isLocked(String lockKey) {
        try {
            String key = LOCK_PREFIX + lockKey;
            return Boolean.TRUE.equals(redisTemplate.hasKey(key));
        } catch (Exception e) {
            log.error("检查分布式锁状态异常，lockKey: {}", lockKey, e);
            return false;
        }
    }

    /**
     * 强制释放锁（不验证lockValue）
     *
     * @param lockKey 锁的key
     * @return 是否释放成功
     */
    public boolean forceUnlock(String lockKey) {
        try {
            String key = LOCK_PREFIX + lockKey;
            Boolean result = redisTemplate.delete(key);
            log.debug("强制释放分布式锁，lockKey: {}, result: {}", lockKey, result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("强制释放分布式锁异常，lockKey: {}", lockKey, e);
            return false;
        }
    }
}
