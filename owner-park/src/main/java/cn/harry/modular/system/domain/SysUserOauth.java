package cn.harry.modular.system.domain;

import cn.harry.common.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 第三方登陆授权信息
 * @TableName sys_user_oauth
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="sys_user_oauth")
@Data
public class SysUserOauth extends BaseEntity {


    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 授权来源 1 微信小程序 2 微信公众号
     */
    private String oauthType;

    /**
     * appid
     */
    private String appid;

    /**
     * openId
     */
    private String openid;

    /**
     * unionId
     */
    private String unionId;

}