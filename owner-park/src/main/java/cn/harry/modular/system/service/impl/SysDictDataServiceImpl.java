package cn.harry.modular.system.service.impl;

import cn.harry.modular.system.enums.StatusEnums;
import cn.harry.modular.system.vo.DictItemOptionVO;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.harry.modular.system.domain.SysDictData;
import cn.harry.modular.system.service.SysDictDataService;
import cn.harry.modular.system.mapper.SysDictDataMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @公众号 Harry技术
*/
@Service
public class SysDictDataServiceImpl extends ServiceImpl<SysDictDataMapper, SysDictData>
    implements SysDictDataService{

    @Override
    public List<DictItemOptionVO> getDictItems(String dictType) {
        List<SysDictData> list = this.list(Wrappers.<SysDictData>lambdaQuery().eq(SysDictData::getDictType, dictType).eq(SysDictData::getStatus, StatusEnums.ENABLE.getKey()));
        if (CollUtil.isNotEmpty(list)) {
            return list.stream().map(item -> {
                DictItemOptionVO dictItemOptionVO = new DictItemOptionVO();
                dictItemOptionVO.setValue(item.getDictValue());
                dictItemOptionVO.setLabel(item.getDictLabel());
                dictItemOptionVO.setTagType(item.getListClass());
                return dictItemOptionVO;
            }).toList();
        }
        return List.of();
    }
}




