package cn.harry.modular.system.utils;

import cn.harry.common.utils.SpringUtils;
import cn.harry.modular.system.service.SysConfigService;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import lombok.experimental.UtilityClass;

/**
 * 系统参数配置解析器
 *
 * <AUTHOR>
 */
@UtilityClass
public class ParamResolver {

    /**
     * 根据key 查询value 配置
     *
     * @param key        key
     * @param defaultVal 默认值
     * @return value
     */
    public Long getLong(String key, Long... defaultVal) {
        return checkAndGet(key, Long.class, defaultVal);
    }

    /**
     * 根据key 查询value 配置
     *
     * @param key        key
     * @param defaultVal 默认值
     * @return value
     */
    public String getStr(String key, String... defaultVal) {
        return checkAndGet(key, String.class, defaultVal);
    }

    @SafeVarargs
    private <T> T checkAndGet(String key, Class<T> clazz, T... defaultVal) {
        // 校验入参是否合法
        if (StrUtil.isBlank(key) || defaultVal.length > 1) {
            throw new IllegalArgumentException("参数不合法");
        }

        SysConfigService configService = SpringUtils.getBean(SysConfigService.class);

        String result = configService.getKeyToValue(key);

        if (StrUtil.isNotBlank(result)) {
            return Convert.convert(clazz, result);
        }

        if (defaultVal.length == 1) {
            return Convert.convert(clazz, defaultVal.clone()[0]);

        }
        return null;
    }

}
