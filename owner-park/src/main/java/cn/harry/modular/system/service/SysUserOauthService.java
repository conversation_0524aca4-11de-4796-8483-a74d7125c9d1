package cn.harry.modular.system.service;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.harry.modular.system.domain.SysUserOauth;
import cn.harry.modular.wx.miniapp.vo.AuthResult;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 * @description 针对表【sys_user_oauth(第三方登陆授权信息)】的数据库操作Service
 * @createDate 2025-04-30 15:05:52
 */
public interface SysUserOauthService extends IService<SysUserOauth> {

    /**
     * 获取授权结果
     *
     * @param appid   小程序appid
     * @param session
     * @param type
     * @return
     */
    AuthResult getAuthResult(String appid, WxMaJscode2SessionResult session, String type);

    /**
     * 绑定openid
     *
     * @param userId
     * @param openid
     */
    void grantBind(Long userId, String openid);

    /**
     * 绑定openid
     *
     * @param appid
     * @param openid
     * @param userId
     * @return
     */
    Boolean bindOpenid(String appid, String openid, Long userId);
}
