package cn.harry.modular.system.service.impl;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.harry.component.security.constant.SecurityConstants;
import cn.harry.component.security.model.SysUserDetails;
import cn.harry.component.security.utils.JwtUtils;
import cn.harry.config.property.SecurityProperties;
import cn.harry.modular.system.domain.SysUser;
import cn.harry.modular.system.domain.SysUserOauth;
import cn.harry.modular.system.mapper.SysUserMapper;
import cn.harry.modular.system.mapper.SysUserOauthMapper;
import cn.harry.modular.system.param.SysRegisterParam;
import cn.harry.modular.system.service.SysUserOauthService;
import cn.harry.modular.system.service.SysUserService;
import cn.harry.modular.wx.miniapp.enums.OauthTypeEnums;
import cn.harry.modular.wx.miniapp.vo.AuthResult;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 针对表【sys_user_oauth(第三方登陆授权信息)】的数据库操作Service实现
 * @createDate 2025-04-30 15:05:52
 */
@Slf4j
@Service
@AllArgsConstructor
public class SysUserOauthServiceImpl extends ServiceImpl<SysUserOauthMapper, SysUserOauth> implements SysUserOauthService {

    private final UserDetailsService userDetailsService;
    private final SecurityProperties securityProperties;
    private final SysUserMapper sysUserMapper;
    private final SysUserService sysUserService;

    @Override
    public AuthResult getAuthResult(String appid, WxMaJscode2SessionResult session, String type) {
        AuthResult res = new AuthResult();
        Long userId = saveOpenId(appid, session, type);
        if (userId == null) {
            return res;
        }

        String accessToken;
        Long expiration = securityProperties.getJwt().getTtl();
        // 密码需要客户端加密后传递
        try {
            SysUser sysUser = sysUserMapper.selectById(userId);
            String username = sysUser.getUsername();
            SysUserDetails userDetails = (SysUserDetails) this.userDetailsService.loadUserByUsername(username);

            // 认证成功后生成JWT令牌
            accessToken = JwtUtils.createTokenUser(userDetails);
            res.setToken(accessToken);
            res.setExpiration(expiration);
            res.setTokenType(StrUtil.trim(SecurityConstants.JWT_TOKEN_PREFIX));
            return res;
        } catch (Exception e) {
            log.error("登录异常:{}", e);
        }
        return res;
    }

    @Override
    public void grantBind(Long userId, String openid) {
        SysUserOauth userOauth = getByOpenid(openid);
        if (userOauth != null) {
            userOauth.setUserId(userId);
            updateById(userOauth);
        }
    }

    @Override
    public Boolean bindOpenid(String appid, String openid, Long userId) {
        SysUserOauth userOauth = getByOpenid(openid);
        if (userOauth != null) {
            userOauth.setUserId(userId);
        } else {
            userOauth = new SysUserOauth();
            userOauth.setAppid(appid);
            userOauth.setOpenid(openid);
            userOauth.setUserId(userId);
            userOauth.setOauthType(OauthTypeEnums.WX_XCX.getType());
        }
        return saveOrUpdate(userOauth);
    }

    private Long saveOpenId(String appid, WxMaJscode2SessionResult session, String type) {
        SysUserOauth userOauth = getByOpenid(session.getOpenid());
        if (userOauth == null) {
            userOauth = new SysUserOauth();
        }
        userOauth.setAppid(appid);
        userOauth.setOpenid(session.getOpenid());
        userOauth.setUnionId(session.getUnionid());
        userOauth.setOauthType(type);
        if (userOauth.getUserId() == null) {
            Long userid = createGuestUser(appid, session, type);
            userOauth.setUserId(userid);
        }
        return saveOrUpdate(userOauth) ? userOauth.getUserId() : null;
    }

    private SysUserOauth getByOpenid(String openid) {
        return getOne(Wrappers.<SysUserOauth>lambdaQuery().eq(SysUserOauth::getOpenid, openid));
    }

    /**
     * 创建游客用户
     *
     * @param appid   小程序appid
     * @param session 微信登录会话信息
     * @param type    OAuth类型
     * @return 用户ID
     */
    private Long createGuestUser(String appid, WxMaJscode2SessionResult session, String type) {
        try {
            // 生成当前时间字符串（精确到秒）
            String currentTimeStr = DateUtil.format(new Date(), "MMddHHmmss");

            // 生成随机8位中英文字符密码
            String randomPassword = RandomUtil.randomString(8);

            // 创建新用户
            SysRegisterParam newUser = new SysRegisterParam();
            newUser.setUsername("u" + currentTimeStr);
            newUser.setPassword(randomPassword);
            newUser.setNickName("游客" + currentTimeStr);

            // 保存用户
            SysUser userCreated = sysUserService.register(newUser);
            if (userCreated == null) {
                log.error("创建游客用户失败");
                return null;
            }

            log.info("成功创建游客用户，用户名：{}，用户ID：{}", newUser.getUsername(), userCreated.getId());
            return userCreated.getId();

        } catch (Exception e) {
            log.error("创建游客用户异常：{}", e.getMessage(), e);
            return null;
        }
    }
}




