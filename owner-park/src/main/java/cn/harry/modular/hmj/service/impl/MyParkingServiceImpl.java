package cn.harry.modular.hmj.service.impl;

import cn.harry.common.exception.ApiException;
import cn.harry.component.security.utils.SecurityUtils;
import cn.harry.modular.deliyun.req.DeleteCarRequest;
import cn.harry.modular.deliyun.req.SaveParkingSpaceCarRequest;
import cn.harry.modular.deliyun.service.DeliyunService;
import cn.harry.modular.hmj.domain.CarInfo;
import cn.harry.modular.hmj.domain.ParkingLot;
import cn.harry.modular.hmj.domain.PoolPark;
import cn.harry.modular.hmj.param.AddCarRequest;
import cn.harry.modular.hmj.param.DelCarRequest;
import cn.harry.modular.hmj.service.CarInfoService;
import cn.harry.modular.hmj.service.MyParkingService;
import cn.harry.modular.hmj.service.ParkingLotService;
import cn.harry.modular.hmj.service.PoolParkService;
import cn.harry.modular.hmj.utils.HmjUtils;
import cn.harry.modular.hmj.vo.MyParkingVO;
import cn.harry.modular.system.domain.SysUser;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 我的车位服务实现
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MyParkingServiceImpl implements MyParkingService {

    private final PoolParkService poolParkService;
    private final CarInfoService carInfoService;
    private final DeliyunService deliyunService;
    private final ParkingLotService parkingLotService;


    @Override
    public List<MyParkingVO> getMyParkingList() {
        Long userId = SecurityUtils.getUserId();
        if (userId == null) {
            throw new ApiException("用户未登录");
        }
        List<PoolPark> list = poolParkService.list(Wrappers.<PoolPark>lambdaQuery().eq(PoolPark::getUserId, userId));
        if (list != null && !list.isEmpty()) {
            return list.stream().map(this::coverToMyParkingVO).toList();
        }

        return List.of();
    }

    private MyParkingVO coverToMyParkingVO(PoolPark item) {
        MyParkingVO vo = new MyParkingVO();
        BeanUtil.copyProperties(item, vo);

        // 将时间戳转换为日期字符串 yyyy-MM-dd
        vo.setBeginDateStr(HmjUtils.timestamp2Date(item.getBeginDate()));
        vo.setEndDateStr(HmjUtils.timestamp2Date(item.getEndDate()));

        Integer expireStatus = HmjUtils.funExpiryStatus(item.getEndDate());
        vo.setExpireStatus(expireStatus);

        Long garageId = item.getGarageId();
        ParkingLot lot = parkingLotService.getById(garageId);
        if (lot != null) {
            vo.setGarageName(lot.getPname());
        }

        List<CarInfo> cars = new ArrayList<>();
        String plateNums = item.getPlateNums();
        if (plateNums != null && !plateNums.trim().isEmpty()) {
            String[] plateNumArray = plateNums.split(",");
            for (String plateNum : plateNumArray) {
                CarInfo carInfo = new CarInfo();
                CarInfo car = carInfoService.getByGarageIdAndPlateNum(lot.getId(), plateNum);
                if (car != null) {
                    BeanUtil.copyProperties(car, carInfo);
                }
                cars.add(carInfo);
            }
            vo.setCars(cars);
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCarToParking(AddCarRequest request) {
        SysUser user = SecurityUtils.getSysUser();
        if (user.getId() == null) {
            throw new ApiException("用户未登录");
        }

        // 验证车位是否属于当前用户
        PoolPark parking = poolParkService.getById(request.getParkingId());
        if (parking == null) {
            throw new ApiException("车位不存在");
        }
        if (!user.getId().equals(parking.getUserId())) {
            throw new ApiException("该车位不属于您");
        }

        // 检查车牌号是否已存在
        CarInfo existingCar = carInfoService.getByGarageIdAndPlateNum(parking.getGarageId(), request.getPlateNum());
        if (existingCar != null) {
            throw new ApiException("该车牌号已存在");
        }

        // 创建车辆记录
        CarInfo carInfo = new CarInfo();
        carInfo.setPlateNum(request.getPlateNum());
        carInfo.setCarTypeId(request.getCarTypeId());
        carInfo.setPname(user.getNickName()); // 使用pname字段存储联系电话

        carInfo.setParkNum(parking.getParkName());

        boolean result = carInfoService.save(carInfo);
        if (!result) {
            throw new ApiException("添加车辆失败");
        }

        // 更新车位的车牌号列表
        String currentPlateNums = parking.getPlateNums();
        String newPlateNums;
        if (currentPlateNums == null || currentPlateNums.trim().isEmpty()) {
            newPlateNums = request.getPlateNum();
        } else {
            newPlateNums = currentPlateNums + "," + request.getPlateNum();
        }
        parking.setPlateNums(newPlateNums);
        poolParkService.updateById(parking);

        log.info("用户 {} 在车位 {} 添加车辆 {}", user.getId(), request.getParkingId(), request.getPlateNum());

        // TODO  需要考虑调用德立云接口
        // 参数封装
        saveParameterEncapsulation(parking);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCarInfo(DelCarRequest request) {
        Long userId = SecurityUtils.getUserId();

        if (userId == null) {
            throw new ApiException("用户未登录");
        }
        Long carId = request.getCarId();
        Long parkingId = request.getParkingId();

        // 验证车辆是否存在
        CarInfo carInfo = carInfoService.getById(carId);
        if (carInfo == null) {
            throw new ApiException("车辆不存在");
        }

        // 从所有车位中移除该车牌号
        String plateNum = carInfo.getPlateNum();
        List<PoolPark> userParkings = poolParkService.list(
                Wrappers.<PoolPark>lambdaQuery()
                        .eq(PoolPark::getGarageId,carInfo.getGarageId())
                        .eq(!SecurityUtils.isRoot(), PoolPark::getUserId, userId)
                        .like(PoolPark::getPlateNums, plateNum)
        );

        for (PoolPark parking : userParkings) {
            String plateNums = parking.getPlateNums();
            if (plateNums != null && plateNums.contains(plateNum)) {
                // 移除车牌号
                String[] plateArray = plateNums.split(",");
                List<String> plateList = new ArrayList<>();
                for (String plate : plateArray) {
                    if (!plate.trim().equals(plateNum)) {
                        plateList.add(plate.trim());
                    }
                }
                String newPlateNums = plateList.isEmpty() ? null : String.join(",", plateList);
                parking.setPlateNums(newPlateNums);

                poolParkService.update(Wrappers.<PoolPark>lambdaUpdate()
                        .eq(PoolPark::getId, parking.getId())
                        .set(PoolPark::getPlateNums, newPlateNums)
                );

                poolParkService.updateById(parking);
            }
        }

        // 删除车辆记录
        boolean result = carInfoService.removeById(carId);
        if (!result) {
            throw new ApiException("删除车辆失败");
        }

        log.info("用户 {} 删除车辆 {} (ID: {})", userId, plateNum, carId);
        deleteParameterEncapsulation(plateNum);
    }

    /**
     * 删除车辆封装参数
     *
     * @param plateNum 车牌号
     */
    private void deleteParameterEncapsulation(String plateNum) {
        DeleteCarRequest request = new DeleteCarRequest();
        request.setPlateNum(plateNum);
        log.error("调用德立云接口参数:{}", JSONUtil.toJsonStr(request));
        deliyunService.deleteCar(request);
    }


    private void saveParameterEncapsulation(PoolPark parking) {

        if (parking.getGarageId() == null) {
            log.error("车库ID为空");
            return;
        }
        ParkingLot parkingLot = parkingLotService.getById(parking.getGarageId());
        if (parkingLot == null) {
            log.error("车库不存在");
            return;
        }
        String commKey = parkingLot.getCommKey();
        if (StrUtil.isBlank(commKey)) {
            log.error("车库项目唯一编码为空");
            return;
        }


        String poolName = parking.getPoolName();
        Byte chargeType = parking.getChargeType(); // 车位池满时收费方式(1:先出收费, 2:后进收费)（必填）
        Byte fullMode = parking.getFullMode(); // 所属车库名称(如果 fullMode 为 1 时必填, 为 2 时可以不填)
        String pgname = parking.getPgname();
        Byte cpType = 0; // 车位池类型(0:默认类型, 1:子母车位)

        SaveParkingSpaceCarRequest request = new SaveParkingSpaceCarRequest();
        request.setPoolName(poolName);
        request.setChargeType(chargeType);
        request.setFullMode(fullMode);
        request.setPgname(pgname);
        request.setCpType(cpType);

        List<CarInfo> carList = carInfoService.list(Wrappers.<CarInfo>lambdaQuery().eq(CarInfo::getParkNum, poolName));

        List<SaveParkingSpaceCarRequest.CarInfo> carRequestList = new ArrayList<>();
        for (CarInfo carInfo : carList) {
            SaveParkingSpaceCarRequest.CarInfo car = new SaveParkingSpaceCarRequest.CarInfo();
            car.setPlateNum(carInfo.getPlateNum());
            car.setCarTypeId(carInfo.getCarTypeId());
            car.setPname(carInfo.getPname());
            carRequestList.add(car);
        }
        request.setCarList(carRequestList);

        List<SaveParkingSpaceCarRequest.ParkingSpace> parkList = new ArrayList<>();
        SaveParkingSpaceCarRequest.ParkingSpace parkingSpace = new SaveParkingSpaceCarRequest.ParkingSpace();
        parkingSpace.setParkName(parking.getParkName());
        parkingSpace.setParkType((byte) 2);
        parkingSpace.setBeginDate(parking.getBeginDate());
        parkingSpace.setEndDate(parking.getEndDate());
        parkingSpace.setChargeMoney(0.0);
        parkList.add(parkingSpace);
        request.setParkList(parkList);
        // TODO  调用德立云接口
        log.error("调用德立云接口参数:{}", JSONUtil.toJsonStr(request));
        deliyunService.saveParkingSpaceCar(request, commKey);
    }

}
