package cn.harry.modular.hmj.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class CalculateFeeVO {

    // 类型
    @Schema(description = "类型")
    private String billingType;
    // 开始时间
    @Schema(description = "开始时间")
    private String startTime;
    // 结束时间
    @Schema(description = "结束时间")
    private String endTime;
    // 选择数
    @Schema(description = "选择数")
    private Integer periodCount;
    /**
     * 应缴费用
     */
    @Schema(description = "应缴费用")
    private Double shouldPay;
}
