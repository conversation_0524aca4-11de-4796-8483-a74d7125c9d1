package cn.harry.modular.hmj.controller;

import cn.harry.common.annotation.SysLog;
import cn.harry.common.api.R;
import cn.harry.common.enums.BusinessType;
import cn.harry.modular.hmj.param.AddCarRequest;
import cn.harry.modular.hmj.param.DelCarRequest;
import cn.harry.modular.hmj.service.MyParkingService;
import cn.harry.modular.hmj.vo.MyParkingVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 我的车位管理控制器
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Slf4j
@Tag(name = "我的车位管理")
@RestController
@RequestMapping("/hmj/my-parking")
@RequiredArgsConstructor
public class MiniappParkingController {

    private final MyParkingService myParkingService;

    @Operation(summary = "获取我的车位列表")
    @GetMapping(value = "/list")
    public R<List<MyParkingVO>> getMyParkingList() {
        try {
            List<MyParkingVO> result = myParkingService.getMyParkingList();
            return R.success(result);
        } catch (Exception e) {
            log.error("获取我的车位列表失败", e);
            return R.failed("获取我的车位列表失败：" + e.getMessage());
        }
    }

    @Operation(summary = "添加车辆到车位")
    @SysLog(title = "我的车位管理", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add-car")
    public R<String> addCarToParking(@RequestBody AddCarRequest request) {
        try {
            myParkingService.addCarToParking(request);
            return R.success("添加车辆成功");
        } catch (Exception e) {
            log.error("添加车辆失败", e);
            return R.failed("添加车辆失败：" + e.getMessage());
        }
    }


    @Operation(summary = "删除车辆信息")
    @SysLog(title = "我的车位管理", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete-car")
    public R<String> deleteCarInfo(@RequestBody DelCarRequest request) {
        try {
            myParkingService.deleteCarInfo(request);
            return R.success("删除车辆信息成功");
        } catch (Exception e) {
            log.error("删除车辆信息失败", e);
            return R.failed("删除车辆信息失败：" + e.getMessage());
        }
    }

}
