package cn.harry.modular.hmj.vo;

import cn.harry.modular.hmj.domain.PoolPark;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PoolParkVO extends PoolPark {
    /**
     * 过期状态： 0-正常，1-即将到期，2-已到期
     */
    @Schema(description = "过期状态： 0-正常，1-即将到期，2-已到期")
    private Integer expireStatus;

    /**
     * 有效期开始时间
     */
    @Schema(description = "有效期开始时间")
    private String beginDateStr;
    /**
     * 有效期结束时间
     */
    @Schema(description = "有效期结束时间")
    private String endDateStr;
}
