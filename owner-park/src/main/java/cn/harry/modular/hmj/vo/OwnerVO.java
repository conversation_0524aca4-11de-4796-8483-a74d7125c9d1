package cn.harry.modular.hmj.vo;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class OwnerVO implements Serializable {
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;

    /**
     * 密码
     */
    @Schema(description = "密码")
    private String password;


    /**
     * 头像
     */
    @Schema(description = "头像")
    private String icon;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickName;

    /**
     * 性别，0:男 1:女 2:未知
     */
    @Schema(description = "性别，0:男 1:女 2:未知")
    private String sex;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    private String note;

    /**
     * 启用状态，0:禁用 1:启用
     */
    @Schema(description = "启用状态，0:禁用 1:启用")
    private String status;

    /**
     * 最后登录时间
     */
    @Schema(description = "最后登录时间")
    private Date loginTime;

    /**
     * 最后登陆IP
     */
    @Schema(description = "最后登陆IP")
    private String loginIp;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date modifyTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String modifyBy;

    /**
     * 有效状态，0:无效 1:有效
     */
    @Schema(description = "有效状态，0:无效 1:有效")
    @TableLogic
    private Integer valid;

    /**
     * 所属部门ID
     */
    @Schema(description = "所属部门ID")
    private Long deptId;

    /**
     * 所属部门名称
     */
    @Schema(description = "所属部门名称")
    private String deptName;

    /**
     * 关联的车位信息列表
     */
    @Schema(description = "关联的车位信息列表")
    private List<ParkingInfo> parkingList;

    /**
     * 关联的车牌号列表
     */
    @Schema(description = "关联的车牌号列表")
    private List<String> plateNumbers;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 车位信息内部类
     */
    @Data
    @Schema(description = "车位信息")
    public static class ParkingInfo {
        /**
         * 车位ID
         */
        @Schema(description = "车位ID")
        private Long id;

        /**
         * 车位号
         */
        @Schema(description = "车位号")
        private String parkName;

        /**
         * 车位池名称
         */
        @Schema(description = "车位池名称")
        private String poolName;

        /**
         * 车库名称
         */
        @Schema(description = "车库名称")
        private String pgname;

        /**
         * 车位类型(0:公用车位, 1:私家车位)
         */
        @Schema(description = "车位类型(0:公用车位, 1:私家车位)")
        private Integer parkType;

        /**
         * 绑定的车牌号
         */
        @Schema(description = "绑定的车牌号")
        private String plateNums;

        /**
         * 剩余车位数
         */
        @Schema(description = "剩余车位数")
        private Integer emptyNum;

        /**
         * 有效期开始时间
         */
        @Schema(description = "有效期开始时间")
        private String beginDateStr;

        /**
         * 有效期结束时间
         */
        @Schema(description = "有效期结束时间")
        private String endDateStr;
        /**
         * 过期状态： 0-正常，1-即将到期，2-已到期 3- 未知
         */
        @Schema(description = "过期状态： 0-正常，1-即将到期，2-已到期")
        private Integer expireStatus;
    }
}
