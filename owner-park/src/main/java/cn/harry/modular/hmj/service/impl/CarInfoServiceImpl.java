package cn.harry.modular.hmj.service.impl;

import cn.harry.common.exception.ApiException;
import cn.harry.common.utils.RedisLockUtil;
import cn.harry.modular.deliyun.enums.CardTypeEnums;
import cn.harry.modular.deliyun.req.FindCarListRequest;
import cn.harry.modular.deliyun.res.FindCarListResponse;
import cn.harry.modular.deliyun.service.DeliyunService;
import cn.harry.modular.hmj.domain.CarInfo;
import cn.harry.modular.hmj.domain.ParkingLot;
import cn.harry.modular.hmj.enums.SyncStatusEnums;
import cn.harry.modular.hmj.mapper.CarInfoMapper;
import cn.harry.modular.hmj.service.CarInfoService;
import cn.harry.modular.hmj.service.ParkingLotService;
import cn.harry.modular.hmj.service.PoolParkService;
import cn.harry.modular.hmj.utils.HmjUtils;
import cn.harry.modular.hmj.vo.CarInfoVO;
import cn.harry.modular.hmj.vo.GarageVO;
import cn.harry.modular.hmj.vo.PoolParkVO;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description 针对表【t_car_info(车辆信息表)】的数据库操作Service实现
 * @createDate 2025-06-26 16:34:52
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CarInfoServiceImpl extends ServiceImpl<CarInfoMapper, CarInfo> implements CarInfoService {
    private final DeliyunService deliyunService;
    private final PoolParkService poolParkService;
    private final ParkingLotService parkingLotService;
    private final RedisLockUtil redisLockUtil;

    @Override
    public void syncDeliyunCarInfoByGarage(Long garageId) {
        // 构建锁的key和value
        String lockKey = "sync_car_info_garage_" + garageId;
        String lockValue = UUID.randomUUID().toString();

        // 尝试获取分布式锁，锁定10分钟
        if (!redisLockUtil.tryLock(lockKey, lockValue, 600)) {
            throw new ApiException("车辆信息正在同步中，请稍后再试");
        }

        try {
            log.info("开始同步车库[{}]的车辆信息", garageId);

            // 先将指定车库的同步状态更新为 0
            update(Wrappers.<CarInfo>lambdaUpdate()
                    .eq(CarInfo::getGarageId, garageId)
                    .set(CarInfo::getSyncStatus, SyncStatusEnums.NOT_SYNC.getCode()));

            GarageVO garageVO = parkingLotService.getGarageById(garageId);
            if (garageVO == null) {
                throw new ApiException("车库不存在");
            }

            int page = 1;
            int size = 100;
            syncDeliyunCarInfo(garageVO, page, size);

            log.info("车库[{}]的车辆信息同步完成", garageId);
        } finally {
            // 释放分布式锁
            redisLockUtil.unlock(lockKey, lockValue);
        }
    }


    @Override
    public IPage<CarInfoVO> getPageVO(Page<CarInfo> page, CarInfo entity) {
        LambdaQueryWrapper<CarInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StrUtil.isNotEmpty(entity.getPlateNum()), CarInfo::getPlateNum, entity.getPlateNum());
        wrapper.like(StrUtil.isNotEmpty(entity.getParkNum()), CarInfo::getParkNum, entity.getParkNum());
        wrapper.eq(ObjectUtil.isNotEmpty(entity.getCardTypeId()), CarInfo::getCardTypeId, entity.getCardTypeId());
        wrapper.like(StrUtil.isNotEmpty(entity.getPname()), CarInfo::getPname, entity.getPname());
        wrapper.like(StrUtil.isNotEmpty(entity.getMobile()), CarInfo::getMobile, entity.getMobile());
        wrapper.eq(ObjectUtil.isNotEmpty(entity.getGarageId()), CarInfo::getGarageId, entity.getGarageId());
        wrapper.orderByDesc(CarInfo::getCreateDate);

        IPage<CarInfo> pageData = page(page, wrapper);
        return pageData.convert(item -> {
            CarInfoVO vo = new CarInfoVO();
            BeanUtil.copyProperties(item, vo);

            Integer expireStatus = HmjUtils.funExpiryStatus(item.getEndDate());
            vo.setExpireStatus(expireStatus);

            Long cardTypeId = item.getCardTypeId();
            if (Objects.equals(CardTypeEnums.PARKING_SPACE_POOL_CAR.getCode(), cardTypeId)) {
                PoolParkVO poolParkVO = poolParkService.getByGarageIdAndPlateNum(item.getGarageId(), item.getPlateNum());
                if (poolParkVO != null) {
                    vo.setPoolParkBeginDate(poolParkVO.getBeginDateStr());
                    vo.setPoolParkEndDate(poolParkVO.getEndDateStr());
                    vo.setPoolParkExpireStatus(poolParkVO.getExpireStatus());

                    String parkNum = poolParkVO.getParkName();
                    vo.setParkNum(parkNum);
                }
            }
            return vo;
        });
    }

    private void syncDeliyunCarInfo(GarageVO lot, int page, int size) {
        String commKey = lot.getCommKey();
        if (StrUtil.isBlank(commKey)) {
            return;
        }

        FindCarListRequest findCarListRequest = new FindCarListRequest();
        findCarListRequest.setPage(page);
        findCarListRequest.setSize(size);
        List<FindCarListResponse> res = deliyunService.findCarList(findCarListRequest, commKey);
        if (res != null && !res.isEmpty()) {
            convertCarInfo(res, lot.getId());
            syncDeliyunCarInfo(lot, page + 1, size);
        }
    }

    @Override
    public Boolean syncByPlateNum(CarInfo entity) {
        Long garageId = entity.getGarageId();
        if (garageId == null) {
            return false;
        }
        ParkingLot parkingLot = parkingLotService.getById(garageId);
        if (parkingLot == null) {
            return false;
        }
        String commKey = parkingLot.getCommKey();
        if (StrUtil.isBlank(commKey)) {
            return false;
        }
        FindCarListRequest findCarListRequest = new FindCarListRequest();
        findCarListRequest.setPlateNum(entity.getPlateNum());
        List<FindCarListResponse> res = deliyunService.findCarList(findCarListRequest, commKey);
        if (res != null && !res.isEmpty()) {
            convertCarInfo(res, parkingLot.getId());
        }
        return null;
    }

    @Override
    public CarInfo getByGarageIdAndPlateNum(Long garageId, String plateNum) {
        return getOne(Wrappers.<CarInfo>lambdaQuery().eq(CarInfo::getGarageId, garageId).eq(CarInfo::getPlateNum, plateNum));
    }

    private void convertCarInfo(List<FindCarListResponse> res, Long lotId) {
        for (FindCarListResponse item : res) {
            String plateNum = item.getPlateNum();
            // 根据车牌号码查询车辆信息
            CarInfo carInfo = getByGarageIdAndPlateNum(lotId, plateNum);
            if (carInfo == null) {
                carInfo = new CarInfo();
            }

            carInfo.setGarageId(lotId);

            carInfo.setPlateNum(item.getPlateNum());
            carInfo.setPlateColor(item.getPlateColor());
            carInfo.setCardNo(item.getCardNo());
            carInfo.setCardTypeId(item.getCardTypeId());
            carInfo.setCarTypeId(item.getCarTypeId());
            carInfo.setGname(item.getGname());
            carInfo.setPname(item.getPname());
            carInfo.setMobile(item.getMobile());
            carInfo.setAddr(item.getAddr());
            carInfo.setBeginDate(item.getBeginDate());
            carInfo.setEndDate(item.getEndDate());
            carInfo.setBalance(item.getBalance());
            carInfo.setAuthPgName(item.getAuthPgName());
            carInfo.setLastInDate(item.getLastInDate());
            carInfo.setLastOutDate(item.getLastOutDate());
            carInfo.setRealName(item.getRealName());
            carInfo.setRemark(item.getRemark());
            carInfo.setIoState(item.getIoState());
            carInfo.setCreateDate(item.getCreateDate());
            carInfo.setLastUpdateDate(item.getLastUpdateDate());

            carInfo.setSyncStatus(SyncStatusEnums.SYNC.getCode());
            carInfo.setSyncLastDate(DateUtil.date());

            // 根据车牌号 获取车位池号
            String parkNum = item.getParkNum();
            if (StrUtil.isBlank(parkNum)) {
                parkNum = poolParkService.getParkNumByGarageIdAndParkName(lotId, item.getPlateNum());
            }
            carInfo.setParkNum(parkNum);

            saveOrUpdate(carInfo);
        }
    }


}




