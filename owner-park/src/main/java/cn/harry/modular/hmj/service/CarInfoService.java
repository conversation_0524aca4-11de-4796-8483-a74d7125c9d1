package cn.harry.modular.hmj.service;

import cn.harry.modular.hmj.domain.CarInfo;
import cn.harry.modular.hmj.vo.CarInfoVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【t_car_info(车辆信息表)】的数据库操作Service
* @createDate 2025-06-26 16:34:52
*/
public interface CarInfoService extends IService<CarInfo> {

    // 根据车库同步德立云车辆信息
    void syncDeliyunCarInfoByGarage(Long garageId);

    IPage<CarInfoVO> getPageVO(Page<CarInfo> page, CarInfo entity);

    /**
     * 根据车牌号同步车辆信息
     *
     * @param entity
     * @return
     */
    Boolean syncByPlateNum(CarInfo entity);

    /**
     * 根据车库和车牌号查询车辆信息
     *
     * @param garageId
     * @param plateNum
     * @return
     */
    CarInfo getByGarageIdAndPlateNum(Long garageId, String plateNum);
}
