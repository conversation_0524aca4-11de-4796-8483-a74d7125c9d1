package cn.harry.modular.hmj.vo;

import cn.harry.modular.hmj.domain.RechargeRecord;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 支付记录VO
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "支付记录VO")
public class PaymentRecordVO extends RechargeRecord {
    /**
     * 支付状态名称
     */
    private String payStatusLabel;
}
