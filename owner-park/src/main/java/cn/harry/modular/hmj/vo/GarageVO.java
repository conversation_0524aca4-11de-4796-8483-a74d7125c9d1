package cn.harry.modular.hmj.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
public class GarageVO {

    /**
     * 停车场ID
     */
    @Schema(description = "停车场ID")
    private Long id;

    /**
     * 停车场名称
     */
    @Schema(description = "停车场名称")
    private String pname;

    /**
     * 项目唯一编码
     */
    @Schema(description = "项目唯一编码")
    private String commKey;
    /**
     * 状态 0 禁用 1 启用
     */
    @Schema(description = "状态 0 禁用 1 启用")
    private Integer status;
}
