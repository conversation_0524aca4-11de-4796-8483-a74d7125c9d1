package cn.harry.modular.hmj.service;

import cn.harry.modular.hmj.param.AddCarRequest;
import cn.harry.modular.hmj.param.DelCarRequest;
import cn.harry.modular.hmj.vo.MyParkingVO;

import java.util.List;

/**
 * 我的车位服务接口
 * <AUTHOR>
 * @公众号 Harry技术
 */
public interface MyParkingService {

    /**
     * 获取我的车位列表
     *
     * @return 车位列表
     */
    List<MyParkingVO> getMyParkingList();

    /**
     * 添加车辆到车位
     *
     * @param request 添加车辆请求参数
     */
    void addCarToParking(AddCarRequest request);


    /**
     * 删除车辆信息
     *
     * @param carId 车辆ID
     */
    void deleteCarInfo(DelCarRequest carId);

}
