package cn.harry.modular.hmj.controller;

import cn.harry.common.annotation.SysLog;
import cn.harry.common.api.R;
import cn.harry.common.enums.BusinessType;
import cn.harry.modular.hmj.domain.CarInfo;
import cn.harry.modular.hmj.param.DelCarRequest;
import cn.harry.modular.hmj.service.CarInfoService;
import cn.harry.modular.hmj.service.MyParkingService;
import cn.harry.modular.hmj.vo.CarInfoVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 车辆信息表前端控制层
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Tag(name = "车辆信息表接口")
@RestController
@RequestMapping("/hmj/carInfo")
@RequiredArgsConstructor
public class CarInfoController {

    private final CarInfoService carInfoService;

    private final MyParkingService myParkingService;

    /**
     * 分页查询列表
     */
    @Operation(summary = "分页查询")
    @PreAuthorize("@ss.hasPermission('t_car_info_page')")
    @GetMapping(value = "/page")
    public R<IPage<CarInfoVO>> page(@ParameterObject Page<CarInfo> page, @ParameterObject CarInfo entity) {
        IPage<CarInfoVO> pageDataVO = carInfoService.getPageVO(page, entity);
        return R.success(pageDataVO);
    }

    /**
     * 根据id获取详情
     */
    @Operation(summary = "根据id获取详情")
    @PreAuthorize("@ss.hasPermission('t_car_info_get')")
    @GetMapping(value = "/{id}")
    public R<CarInfo> getById(@PathVariable Long id) {
        return R.success(carInfoService.getById(id));
    }

    /**
     * 新增
     */
    @Operation(summary = "新增")
    @PreAuthorize("@ss.hasPermission('t_car_info_add')")
    @SysLog(title = "t_car_info", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Integer> save(@RequestBody CarInfo entity) {
        return carInfoService.save(entity) ? R.success() : R.failed();
    }

    /**
     * 更新
     */
    @Operation(summary = "更新")
    @PreAuthorize("@ss.hasPermission('t_car_info_edit')")
    @SysLog(title = "t_car_info", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Integer> update(@RequestBody CarInfo entity) {
        return carInfoService.updateById(entity) ? R.success() : R.failed();
    }

    /**
     * 删除
     */
    @Operation(summary = "删除")
    @PreAuthorize("@ss.hasPermission('t_car_info_del')")
    @SysLog(title = "t_car_info", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/{ids}")
    public R<Integer> deleteByIds(@Parameter(description = "多个以英文逗号(,)拼接") @PathVariable Long[] ids) {

        for (Long id : ids) {
            DelCarRequest request = new DelCarRequest();
            request.setCarId(id);
            myParkingService.deleteCarInfo(request);
        }

        return R.success();
    }

    @Operation(summary = "同步车辆信息")
    @PostMapping(value = "/syncCarInfo")
    public R<Integer> syncCarInfo(@RequestParam(required = false) Long garageId) {
        if (garageId != null) {
            carInfoService.syncDeliyunCarInfoByGarage(garageId);
        } else {
            return R.failed("请先选择要同步的车库信息");
        }
        return R.success();
    }

    @Operation(summary = "根据车牌号同步车辆信息")
    @PostMapping(value = "/syncByPlateNum")
    public R<Boolean> syncByPlateNum(@RequestBody CarInfo entity) {
        return R.success(carInfoService.syncByPlateNum(entity));
    }
}