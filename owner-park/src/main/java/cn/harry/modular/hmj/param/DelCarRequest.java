package cn.harry.modular.hmj.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 删除车辆请求参数
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
@Schema(description = "删除车辆请求参数")
public class DelCarRequest {

    /**
     * 车位ID
     */
    @Schema(description = "车位ID")
    private Long parkingId;


    /**
     * 车型ID
     */
    @Schema(description = "车型ID")
    private Long carId;

}
