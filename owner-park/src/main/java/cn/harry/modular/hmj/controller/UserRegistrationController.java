package cn.harry.modular.hmj.controller;

import cn.harry.common.annotation.SysLog;
import cn.harry.common.api.R;
import cn.harry.common.enums.BusinessType;
import cn.harry.modular.hmj.domain.UserRegistration;
import cn.harry.modular.hmj.service.UserRegistrationService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
* 用户信息登记表前端控制层
*
* <AUTHOR>
* @公众号 Harry技术
*/
@Tag(name = "用户信息登记表接口")
@RestController
@RequestMapping("/hmj/userRegistration")
@RequiredArgsConstructor
public class UserRegistrationController  {

    private final UserRegistrationService userRegistrationService;

    /**
    * 分页查询列表
    */
    @Operation(summary = "分页查询")
    @PreAuthorize("@ss.hasPermission('t_user_registration_page')")
    @GetMapping(value = "/page")
        public R<IPage<UserRegistration>> page(@ParameterObject Page<UserRegistration> page,@ParameterObject  UserRegistration entity) {
        return R.success(userRegistrationService.page(page, Wrappers.lambdaQuery(entity).orderByDesc(UserRegistration::getId)));
    }

    /**
    * 根据id获取详情
    */
    @Operation(summary = "根据id获取详情")
    @PreAuthorize("@ss.hasPermission('t_user_registration_get')")
    @GetMapping(value = "/{id}")
    public R<UserRegistration> getById(@PathVariable Long id) {
        return R.success(userRegistrationService.getById(id));
    }

    /**
    * 新增
    */
    @Operation(summary = "新增")
    @PreAuthorize("@ss.hasPermission('t_user_registration_add')")
    @SysLog(title = "t_user_registration", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Integer> save(@RequestBody UserRegistration entity) {
        return userRegistrationService.save(entity) ? R.success() : R.failed();
    }

    /**
    * 更新
    */
    @Operation(summary = "更新")
    @PreAuthorize("@ss.hasPermission('t_user_registration_edit')")
    @SysLog(title = "t_user_registration", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Integer> update(@RequestBody UserRegistration entity) {
        return userRegistrationService.updateById(entity) ? R.success() : R.failed();
    }

    /**
    * 删除
    */
    @Operation(summary = "删除")
    @PreAuthorize("@ss.hasPermission('t_user_registration_del')")
    @SysLog(title = "t_user_registration", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/{ids}")
    public R<Integer> deleteByIds(@Parameter(description = "多个以英文逗号(,)拼接") @PathVariable Long[] ids) {
        return userRegistrationService.removeBatchByIds(Arrays.asList(ids)) ? R.success() : R.failed();
    }
}