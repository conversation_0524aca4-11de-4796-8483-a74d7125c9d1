package cn.harry.modular.hmj.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 车位池关联车位车牌信息表
 * @TableName t_pool_park_plate_nums
 */
@TableName(value ="t_pool_park_plate_nums")
@Data
public class PoolParkPlateNums implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 车库ID
     */
    @Schema(description = "车库ID")
    private Long garageId;
    /**
     * 所属车位池
     */
    private String poolName;

    /**
     * 车位满缴费方式(1：先出收费，2：后进收费)
     */
    private Byte chargeType;

    /**
     * 车位池满统计规则（1：按停车场统计 2：按车库统计）
     */
    private Byte fullMode;

    /**
     * 总车位数
     */
    private Integer totalNum;

    /**
     * 有效车位数
     */
    private Integer validNum;

    /**
     * 在场车辆数
     */
    private Integer parkedNum;

    /**
     * 剩余车位数
     */
    private Integer emptyNum;

    /**
     * 绑定的车牌号
     */
    private String plateNums;

    /**
     * 所属楼栋单元房铺
     */
    private String hourseAddr;

    /**
     * 管理员姓名
     */
    private String pname;

    /**
     * 管理员电话
     */
    private String mobile;

    /**
     * 免费换车时间(单位分钟)
     */
    private Integer freeTime;

    /**
     * 同步标识 0 未同步 1 已同步
     */
    private String syncStatus;

    /**
     * 最后一次同步时间
     */
    private Date syncLastDate;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}