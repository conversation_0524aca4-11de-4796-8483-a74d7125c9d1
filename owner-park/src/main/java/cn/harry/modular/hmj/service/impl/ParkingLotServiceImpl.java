package cn.harry.modular.hmj.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaCodeLineColor;
import cn.harry.modular.hmj.vo.GarageVO;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.harry.modular.hmj.domain.ParkingLot;
import cn.harry.modular.hmj.service.ParkingLotService;
import cn.harry.modular.hmj.mapper.ParkingLotMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_parking_lot(停车场信息表)】的数据库操作Service实现
 * @createDate 2025-07-15 19:12:32
 */
@Slf4j
@Service
@AllArgsConstructor
public class ParkingLotServiceImpl extends ServiceImpl<ParkingLotMapper, ParkingLot>
        implements ParkingLotService {
    private final WxMaService wxMaService;

    @Override
    public List<GarageVO> getGarageList() {
        List<ParkingLot> parkingLotList = this.list();
        if (parkingLotList != null && !parkingLotList.isEmpty()) {
            return parkingLotList.stream().map(item -> {
                GarageVO vo = new GarageVO();
                BeanUtil.copyProperties(item, vo);
                return vo;
            }).toList();
        }
        return List.of();
    }

    @Override
    public GarageVO getGarageById(Long id) {
        ParkingLot parkingLot = this.getById(id);
        if (parkingLot != null) {
            GarageVO vo = new GarageVO();
            BeanUtil.copyProperties(parkingLot, vo);
            return vo;
        }
        return null;
    }

    @Override
    public String getGarageQrcode(String scene, String pages) {
        try {
            byte[] qrcodeBytes = wxMaService.getQrcodeService().createWxaCodeUnlimitBytes(scene, pages, true, "release", 1280, false, new WxMaCodeLineColor("", "", ""), false);
            if (qrcodeBytes != null) {
                return Base64.encode(qrcodeBytes);
            }
        } catch (WxErrorException e) {
            log.error("生成二维码失败：{}", e.getMessage());
        }
        return null;
    }
}




