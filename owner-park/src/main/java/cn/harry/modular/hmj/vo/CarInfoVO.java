package cn.harry.modular.hmj.vo;

import cn.harry.modular.hmj.domain.CarInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CarInfoVO extends CarInfo {
    /**
     * 过期状态： 0-正常，1-即将到期，2-已到期
     */
    private Integer expireStatus;


    /**
     * 停车位有效期开始时间（时间戳，单位:秒）
     */
    @Schema(description = "有效期开始时间（时间戳，单位:秒）")
    private String poolParkBeginDate;

    /**
     * 停车位有效期结束时间（时间戳，单位:秒）
     */
    @Schema(description = "有效期结束时间（时间戳，单位:秒）")
    private String poolParkEndDate;

    /**
     * 停车位过期状态： 0-正常，1-即将到期，2-已到期
     */
    private Integer poolParkExpireStatus;
}
