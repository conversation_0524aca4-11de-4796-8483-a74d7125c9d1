package cn.harry.modular.hmj.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class SelectParkingSpotVO {
    /**
     * 车位ID
     */
    @Schema(description = "车位ID")
    private Long id;

    /**
     * 车位类型(0:公用车位, 1:私家车位)
     */
    @Schema(description = "车位类型(0:公用车位, 1:私家车位)")
    private Integer parkType;
    /**
     * 所属车位池
     */
    @Schema(description = "所属车位池")
    private String poolName;

    /**
     * 车位名称、车位号
     */
    @Schema(description = "车位名称、车位号")
    private String parkName;
}
