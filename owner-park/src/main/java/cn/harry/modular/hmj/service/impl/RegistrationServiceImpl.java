package cn.harry.modular.hmj.service.impl;

import cn.harry.common.exception.ApiException;
import cn.harry.component.security.utils.SecurityUtils;
import cn.harry.modular.hmj.domain.PoolPark;
import cn.harry.modular.hmj.domain.UserRegistration;
import cn.harry.modular.hmj.enums.PayStatusEnums;
import cn.harry.modular.hmj.enums.PoolParkStatusEnums;
import cn.harry.modular.hmj.param.CalculateFeeParam;
import cn.harry.modular.hmj.param.RegistrationParam;
import cn.harry.modular.hmj.param.SelectParkingSpotParam;
import cn.harry.modular.hmj.service.PoolParkService;
import cn.harry.modular.hmj.service.RegistrationService;
import cn.harry.modular.hmj.service.UserRegistrationService;
import cn.harry.modular.hmj.vo.CalculateFeeVO;
import cn.harry.modular.hmj.vo.SelectParkingSpotVO;
import cn.harry.modular.system.domain.SysUser;
import cn.harry.modular.system.service.SysDictDataService;
import cn.harry.modular.system.service.SysUserService;
import cn.harry.modular.system.vo.DictItemOptionVO;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RegistrationServiceImpl implements RegistrationService {

    private final SysDictDataService sysDictDataService;
    private final PoolParkService poolParkService;
    private final UserRegistrationService userRegistrationService;
    private final SysUserService sysUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(RegistrationParam registrationParam) {
        // 1. 验证参数
        boolean isValidate = validateParameters(registrationParam);
        if (!isValidate) {
            log.error("用户登记信息验证失败,缺少必要参数: {}", registrationParam);
            return 0L;
        }
        // 2. 保存到数据库
        UserRegistration userRegistration = convertUserRegistrationParam(registrationParam);
        return userRegistration.getId();
    }

    private UserRegistration convertUserRegistrationParam(RegistrationParam registrationParam) {
        UserRegistration userRegistration = BeanUtil.copyProperties(registrationParam, UserRegistration.class);
        // 判断是否为续费 续费 则获取用户信息
        SysUser sysUser = SecurityUtils.getSysUser();
        userRegistration.setUserId(sysUser.getId());

        if (1 == userRegistration.getIsRenewal()) {
            userRegistration.setMobile(sysUser.getPhone());
            userRegistration.setPname(sysUser.getNickName());
        }
        // 如果是登记 更新一下用户信息
        if (0 == userRegistration.getIsRenewal() && sysUser.getPhone() == null) {
            sysUser.setPhone(userRegistration.getMobile());
            sysUser.setNickName(userRegistration.getPname());
            sysUserService.updateUserInfo(sysUser);
        }

        if (0 == userRegistration.getIsRenewal()) {
            // 如果是登记 锁定车位状态
            Long parkId = userRegistration.getParkId();
            poolParkService.updateStatus(parkId, PoolParkStatusEnums.PENDING.getCode());
        }
        userRegistration.setStatus(PayStatusEnums.PENDING.getCode());
        userRegistrationService.save(userRegistration);
        return userRegistration;
    }

    private boolean validateParameters(RegistrationParam registrationParam) {
        if (registrationParam == null) {
            log.error("用户登记信息为空");
            return false;
        }
        if (registrationParam.getPname() == null) {
            log.error("用户登记信息缺少车主名称");
        }
        if (registrationParam.getMobile() == null) {
            log.error("用户登记信息缺少手机号");
        }
        if (registrationParam.getPlateNum() == null) {
            log.error("用户登记信息缺少车牌号码");
            return false;
        }
        if (registrationParam.getParkNum() == null) {
            log.error("用户登记信息缺少车位号");
            return false;
        }
        if (registrationParam.getBillingType() == null) {
            log.error("用户登记信息缺少计费类型");
            return false;
        }
        if (registrationParam.getBeginDate() == null) {
            log.error("用户登记信息缺少开始时间");
            return false;
        }
        if (registrationParam.getEndDate() == null) {
            log.error("用户登记信息缺少结束时间");
            return false;
        }
        if (registrationParam.getShouldPay() == null) {
            log.error("用户登记信息缺少应缴费用");
            return false;
        }
        return true;
    }

    @Override
    public CalculateFeeVO calculateFee(CalculateFeeParam param) {
        List<DictItemOptionVO> billingTypeList = sysDictDataService.getDictItems("billing_type");
        for (DictItemOptionVO item : billingTypeList) {
            if (item.getLabel().equals(param.getBillingType())) {
                return calculateShouldPay(param.getBillingType(), item.getValue(), param.getStartTime(), param.getPeriodCount());
            }
        }
        return null;
    }

    @Override
    public List<SelectParkingSpotVO> selectParkingSpot(SelectParkingSpotParam param) {
        if (param.getGarageId() == null) {
            throw new ApiException("请选择车库");
        }
        long time = System.currentTimeMillis() / 1000;
        List<PoolPark> list = poolParkService.list(
                Wrappers.<PoolPark>lambdaQuery()
                        .eq(PoolPark::getGarageId, param.getGarageId())
                        .eq(PoolPark::getStatus, PoolParkStatusEnums.FREE.getCode())
        );
        return list.stream().map(item -> {
            SelectParkingSpotVO selectParkingSpotVO = new SelectParkingSpotVO();
            selectParkingSpotVO.setParkName(item.getParkName());
            selectParkingSpotVO.setPoolName(item.getPoolName());
            selectParkingSpotVO.setParkType(item.getParkType());
            selectParkingSpotVO.setId(item.getId());
            return selectParkingSpotVO;
        }).toList();

    }

    /**
     * 计算应缴费用
     *
     * @param billingType 计费类型：日、月、季、年
     * @param value       单价值
     * @param beginDate   开始日期 (yyyy-MM-dd)
     * @param periodCount 期数
     * @return 计算结果
     */
    public CalculateFeeVO calculateShouldPay(String billingType, String value, String beginDate, Integer periodCount) {
        CalculateFeeVO calculateFeeVO = new CalculateFeeVO();
        calculateFeeVO.setBillingType(billingType);
        calculateFeeVO.setStartTime(beginDate);
        calculateFeeVO.setPeriodCount(periodCount);

        if (billingType == null || beginDate == null) {
            calculateFeeVO.setShouldPay(0.0);
            return calculateFeeVO;
        }

        try {
            // 解析开始日期
            java.util.Date startDate = DateUtil.parse(beginDate, "yyyy-MM-dd");
            java.util.Date endDate;
            double shouldPay = 0.0;

            // 确保periodCount不为null，默认为1
            int count = periodCount != null ? periodCount : 1;

            // 根据计费类型计算结束时间
            endDate = switch (billingType) {
                case "日", "日租" ->
                    // 日租：开始时间 + count天
                        DateUtil.offsetDay(startDate, count);
                case "月", "月租" ->
                    // 月租：开始时间 + count个月
                        DateUtil.offsetMonth(startDate, count);
                case "季", "季租" ->
                    // 季租：开始时间 + count个季度（每季度3个月）
                        DateUtil.offsetMonth(startDate, count * 3);
                case "年", "年租" ->
                    // 年租：开始时间 + count年
                        DateUtil.offsetMonth(startDate, count * 12);
                default ->
                    // 默认情况，返回开始时间
                        startDate;
            };

            // 设置结束时间（格式化为 yyyy-MM-dd） - 1
            calculateFeeVO.setEndTime(DateUtil.formatDate(DateUtil.offsetDay(endDate, -1)));

            // 根据 periodCount * value 计算应缴费用
            if (value != null && !value.trim().isEmpty()) {
                try {
                    double unitPrice = Double.parseDouble(value);
                    shouldPay = count * unitPrice;
                } catch (NumberFormatException e) {
                    log.warn("解析价格失败: {}", value, e);
                    shouldPay = 0.0;
                }
            }

            // 设置应缴费用
            calculateFeeVO.setShouldPay(shouldPay);

        } catch (Exception e) {
            log.error("计算费用失败", e);
            calculateFeeVO.setEndTime(beginDate);
            calculateFeeVO.setShouldPay(0.0);
        }

        return calculateFeeVO;
    }

}
