package cn.harry.modular.hmj.controller;

import cn.harry.common.annotation.SysLog;
import cn.harry.common.api.R;
import cn.harry.common.enums.BusinessType;
import cn.harry.modular.deliyun.req.ParkDelayRequest;
import cn.harry.modular.hmj.domain.PoolPark;
import cn.harry.modular.hmj.dto.BatchAddPoolParkRequest;
import cn.harry.modular.hmj.service.PoolParkService;
import cn.harry.modular.hmj.vo.PoolParkVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * 车位信息表前端控制层
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Tag(name = "车位信息表接口")
@RestController
@RequestMapping("/hmj/poolPark")
@RequiredArgsConstructor
public class PoolParkController {

    private final PoolParkService poolParkService;

    /**
     * 分页查询列表
     */
    @Operation(summary = "分页查询")
    @PreAuthorize("@ss.hasPermission('t_pool_park_page')")
    @GetMapping(value = "/page")
    public R<IPage<PoolParkVO>> page(@ParameterObject Page<PoolPark> page, @ParameterObject PoolPark entity) {
        IPage<PoolParkVO> pageDataVO = poolParkService.getPageVO(page, entity);
        return R.success(pageDataVO);
    }

    /**
     * 根据id获取详情
     */
    @Operation(summary = "根据id获取详情")
    @PreAuthorize("@ss.hasPermission('t_pool_park_get')")
    @GetMapping(value = "/{id}")
    public R<PoolPark> getById(@PathVariable Long id) {
        return R.success(poolParkService.getById(id));
    }

    /**
     * 新增
     */
    @Operation(summary = "新增")
    @PreAuthorize("@ss.hasPermission('t_pool_park_add')")
    @SysLog(title = "t_pool_park", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Integer> save(@RequestBody PoolPark entity) {
        return poolParkService.save(entity) ? R.success() : R.failed();
    }

    /**
     * 更新
     */
    @Operation(summary = "更新")
    @PreAuthorize("@ss.hasPermission('t_pool_park_edit')")
    @SysLog(title = "t_pool_park", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Integer> update(@RequestBody PoolPark entity) {
        return poolParkService.updateById(entity) ? R.success() : R.failed();
    }

    /**
     * 删除
     */
    @Operation(summary = "删除")
    @PreAuthorize("@ss.hasPermission('t_pool_park_del')")
    @SysLog(title = "t_pool_park", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/{ids}")
    public R<Integer> deleteByIds(@Parameter(description = "多个以英文逗号(,)拼接") @PathVariable Long[] ids) {
        return poolParkService.removeBatchByIds(Arrays.asList(ids)) ? R.success() : R.failed();
    }

    @Operation(summary = "同步车位信息")
    @PostMapping("/syncPoolPark")
    public R<String> syncPoolPark(@RequestParam(required = false) Long garageId) {
        if (garageId != null) {
            poolParkService.syncPoolParkByGarage(garageId);
        } else {
            return R.failed("请先选择要同步的车库信息");
        }
        return R.success("同步成功");
    }

    @Operation(summary = "获取可用车位列表")
    @GetMapping("/available")
    public R<IPage<PoolParkVO>> getAvailableParkingSpots(@ParameterObject Page<PoolPark> page) {
        // 查询可用车位（剩余车位数大于0的车位）
        PoolPark entity = new PoolPark();
        IPage<PoolParkVO> pageDataVO = poolParkService.getPageVO(page, entity);
        return R.success(pageDataVO);
    }


    @Operation(summary = "获取未关联的车位列表")
    @GetMapping(value = "/unassociated-parking")
    public R<IPage<PoolParkVO>> getUnassociatedParking(
            @ParameterObject Page<PoolPark> page,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {

        IPage<PoolParkVO> pageDataVO = poolParkService.getUnassociatedParking(page, keyword);
        return R.success(pageDataVO);
    }

    @Operation(summary = "车位延期")
    @PostMapping(value = "/parkDelay")
    public R<String> parkDelay(@RequestBody ParkDelayRequest entity) {
        poolParkService.parkDelay(entity);
        return R.success("延期成功");
    }

    @Operation(summary = "批量添加车位")
    @PreAuthorize("@ss.hasPermission('t_pool_park_add')")
    @SysLog(title = "t_pool_park", businessType = BusinessType.INSERT)
    @PostMapping(value = "/batchAdd")
    public R<String> batchAdd(@RequestBody BatchAddPoolParkRequest request) {
        int count = poolParkService.batchAdd(request);
        return R.success("成功添加 " + count + " 个车位");
    }

}