package cn.harry.modular.hmj.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class RegistrationParam {

    /**
     * 车主
     */
    @Schema(description = "车主")
    private String pname;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String mobile;
    /**
     * 地址
     */
    @Schema(description = "地址")
    private String addr;

    /**
     * 业主凭证
     */
    @Schema(description = "业主凭证")
    private String ownerCertificate;

    /**
     * 车牌号码
     */
    @Schema(description = "车牌号码")
    private String plateNum;
    /**
     * 车位ID
     */
    @Schema(description = "车位ID")
    private Long parkId;
    /**
     * 车位号
     */
    @Schema(description = "车位号")
    private String parkNum;
    /**
     * 计费类型 日租 月租 季租 年租
     */
    @Schema(description = "计费类型 1.日租 2.月租 3.季租 4.年租")
    private String billingType;

    /**
     * 开始时间（yyyy-MM-dd）
     */
    @Schema(description = "开始时间（yyyy-MM-dd）")
    private String beginDate;

    /**
     * 结束时间（yyyy-MM-dd）
     */
    @Schema(description = "结束时间（yyyy-MM-dd）")
    private String endDate;
    /**
     * 应缴费用
     */
    @Schema(description = "应缴费用")
    private String shouldPay;

    /**
     * openid
     */
    @Schema(description = "openid")
    private String openid;
    /**
     * 是否续费 0:登记 1:续费
     */
    @Schema(description = "是否续费 0:登记 1:续费")
    private Integer isRenewal;
    /**
     * 车库ID
     */
    @Schema(description = "车库ID")
    private Long garageId;
}
