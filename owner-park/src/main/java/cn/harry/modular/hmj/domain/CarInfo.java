package cn.harry.modular.hmj.domain;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 车辆信息表
 *
 * @TableName t_car_info
 */
@Data
@TableName(value = "t_car_info")
public class CarInfo {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 车库ID
     */
    @Schema(description = "车库ID")
    private Long garageId;
    /**
     * 是否续费 0:登记 1:续费
     */
    @Schema(description = "是否续费 0:登记 1:续费")
    private Integer isRenewal;
    /**
     * 车牌号码
     */
    @Schema(description = "车牌号码")
    private String plateNum;

    /**
     * 车牌颜色(1 蓝色，2 黄色，3 白色，4 黑色, 5:绿色, 6: 黄绿色)
     */
    @Schema(description = "车牌颜色(1 蓝色，2 黄色，3 白色，4 黑色, 5:绿色, 6: 黄绿色)")
    private Byte plateColor;

    /**
     * 注册号/卡号
     */
    @Schema(description = "注册号/卡号")
    private String cardNo;

    /**
     * 车位号
     */
    @Schema(description = "车位号")
    private String parkNum;

    /**
     * 计费类型ID
     */
    @Schema(description = "计费类型ID")
    private Long cardTypeId;

    /**
     * 车类型ID
     */
    @Schema(description = "车类型ID")
    private Long carTypeId;

    /**
     * 车辆分组
     */
    @Schema(description = "车辆分组")
    private String gname;

    /**
     * 车主
     */
    @Schema(description = "车主")
    private String pname;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String mobile;

    /**
     * 地址
     */
    @Schema(description = "地址")
    private String addr;

    /**
     * 有效期开始时间（yyyy-MM-dd）
     */
    @Schema(description = "有效期开始时间（yyyy-MM-dd）")
    private String beginDate;

    /**
     * 有效期结束时间（yyyy-MM-dd）
     */
    @Schema(description = "有效期结束时间（yyyy-MM-dd）")
    private String endDate;

    /**
     * 余额，只限于储值票车
     */
    @Schema(description = "余额，只限于储值票车")
    private Double balance;

    /**
     * 授权车库，多个以逗号隔开，例如：A车库,B车库
     */
    @Schema(description = "授权车库，多个以逗号隔开，例如：A车库,B车库")
    private String authPgName;

    /**
     * 最后入场时间(yyyy-MM-dd HH:mm:ss)
     */
    @Schema(description = "最后入场时间(yyyy-MM-dd HH:mm:ss)")
    private String lastInDate;

    /**
     * 最后出场时间(yyyy-MM-dd HH:mm:ss)
     */
    @Schema(description = "最后出场时间(yyyy-MM-dd HH:mm:ss)")
    private String lastOutDate;

    /**
     * 最后修改人
     */
    @Schema(description = "最后修改人")
    private String realName;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 状态(0:离场, 1:在场)
     */
    @Schema(description = "状态(0:离场, 1:在场)")
    private Byte ioState;

    /**
     * 创建时间（yyyy-MM-dd HH:mm:ss）
     */
    @Schema(description = "创建时间（yyyy-MM-dd HH:mm:ss）")
    private String createDate;

    /**
     * 最后修改时间（yyyy-MM-dd HH:mm:ss）
     */
    @Schema(description = "最后修改时间（yyyy-MM-dd HH:mm:ss）")
    private String lastUpdateDate;

    /**
     * 同步标识 0 未同步 1 已同步
     */
    @Schema(description = "同步标识 0 未同步 1 已同步")
    private String syncStatus;
    /**
     * 最后同步时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    @Schema(description = "最后一次同步时间")
    private Date syncLastDate;

}