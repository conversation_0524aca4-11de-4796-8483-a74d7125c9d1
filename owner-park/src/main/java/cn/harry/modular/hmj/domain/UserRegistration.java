package cn.harry.modular.hmj.domain;

import cn.harry.common.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户信息登记表
 *
 * @TableName t_user_registration
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_user_registration")
@Data
public class UserRegistration extends BaseEntity {
    /**
     * 车库ID
     */
    @Schema(description = "车库ID")
    private Long garageId;
    /**
     * 车主
     */
    private String pname;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 地址
     */
    private String addr;

    /**
     * 业主凭证
     */
    private String ownerCertificate;

    /**
     * 车牌号码
     */
    private String plateNum;
    /**
     * 车位ID
     */
    @Schema(description = "车位ID")
    private Long parkId;

    /**
     * 车位号
     */
    private String parkNum;

    /**
     * 计费类型 1.日租 2.月租 3.季租 4.年租
     */
    private String billingType;

    /**
     * 开始时间（yyyy-MM-dd）
     */
    private String beginDate;

    /**
     * 结束时间（yyyy-MM-dd）
     */
    private String endDate;

    /**
     * 应缴费用
     */
    private String shouldPay;

    /**
     * 状态
     */
    private String status;
    /**
     * openid
     */
    @Schema(description = "openid")
    private String openid;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 是否续费 0:登记 1:续费
     */
    @Schema(description = "是否续费 0:登记 1:续费")
    private Integer isRenewal;
    /**
     * 有效状态，0:无效 1:有效
     */
    @TableLogic
    private Integer valid;
}