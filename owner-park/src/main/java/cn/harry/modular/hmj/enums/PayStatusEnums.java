package cn.harry.modular.hmj.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Getter
@AllArgsConstructor
public enum PayStatusEnums {
    PENDING("1", "待支付"),
    PAID("2", "已支付"),
    FAILED("3", "已取消"),
    ;

    private final String code;

    private final String message;

    public static String getPayStatusLabel(String payStatus) {
        for (PayStatusEnums status : PayStatusEnums.values()) {
            if (status.getCode().equals(payStatus)) {
                return status.getMessage();
            }
        }
        return "未知";
    }
}
