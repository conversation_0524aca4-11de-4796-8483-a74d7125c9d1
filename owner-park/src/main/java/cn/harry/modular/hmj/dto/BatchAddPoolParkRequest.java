package cn.harry.modular.hmj.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 批量添加车位请求DTO
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
@Schema(description = "批量添加车位请求")
public class BatchAddPoolParkRequest implements Serializable {

    /**
     * 车库ID
     */
    @Schema(description = "车库ID")
    @NotNull(message = "车库ID不能为空")
    private Long garageId;


    /**
     * 车位号列表（逗号分隔或换行分隔）
     */
    @Schema(description = "车位号列表（逗号分隔或换行分隔）")
    @NotBlank(message = "车位号列表不能为空")
    private String parkNames;

    /**
     * 状态：0 空闲 1 占用 2 待支付
     */
    @Schema(description = "状态：0 空闲 1 占用 2 待支付")
    private String status = "0"; // 默认为空闲状态
}
