package cn.harry.modular.hmj.service;

import cn.harry.modular.hmj.domain.RechargeRecord;
import cn.harry.modular.hmj.domain.UserRegistration;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【t_user_registration(用户信息登记表)】的数据库操作Service
* @createDate 2025-07-01 22:22:08
*/
public interface UserRegistrationService extends IService<UserRegistration> {

    /**
     *  更新登记状态 以及车位状态 同步信息到德立云
     *
     * @param record
     */
    void sendMessage(RechargeRecord record);
}
