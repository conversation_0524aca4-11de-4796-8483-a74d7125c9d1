package cn.harry.modular.hmj.service;

import cn.harry.modular.hmj.domain.ParkingLot;
import cn.harry.modular.hmj.vo.GarageVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_parking_lot(停车场信息表)】的数据库操作Service
* @createDate 2025-07-15 19:12:32
*/
public interface ParkingLotService extends IService<ParkingLot> {

    /**
     * 获取车库列表
     * @return
     */
    List<GarageVO> getGarageList();

    /**
     * 根据ID获取车库信息
     * @param id
     * @return
     */
    GarageVO getGarageById(Long id);

    /**
     * 生成车库二维码
     * @param scene 场景值
     * @param pages 页面路径
     * @return base64格式的二维码数据
     */
    String getGarageQrcode(String scene, String pages);
}
