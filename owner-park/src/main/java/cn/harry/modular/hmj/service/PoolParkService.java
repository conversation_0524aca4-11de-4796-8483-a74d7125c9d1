package cn.harry.modular.hmj.service;

import cn.harry.modular.deliyun.req.ParkDelayRequest;
import cn.harry.modular.hmj.domain.PoolPark;
import cn.harry.modular.hmj.dto.BatchAddPoolParkRequest;
import cn.harry.modular.hmj.vo.PoolParkVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 * @description 针对表【t_pool_park(车位信息表)】的数据库操作Service
 * @createDate 2025-06-26 17:23:36
 */
public interface PoolParkService extends IService<PoolPark> {

    // 根据车库同步车位池
    void syncPoolParkByGarage(Long garageId);

    IPage<PoolParkVO> getPageVO(Page<PoolPark> page, PoolPark entity);

    /**
     * 根据车库ID和车位号查询车位号
     *
     * @param garageId
     * @param parkName
     * @return
     */
    String getParkNumByGarageIdAndParkName(Long garageId, String parkName);

    /**
     * 获取未关联的车位列表
     *
     * @param page
     * @return
     */
    IPage<PoolParkVO> getUnassociatedParking(Page<PoolPark> page, String keyword);

    /**
     * 车位延期
     *
     * @param entity
     */
    void parkDelay(ParkDelayRequest entity);


    /**
     * 根据车库ID和车牌号查询车位信息
     *
     * @param garageId
     * @param plateNum
     * @return
     */
    PoolParkVO getByGarageIdAndPlateNum(Long garageId, String plateNum);

    /**
     * 更新车位续费时间
     *
     * @param parkId
     * @param endDate
     */
    void updateRenewal(Long parkId, Long endDate);

    /**
     * 更新车位状态
     *
     * @param parkId
     * @param code
     */
    void updateStatus(Long parkId, String code);

    /**
     * 批量添加车位
     *
     * @param request 批量添加请求
     * @return 成功添加的车位数量
     */
    int batchAdd(BatchAddPoolParkRequest request);

}
