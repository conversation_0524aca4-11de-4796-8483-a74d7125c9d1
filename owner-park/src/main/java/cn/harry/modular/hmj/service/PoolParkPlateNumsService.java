package cn.harry.modular.hmj.service;

import cn.harry.modular.hmj.domain.PoolParkPlateNums;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 * @description 针对表【t_pool_park_plate_nums(车位池关联车位车牌信息表)】的数据库操作Service
 * @createDate 2025-06-30 19:27:01
 */
public interface PoolParkPlateNumsService extends IService<PoolParkPlateNums> {

    /**
     * 根据车库ID和车位池名称查询
     *
     * @param garageId
     * @param poolName
     * @return
     */
    PoolParkPlateNums getByGarageIdAndPoolName(Long garageId, String poolName);

}
