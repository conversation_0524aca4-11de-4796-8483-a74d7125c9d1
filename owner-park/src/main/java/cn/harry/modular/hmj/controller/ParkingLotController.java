package cn.harry.modular.hmj.controller;

import cn.harry.common.annotation.SysLog;
import cn.harry.common.api.R;
import cn.harry.common.enums.BusinessType;
import cn.harry.modular.hmj.domain.ParkingLot;
import cn.harry.modular.hmj.service.ParkingLotService;
import cn.harry.modular.hmj.vo.GarageVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 停车场信息前端控制层
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Tag(name = "停车场信息接口")
@RestController
@RequestMapping("/hmj/parkingLot")
@RequiredArgsConstructor
public class ParkingLotController {

    private final ParkingLotService parkingLotService;

    /**
     * 分页查询列表
     */
    @Operation(summary = "分页查询")
    @PreAuthorize("@ss.hasPermission('t_parking_lot_page')")
    @GetMapping(value = "/page")
    public R<IPage<ParkingLot>> page(@ParameterObject Page<ParkingLot> page, @ParameterObject ParkingLot entity) {
        return R.success(parkingLotService.page(page, Wrappers.lambdaQuery(entity)));
    }

    /**
     * 根据id获取详情
     */
    @Operation(summary = "根据id获取详情")
    @PreAuthorize("@ss.hasPermission('t_parking_lot_get')")
    @GetMapping(value = "/{id}")
    public R<ParkingLot> getById(@PathVariable Long id) {
        return R.success(parkingLotService.getById(id));
    }

    /**
     * 新增
     */
    @Operation(summary = "新增")
    @PreAuthorize("@ss.hasPermission('t_parking_lot_add')")
    @SysLog(title = "t_parking_lot", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Integer> save(@RequestBody ParkingLot entity) {
        return parkingLotService.save(entity) ? R.success() : R.failed();
    }

    /**
     * 更新
     */
    @Operation(summary = "更新")
    @PreAuthorize("@ss.hasPermission('t_parking_lot_edit')")
    @SysLog(title = "t_parking_lot", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Integer> update(@RequestBody ParkingLot entity) {
        return parkingLotService.updateById(entity) ? R.success() : R.failed();
    }

    /**
     * 删除
     */
    @Operation(summary = "删除")
    @PreAuthorize("@ss.hasPermission('t_parking_lot_del')")
    @SysLog(title = "t_parking_lot", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/{ids}")
    public R<Integer> deleteByIds(@Parameter(description = "多个以英文逗号(,)拼接") @PathVariable Long[] ids) {
        return parkingLotService.removeBatchByIds(Arrays.asList(ids)) ? R.success() : R.failed();
    }

    /**
     * 获取车库列表
     */
    @Operation(summary = "获取车库列表")
    @GetMapping(value = "/garage")
    public R<List<GarageVO>> getGarageList() {
        return R.success(parkingLotService.getGarageList());
    }

    @Operation(summary = "根据车库ID 绘制车库二维码")
    @GetMapping(value = "/garage/{id}/qrcode")
    public R<String> getGarageQrcode(@PathVariable Long id,
                                     @RequestParam(value = "scene", defaultValue = "scene") String scene,
                                     @RequestParam(value = "pages", defaultValue = "pages/index/index") String pages) {
        ParkingLot parking = parkingLotService.getById(id);
        if (parking == null) {
            return R.failed("车库不存在");
        }

        //  判断设备是否管理， 判断店铺审核状态
        scene = "garageId=" + parking.getId();
        pages = "pages/index/index";

        String qrcodeData = parkingLotService.getGarageQrcode(scene, pages);
        if (qrcodeData == null) {
            return R.failed("二维码生成失败");
        }
        return R.success(qrcodeData);
    }

}
