package cn.harry.modular.hmj.vo;

import cn.harry.modular.hmj.domain.CarInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 我的车位信息VO
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
@Schema(description = "我的车位信息")
public class MyParkingVO {
    
    /**
     * 车位ID
     */
    @Schema(description = "车位ID")
    private Long id;
    /**
     * 车库ID
     */
    @Schema(description = "车库ID")
    private Long garageId;
    /**
     * 车库名称
     */
    private String garageName;

    /**
     * 车位号
     */
    @Schema(description = "车位号")
    private String parkName;

    /**
     * 车位池名称
     */
    @Schema(description = "车位池名称")
    private String poolName;

    /**
     * 车库名称
     */
    @Schema(description = "车库名称")
    private String pgname;

    /**
     * 车位类型(0:公用车位, 1:私家车位)
     */
    @Schema(description = "车位类型(0:公用车位, 1:私家车位)")
    private Integer parkType;

    /**
     * 有效期开始时间
     */
    @Schema(description = "有效期开始时间")
    private String beginDateStr;

    /**
     * 有效期结束时间
     */
    @Schema(description = "有效期结束时间")
    private String endDateStr;

    /**
     * 过期状态：0-正常，1-即将到期，2-已到期，3-未知
     */
    @Schema(description = "过期状态：0-正常，1-即将到期，2-已到期，3-未知")
    private Integer expireStatus;

    /**
     * 关联的车辆列表
     */
    @Schema(description = "关联的车辆列表")
    private List<CarInfo> cars;

}
