package cn.harry.modular.hmj.service;

import cn.harry.modular.hmj.vo.OwnerVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
public interface OwnerService {

    IPage<OwnerVO> getPage(Page<OwnerVO> page, OwnerVO vo);

    OwnerVO getUserById(Long id);

    void associateParking(Long userId, List<Long> parkingIds);

    /**
     * 解绑车位
     *
     * @param userId 用户ID
     * @param parkingIds 车位ID列表
     */
    void unbindParking(Long userId, List<Long> parkingIds);

}
