package cn.harry.modular.hmj.mapper;

import cn.harry.modular.hmj.vo.OwnerVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Mapper
public interface OwnerMapper extends BaseMapper<OwnerVO> {

    IPage<OwnerVO> getPage(Page<OwnerVO> page, @Param("vo") OwnerVO vo);

    OwnerVO getUserById(@Param("id") Long id);

}
