package cn.harry.modular.hmj.domain;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 车位信息表
 *
 * @TableName t_pool_park
 */
@TableName(value = "t_pool_park")
@Data
public class PoolPark implements Serializable {

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @Schema(description = "id")
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 车库ID
     */
    @Schema(description = "车库ID")
    private Long garageId;
    /**
     * 所属车位池
     */
    @Schema(description = "所属车位池")
    private String poolName;

    /**
     * 所属车库名称
     */
    @Schema(description = "所属车库名称")
    private String pgname;

    /**
     * 车位名称、车位号
     */
    @Schema(description = "车位名称、车位号")
    private String parkName;

    /**
     * 车位类型(0:公用车位, 1:私家车位)
     */
    @Schema(description = "车位类型(0:公用车位, 1:私家车位)")
    private Integer parkType;

    /**
     * 有效期开始时间（时间戳，单位:秒）
     */
    @Schema(description = "有效期开始时间（时间戳，单位:秒）")
    private Long beginDate;

    /**
     * 有效期结束时间（时间戳，单位:秒）
     */
    @Schema(description = "有效期结束时间（时间戳，单位:秒）")
    private Long endDate;

    /**
     * 创建时间（yyyy-MM-dd HH:mm:ss）
     */
    @Schema(description = "创建时间（yyyy-MM-dd HH:mm:ss）")
    private String createDate;

    /**
     * 最后修改时间（yyyy-MM-dd HH:mm:ss）
     */
    @Schema(description = "最后修改时间（yyyy-MM-dd HH:mm:ss）")
    private String lastUpdateDate;

    /**
     * 车位满缴费方式(1：先出收费，2：后进收费)
     */
    @Schema(description = "车位满缴费方式")
    private Byte chargeType;
    /**
     * 车位池满统计规则（1：按停车场统计 2：按车库统计）
     */
    @Schema(description = "车位池满统计规则")
    private Byte fullMode;
    /**
     * 总车位数
     */
    @Schema(description = "总车位数")
    private Integer totalNum;
    /**
     * 有效车位数
     */
    @Schema(description = "有效车位数")
    private Integer validNum;
    /**
     * 在场车辆数
     */
    @Schema(description = "在场车辆数")
    private Integer parkedNum;
    /**
     * 剩余车位数
     */
    @Schema(description = "剩余车位数")
    private Integer emptyNum;
    /**
     * 绑定车牌号
     */
    @Schema(description = "绑定的车牌号")
    private String plateNums;
    /**
     * 所属楼栋单元房铺
     */
    @Schema(description = "所属楼栋单元房铺")
    private String hourseAddr;
    /**
     * 管理员姓名
     */
    @Schema(description = "管理员姓名")
    private String pname;
    /**
     * 管理员电话
     */
    @Schema(description = "管理员电话")
    private String mobile;
    /**
     * 免费换车时间(单位分钟)
     */
    @Schema(description = "免费换车时间(单位分钟)")
    private Integer freeTime;
    /**
     * 同步标识 0 未同步 1 已同步
     */
    @Schema(description = "同步标识 0 未同步 1 已同步")
    private String syncStatus;
    /**
     * 最后同步时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    @Schema(description = "最后一次同步时间")
    private Date syncLastDate;
    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private Long userId;
    /**
     * 状态：0 空闲 1 占用 2 待支付
     */
    @Schema(description = "状态：0 空闲 1 占用 2 待支付")
    private String status;
}