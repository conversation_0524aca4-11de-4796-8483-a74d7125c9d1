package cn.harry.modular.hmj.service;

import cn.harry.modular.hmj.param.CalculateFeeParam;
import cn.harry.modular.hmj.param.RegistrationParam;
import cn.harry.modular.hmj.param.SelectParkingSpotParam;
import cn.harry.modular.hmj.vo.CalculateFeeVO;
import cn.harry.modular.hmj.vo.SelectParkingSpotVO;

import java.util.List;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
public interface RegistrationService {
    /**
     * 登记
     *
     * @param registrationParam
     * @return
     */
    Long add(RegistrationParam registrationParam);

    /**
     * 计算费用
     *
     * @param registrationParam
     * @return
     */
    CalculateFeeVO calculateFee(CalculateFeeParam registrationParam);

    /**
     * 选择可用车位
     *
     * @param param
     * @return
     */
    List<SelectParkingSpotVO> selectParkingSpot(SelectParkingSpotParam param);
}
