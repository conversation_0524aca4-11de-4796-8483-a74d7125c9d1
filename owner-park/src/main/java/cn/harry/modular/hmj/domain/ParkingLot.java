package cn.harry.modular.hmj.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;

import lombok.Data;

/**
 * 停车场信息表
 *
 * @TableName t_parking_lot
 */
@TableName(value = "t_parking_lot")
@Data
public class ParkingLot {
    /**
     * 停车场ID
     */
    @TableId
    private Long id;

    /**
     * 停车场名称
     */
    private String pname;

    /**
     * 所在区（道里区等）
     */
    private String parea;

    /**
     * 城市（哈尔滨市等）
     */
    private String city;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 经度
     */
    private BigDecimal lng;

    /**
     * 纬度
     */
    private BigDecimal lat;

    /**
     * 总车位数量
     */
    private Integer parkTotalCount;

    /**
     * 空车位数量
     */
    private Integer parkEmptyCount;

    /**
     * 空车位信息更新时间戳
     */
    private Long parkEmptyUpdateDate;

    /**
     * 是否在线（1：在线，0：离线）
     */
    private Integer isOnline;

    /**
     * 出场延迟时间（分钟）
     */
    private Integer outDelay;

    /**
     * 项目唯一编码
     */
    private String commKey;
    /**
     * 状态 0 禁用 1 启用
     */
    private Integer status;
}