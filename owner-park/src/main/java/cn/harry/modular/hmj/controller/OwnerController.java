package cn.harry.modular.hmj.controller;

import cn.harry.common.annotation.SysLog;
import cn.harry.common.api.R;
import cn.harry.common.enums.BusinessType;
import cn.harry.modular.hmj.param.AssociateParkingRequest;
import cn.harry.modular.hmj.service.OwnerService;
import cn.harry.modular.hmj.vo.OwnerVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Slf4j
@Tag(name = "车主管理")
@RestController
@RequestMapping("/hmj/owner")
@RequiredArgsConstructor
public class OwnerController {
    private final OwnerService ownerService;

    @Operation(summary = "分页查询")
    @GetMapping(value = "/page")
    public R<IPage<OwnerVO>> page(@ParameterObject Page<OwnerVO> page, @ParameterObject OwnerVO vo) {
        return R.success(ownerService.getPage(page, vo));
    }

    @Operation(summary = "详情")
    @GetMapping(value = "/{id}")
    public R<OwnerVO> getById(@PathVariable Long id) {
        return R.success(ownerService.getUserById(id));
    }


    @Operation(summary = "关联车位")
    @SysLog(title = "用户管理", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/{userId}/parking/associate")
    public R<String> associateParking(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @RequestBody AssociateParkingRequest request) {
        try {
            ownerService.associateParking(userId, request.getParkingIds());
            return R.success("关联车位成功");
        } catch (Exception e) {
            log.error("关联车位失败", e);
            return R.failed("关联车位失败：" + e.getMessage());
        }
    }

    @Operation(summary = "解绑车位")
    @SysLog(title = "用户管理", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/{userId}/parking/unbind")
    public R<String> unbindParking(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @RequestBody AssociateParkingRequest request) {
        try {
            ownerService.unbindParking(userId, request.getParkingIds());
            return R.success("解绑车位成功");
        } catch (Exception e) {
            log.error("解绑车位失败", e);
            return R.failed("解绑车位失败：" + e.getMessage());
        }
    }

}
