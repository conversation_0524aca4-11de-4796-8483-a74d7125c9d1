package cn.harry.modular.hmj.service.impl;

import cn.harry.modular.hmj.domain.RechargeRecord;
import cn.harry.modular.hmj.domain.UserRegistration;
import cn.harry.modular.hmj.enums.PayStatusEnums;
import cn.harry.modular.hmj.enums.PoolParkStatusEnums;
import cn.harry.modular.hmj.mapper.RechargeRecordMapper;
import cn.harry.modular.hmj.service.PoolParkService;
import cn.harry.modular.hmj.service.RechargeRecordService;
import cn.harry.modular.hmj.service.UserRegistrationService;
import cn.harry.modular.hmj.vo.PaymentRecordVO;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 充值记录服务实现类
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Service
@RequiredArgsConstructor
public class RechargeRecordServiceImpl extends ServiceImpl<RechargeRecordMapper, RechargeRecord> implements RechargeRecordService {
    private final PoolParkService poolParkService;
    private final UserRegistrationService userRegistrationService;


    @Override
    public void saveRecord(String outTradeNo, UserRegistration userRegistration, String subMchId) {
        RechargeRecord record = new RechargeRecord();
        record.setGarageId(userRegistration.getGarageId());
        record.setParkId(userRegistration.getParkId());
        record.setUserId(userRegistration.getUserId());
        record.setRegistrationId(userRegistration.getId());
        record.setPname(userRegistration.getPname());
        record.setOpenid(userRegistration.getOpenid());
        record.setPhone(userRegistration.getMobile());
        record.setPlateNum(userRegistration.getPlateNum());
        record.setParkNum(userRegistration.getParkNum());
        record.setRechargeAmount(userRegistration.getShouldPay());
        record.setRechargeType(userRegistration.getBillingType());
        record.setRechargeSn(outTradeNo);
        record.setPayStatus("1");
        record.setSubMchid(subMchId);
        record.setIsRenewal(userRegistration.getIsRenewal());
        save(record);
    }

    @Override
    public List<PaymentRecordVO> getMyPaymentRecords(Long userId) {
        // 查询用户的充值记录
        LambdaQueryWrapper<RechargeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RechargeRecord::getUserId, userId).orderByDesc(RechargeRecord::getCreateTime);

        return list(queryWrapper).stream()
                .map(this::convertToVO)
                .toList();
    }

    @Override
    public void checkPaymentStatus() {
        // 查询30分钟前的待支付记录
        LambdaQueryWrapper<RechargeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RechargeRecord::getPayStatus, PayStatusEnums.PENDING.getCode())
                .lt(RechargeRecord::getCreateTime, DateUtil.offsetMinute(DateUtil.date(), -30));
        List<RechargeRecord> records = list(queryWrapper);
        if (records.isEmpty()) {
            return;
        }

        for (RechargeRecord record : records) {
            record.setPayStatus(PayStatusEnums.FAILED.getCode());
            updateById(record);
            // 是否登记 登记释放车位
            if (record.getIsRenewal() != null && 0 == record.getIsRenewal()) {
                poolParkService.updateStatus(record.getParkId(), PoolParkStatusEnums.FREE.getCode());
            }
            userRegistrationService.update(Wrappers.<UserRegistration>lambdaUpdate()
                    .eq(UserRegistration::getId, record.getRegistrationId())
                    .set(UserRegistration::getStatus, PayStatusEnums.FAILED.getCode()));
        }

    }

    /**
     * 转换为PaymentRecordVO
     */
    private PaymentRecordVO convertToVO(RechargeRecord record) {
        PaymentRecordVO vo = new PaymentRecordVO();
        BeanUtil.copyProperties(record, vo);
        vo.setPayStatusLabel(PayStatusEnums.getPayStatusLabel(record.getPayStatus()));
        return vo;
    }
}
