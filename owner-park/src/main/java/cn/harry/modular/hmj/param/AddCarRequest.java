package cn.harry.modular.hmj.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 添加车辆请求参数
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
@Schema(description = "添加车辆请求参数")
public class AddCarRequest {

    /**
     * 车位ID
     */
    @Schema(description = "车位ID")
    private Long parkingId;

    /**
     * 车牌号
     */
    @Schema(description = "车牌号")
    private String plateNum;

    /**
     * 车型ID
     */
    @Schema(description = "车型ID")
    private Long carTypeId;

    /**
     * 车主电话
     */
    @Schema(description = "车主电话")
    private String ownerPhone;
}
