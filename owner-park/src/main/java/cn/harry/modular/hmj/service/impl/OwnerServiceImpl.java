package cn.harry.modular.hmj.service.impl;

import cn.harry.common.exception.ApiException;
import cn.harry.modular.hmj.domain.PoolPark;
import cn.harry.modular.hmj.mapper.OwnerMapper;
import cn.harry.modular.hmj.mapper.PoolParkMapper;
import cn.harry.modular.hmj.service.OwnerService;
import cn.harry.modular.hmj.utils.HmjUtils;
import cn.harry.modular.hmj.vo.OwnerVO;
import cn.harry.modular.system.domain.SysUser;
import cn.harry.modular.system.mapper.SysUserMapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OwnerServiceImpl extends ServiceImpl<OwnerMapper, OwnerVO> implements OwnerService {

    private final PoolParkMapper poolParkMapper;
    private final SysUserMapper sysUserMapper;

    @Override
    public IPage<OwnerVO> getPage(Page<OwnerVO> page, OwnerVO vo) {
        IPage<OwnerVO> result = baseMapper.getPage(page, vo);
        // 处理车牌号列表
        result.getRecords().forEach(this::processPlateNumbers);
        return result;
    }

    @Override
    public OwnerVO getUserById(Long id) {
        OwnerVO ownerVO = baseMapper.getUserById(id);
        if (ownerVO != null) {
            processPlateNumbers(ownerVO);
        }
        return ownerVO;
    }

    /**
     * 处理车牌号列表和到期状态
     */
    private void processPlateNumbers(OwnerVO ownerVO) {
        if (ownerVO.getParkingList() != null && !ownerVO.getParkingList().isEmpty()) {
            // 处理车牌号列表
            List<String> plateNumbers = ownerVO.getParkingList().stream()
                    .map(OwnerVO.ParkingInfo::getPlateNums)
                    .filter(plateNums -> plateNums != null && !plateNums.trim().isEmpty())
                    .flatMap(plateNums -> Stream.of(plateNums.split(",")))
                    .map(String::trim)
                    .distinct()
                    .toList();
            ownerVO.setPlateNumbers(plateNumbers);

            // 处理每个车位的到期状态
            ownerVO.getParkingList().forEach(this::calculateExpireStatus);
        }
    }

    /**
     * 计算车位到期状态
     *
     * @param parkingInfo 车位信息
     */
    private void calculateExpireStatus(OwnerVO.ParkingInfo parkingInfo) {
        String endDateStr = parkingInfo.getEndDateStr();


        if (endDateStr == null || endDateStr.trim().isEmpty()) {
            parkingInfo.setExpireStatus(3); // 未知状态
            return;
        }

        Integer expireStatus = HmjUtils.funExpiryStatus(endDateStr);
        parkingInfo.setExpireStatus(expireStatus);

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void associateParking(Long userId, List<Long> parkingIds) {
        // 验证用户是否存在
        SysUser user = sysUserMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 验证车位ID列表
        if (parkingIds == null || parkingIds.isEmpty()) {
            throw new ApiException("车位ID列表不能为空");
        }

        // 验证车位是否存在
        List<PoolPark> parkings = poolParkMapper.selectByIds(parkingIds);
        if (parkings.size() != parkingIds.size()) {
            throw new ApiException("部分车位不存在");
        }

        // 检查车位是否已被其他用户关联
        for (PoolPark parking : parkings) {
            if (parking.getUserId() != null && !parking.getUserId().equals(userId)) {
                throw new ApiException("车位 " + parking.getParkName() + " 已被其他用户关联");
            }
        }

        // 更新车位的用户关联
        LambdaUpdateWrapper<PoolPark> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(PoolPark::getId, parkingIds)
                .set(PoolPark::getUserId, userId);

        int updateCount = poolParkMapper.update(null, updateWrapper);

        if (updateCount != parkingIds.size()) {
            throw new ApiException("关联车位失败，请稍后重试");
        }

        log.info("用户 {} 成功关联车位: {}", userId, parkingIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unbindParking(Long userId, List<Long> parkingIds) {
        // 验证用户是否存在
        SysUser user = sysUserMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 验证车位ID列表
        if (parkingIds == null || parkingIds.isEmpty()) {
            throw new ApiException("车位ID列表不能为空");
        }

        // 验证车位是否存在且属于该用户
        List<PoolPark> parkings = poolParkMapper.selectByIds(parkingIds);
        if (parkings.size() != parkingIds.size()) {
            throw new ApiException("部分车位不存在");
        }

        // 检查车位是否属于该用户
        for (PoolPark parking : parkings) {
            if (parking.getUserId() == null || !parking.getUserId().equals(userId)) {
                throw new ApiException("车位 " + parking.getParkName() + " 不属于该用户");
            }
        }

        // 解绑车位（将user_id设为null）
        LambdaUpdateWrapper<PoolPark> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(PoolPark::getId, parkingIds)
                    .set(PoolPark::getUserId, null);

        int updateCount = poolParkMapper.update(null, updateWrapper);

        if (updateCount != parkingIds.size()) {
            throw new ApiException("解绑车位失败，请稍后重试");
        }

        log.info("用户 {} 成功解绑车位: {}", userId, parkingIds);
    }

}
