package cn.harry.modular.hmj.controller;

import cn.harry.common.api.R;
import cn.harry.component.security.utils.SecurityUtils;
import cn.harry.modular.hmj.service.RechargeRecordService;
import cn.harry.modular.hmj.vo.PaymentRecordVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 充值记录前端控制层
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Tag(name = "充值记录接口")
@RestController
@RequestMapping("/hmj/rechargeRecord")
@RequiredArgsConstructor
@Slf4j
public class MiniappRechargeRecordController {

    private final RechargeRecordService rechargeRecordService;

    /**
     * 获取我的支付记录
     */
    @Operation(summary = "获取我的支付记录")
    @GetMapping(value = "/my-records")
    public R<List<PaymentRecordVO>> getMyPaymentRecords() {
        try {
            Long userId = SecurityUtils.getUserId();
            if (userId == null) {
                return R.failed("用户未登录");
            }
            List<PaymentRecordVO> records = rechargeRecordService.getMyPaymentRecords(userId);
            return R.success(records);
        } catch (Exception e) {
            log.error("获取支付记录失败", e);
            return R.failed("获取支付记录失败：" + e.getMessage());
        }
    }

}
