package cn.harry.modular.hmj.controller;

import cn.harry.common.api.R;
import cn.harry.modular.hmj.domain.UserRegistration;
import cn.harry.modular.hmj.param.CalculateFeeParam;
import cn.harry.modular.hmj.param.RegistrationParam;
import cn.harry.modular.hmj.param.SelectParkingSpotParam;
import cn.harry.modular.hmj.service.RegistrationService;
import cn.harry.modular.hmj.service.UserRegistrationService;
import cn.harry.modular.hmj.vo.CalculateFeeVO;
import cn.harry.modular.hmj.vo.SelectParkingSpotVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户信息自主登记
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Tag(name = "用户信息自主登记")
@RestController
@RequestMapping("/hmj/registration")
@RequiredArgsConstructor
public class MiniappRegistrationController {
    private final RegistrationService registrationService;
    private final UserRegistrationService userRegistrationService;

    @Operation(summary = "登记")
    @PostMapping(value = "/add")
    public R<Long> add(@RequestBody RegistrationParam registrationParam) {
        return R.success(registrationService.add(registrationParam));
    }

    @Operation(summary = "根据id获取详情")
    @GetMapping(value = "/detail/{id}")
    public R<UserRegistration> getById(@PathVariable Long id) {
        return R.success(userRegistrationService.getById(id));
    }

    @Operation(summary = "计算费用")
    @PostMapping(value = "/calculate-fee")
    public R<CalculateFeeVO> calculateFee(@RequestBody CalculateFeeParam param) {
        return R.success(registrationService.calculateFee(param));
    }

    @Operation(summary = "选择可用车位")
    @PostMapping(value = "/select-parking-spot")
    public R<List<SelectParkingSpotVO>> selectParkingSpot(@RequestBody SelectParkingSpotParam  param) {
        return R.success(registrationService.selectParkingSpot(param));
    }

}
