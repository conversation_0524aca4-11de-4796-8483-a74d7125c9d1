package cn.harry.modular.hmj.service.impl;

import cn.harry.modular.deliyun.req.ParkDelayRequest;
import cn.harry.modular.deliyun.req.SaveParkingSpaceCarRequest;
import cn.harry.modular.deliyun.service.DeliyunService;
import cn.harry.modular.hmj.domain.*;
import cn.harry.modular.hmj.enums.PoolParkStatusEnums;
import cn.harry.modular.hmj.mapper.UserRegistrationMapper;
import cn.harry.modular.hmj.service.CarInfoService;
import cn.harry.modular.hmj.service.ParkingLotService;
import cn.harry.modular.hmj.service.PoolParkService;
import cn.harry.modular.hmj.service.UserRegistrationService;
import cn.harry.modular.hmj.utils.HmjUtils;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_user_registration(用户信息登记表)】的数据库操作Service实现
 * @createDate 2025-07-01 22:22:08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserRegistrationServiceImpl extends ServiceImpl<UserRegistrationMapper, UserRegistration> implements UserRegistrationService {

    private final DeliyunService deliyunService;
    private final ParkingLotService parkingLotService;
    private final CarInfoService carInfoService;
    private final PoolParkService poolParkService;

    @Override
    public void sendMessage(RechargeRecord record) {
        UserRegistration userRegistration = getById(record.getRegistrationId());

        // 查询车库
        if (record.getGarageId() == null) {
            log.error("车库ID为空");
            return;
        }
        ParkingLot parkingLot = parkingLotService.getById(record.getGarageId());
        if (parkingLot == null) {
            log.error("车库不存在");
            return;
        }
        String commKey = parkingLot.getCommKey();
        if (StrUtil.isBlank(commKey)) {
            log.error("车库项目唯一编码为空");
            return;
        }
        // 判断续费还是登记
        if (record.getIsRenewal() == 1) {
            String endDate = userRegistration.getEndDate();
            poolParkService.updateRenewal(userRegistration.getParkId(), HmjUtils.date2Timestamp(endDate, true));
        } else {
            // 登记  封装登记参数
            SaveParkingSpaceCarRequest request = covertRegistrationParams(record, userRegistration);
            deliyunService.saveParkingSpaceCar(request, commKey);

            updateParkInfo(userRegistration, record);
        }

        // 根据缴费到期时间延期
        ParkDelayRequest request = covertRenewalParams(record, userRegistration);
        deliyunService.parkDelay(request, commKey);

        // 更新登记状态
        update(Wrappers.<UserRegistration>lambdaUpdate()
                .eq(UserRegistration::getId, record.getRegistrationId())
                .set(UserRegistration::getStatus, record.getPayStatus()));

    }

    private void updateParkInfo(UserRegistration userRegistration, RechargeRecord record) {
        Long parkId = userRegistration.getParkId();
        PoolPark park = poolParkService.getById(parkId);
        if (park == null) {
            log.error("车位不存在");
            return;
        }
        String plateNum = userRegistration.getPlateNum();

        park.setUserId(userRegistration.getUserId());
        park.setPlateNums(userRegistration.getPlateNum());
        park.setBeginDate(HmjUtils.date2Timestamp(userRegistration.getBeginDate()));
        park.setEndDate(HmjUtils.date2Timestamp(userRegistration.getEndDate(), true));

        park.setStatus(PoolParkStatusEnums.OCCUPIED.getCode());
        poolParkService.updateById(park);


        CarInfo carInfo = carInfoService.getByGarageIdAndPlateNum(park.getGarageId(), plateNum);
        if (carInfo == null) {
            carInfo = new CarInfo();
        }
        carInfo.setCarTypeId(1L); // 先给个默认值
        carInfo.setParkNum(park.getParkName());
        carInfo.setPlateNum(plateNum);
        carInfo.setPname(userRegistration.getPname());
        carInfo.setBeginDate(userRegistration.getBeginDate());
        carInfo.setEndDate(userRegistration.getEndDate());
        carInfo.setGarageId(userRegistration.getGarageId());
        carInfo.setIsRenewal(record.getIsRenewal());
        carInfoService.saveOrUpdate(carInfo);

    }


    private ParkDelayRequest covertRenewalParams(RechargeRecord record, UserRegistration userRegistration) {
        String beginDate = userRegistration.getBeginDate();
        String endDate = userRegistration.getEndDate();

        ParkDelayRequest request = new ParkDelayRequest();
        request.setPoolName(record.getParkNum());
        request.setParkName(record.getParkNum());

        request.setBeginDate(HmjUtils.date2Timestamp(beginDate));
        request.setEndDate(HmjUtils.date2Timestamp(endDate, true));

        request.setPayMode(Byte.valueOf("1"));
        request.setChargeMoney(Double.valueOf(record.getRechargeAmount()));
        return request;
    }


    private SaveParkingSpaceCarRequest covertRegistrationParams(RechargeRecord record, UserRegistration userRegistration) {
        String beginDate = userRegistration.getBeginDate();
        String endDate = userRegistration.getEndDate();

        SaveParkingSpaceCarRequest request = new SaveParkingSpaceCarRequest();

        String poolName = record.getParkNum();
        Byte chargeType = 2; // 车位池满时收费方式(1:先出收费, 2:后进收费)（必填）
        Byte fullMode = 1; // 所属车库名称(如果 fullMode 为 1 时必填, 为 2 时可以不填)
        String pgname = "主车库"; //
        Byte cpType = 0; // 车位池类型(0:默认类型, 1:子母车位)

        request.setPoolName(poolName);
        request.setChargeType(chargeType);
        request.setFullMode(fullMode);
        request.setPgname(pgname);
        request.setCpType(cpType);

        List<CarInfo> carList = carInfoService.list(Wrappers.<CarInfo>lambdaQuery().eq(CarInfo::getParkNum, poolName));

        List<SaveParkingSpaceCarRequest.CarInfo> carRequestList = new ArrayList<>();
        for (CarInfo carInfo : carList) {
            SaveParkingSpaceCarRequest.CarInfo car = new SaveParkingSpaceCarRequest.CarInfo();
            car.setPlateNum(carInfo.getPlateNum());
            car.setCarTypeId(carInfo.getCarTypeId());
            car.setPname(carInfo.getPname());
            carRequestList.add(car);
        }
        String plateNum = userRegistration.getPlateNum();
        // 判断 carList 是否包含登记的车辆
        if (carList.stream().noneMatch(item -> item.getPlateNum().equals(plateNum))) {
            SaveParkingSpaceCarRequest.CarInfo car = new SaveParkingSpaceCarRequest.CarInfo();
            car.setPlateNum(plateNum);
            car.setCarTypeId(1L);
            car.setPname(userRegistration.getPname());
            carRequestList.add(car);
        }

        request.setCarList(carRequestList);

        List<SaveParkingSpaceCarRequest.ParkingSpace> parkList = new ArrayList<>();
        SaveParkingSpaceCarRequest.ParkingSpace parkingSpace = new SaveParkingSpaceCarRequest.ParkingSpace();
        parkingSpace.setParkName(record.getParkNum());
        parkingSpace.setParkType((byte) 2);
        parkingSpace.setBeginDate(HmjUtils.date2Timestamp(beginDate));
        parkingSpace.setEndDate(HmjUtils.date2Timestamp(endDate, true));
        parkingSpace.setChargeMoney(0.0);
        parkList.add(parkingSpace);
        request.setParkList(parkList);
        log.error("调用德立云接口参数:{}", JSONUtil.toJsonStr(request));
        return request;
    }
}




