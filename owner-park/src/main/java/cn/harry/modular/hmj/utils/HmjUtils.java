package cn.harry.modular.hmj.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
public class HmjUtils {

    /**
     * 计算有效期状态
     *
     * @param data 时间字符串
     *             如果 data > 当前时间 并且 data < 当前时间 + 3天 则返回1 表示即将到期 ； data < 当前时间 则返回2 表示已到期
     * @return
     */
    public static Integer funExpiryStatus(String data) {
        if (StrUtil.isBlank(data)) {
            return 0;
        }
        Date dataDate = DateUtil.parse(data);
        Date now = DateUtil.date();
        if (dataDate.getTime() > now.getTime() && dataDate.getTime() < DateUtil.offsetDay(now, 3).getTime()) {
            return 1;
        } else if (dataDate.getTime() < now.getTime()) {
            return 2;
        } else {
            return 0;
        }
    }

    /**
     * 计算有效期状态
     *
     * @param data 10位时间戳
     *             如果 data > 当前时间 并且 data < 当前时间 + 3天 则返回1 表示即将到期 ； data < 当前时间 则返回2 表示已到期
     */
    public static Integer funExpiryStatus(Long data) {
        if (data == null) {
            return 0;
        }
        Date endDateDate = DateUtil.date(data * 1000);
        Date now = DateUtil.date();
        if (endDateDate.getTime() > now.getTime() && endDateDate.getTime() < DateUtil.offsetDay(now, 3).getTime()) {
            return 1;
        } else if (endDateDate.getTime() < now.getTime()) {
            return 2;
        } else {
            return 0;
        }
    }

    /**
     * 10位时间戳 转换成 yyyy-MM-dd
     */
    public static String timestamp2Date(Long timestamp) {
        if (timestamp == null)
            return null;

        // 获取系统默认时区
        ZoneId zoneId = ZoneId.systemDefault();
        // 获取当前时间在该时区下的偏移量
        ZoneOffset offset = zoneId.getRules().getOffset(Instant.ofEpochSecond(timestamp));

        // 使用获取到的偏移量创建LocalDateTime
        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(timestamp, 0, offset);
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * 将日期转换 10位时间戳（默认返回当天开始时间）
     */
    public static Long date2Timestamp(String date) {
        return date2Timestamp(date, false);
    }

    /**
     * 将日期转换 10位时间戳
     *
     * @param date 日期字符串
     * @param endOfDay 是否取当天的结束时间，true：当天结束时间（23:59:59），false：当天开始时间（00:00:00）
     * @return 10位时间戳
     */
    public static Long date2Timestamp(String date, boolean endOfDay) {
        if (StrUtil.isBlank(date)) {
            return null;
        }
        Date parse = DateUtil.parse(date);
        Date targetDate;
        if (endOfDay) {
            // 获取当天的结束时间（23:59:59）
            targetDate = DateUtil.endOfDay(parse);
        } else {
            // 获取当天的开始时间（00:00:00）
            targetDate = DateUtil.beginOfDay(parse);
        }
        return targetDate.getTime() / 1000;
    }
}
