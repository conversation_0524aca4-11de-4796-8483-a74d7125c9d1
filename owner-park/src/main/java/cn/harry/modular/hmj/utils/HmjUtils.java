package cn.harry.modular.hmj.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
public class HmjUtils {

    /**
     * 计算有效期状态
     *
     * @param data 时间字符串
     *             如果 data > 当前时间 并且 data < 当前时间 + 3天 则返回1 表示即将到期 ； data < 当前时间 则返回2 表示已到期
     * @return
     */
    public static Integer funExpiryStatus(String data) {
        if (StrUtil.isBlank(data)) {
            return 0;
        }
        Date dataDate = DateUtil.parse(data);
        Date now = DateUtil.date();
        if (dataDate.getTime() > now.getTime() && dataDate.getTime() < DateUtil.offsetDay(now, 3).getTime()) {
            return 1;
        } else if (dataDate.getTime() < now.getTime()) {
            return 2;
        } else {
            return 0;
        }
    }

    /**
     * 计算有效期状态
     *
     * @param data 10位时间戳
     *             如果 data > 当前时间 并且 data < 当前时间 + 3天 则返回1 表示即将到期 ； data < 当前时间 则返回2 表示已到期
     */
    public static Integer funExpiryStatus(Long data) {
        if (data == null) {
            return 0;
        }
        Date endDateDate = DateUtil.date(data * 1000);
        Date now = DateUtil.date();
        if (endDateDate.getTime() > now.getTime() && endDateDate.getTime() < DateUtil.offsetDay(now, 3).getTime()) {
            return 1;
        } else if (endDateDate.getTime() < now.getTime()) {
            return 2;
        } else {
            return 0;
        }
    }

    /**
     * 10位时间戳 转换成 yyyy-MM-dd
     */
    public static String timestamp2Date(Long timestamp) {
        if (timestamp == null)
            return null;

        // 获取系统默认时区
        ZoneId zoneId = ZoneId.systemDefault();
        // 获取当前时间在该时区下的偏移量
        ZoneOffset offset = zoneId.getRules().getOffset(Instant.ofEpochSecond(timestamp));

        // 使用获取到的偏移量创建LocalDateTime
        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(timestamp, 0, offset);
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * 将日期转换 10位时间戳
     */
    public static Long date2Timestamp(String date) {
        if (StrUtil.isBlank(date)) {
            return null;
        }
        Date parse = DateUtil.parse(date);
        return parse.getTime() / 1000;
    }


    public static void main(String[] args) {
        date2Timestamp("");
    }
}
