package cn.harry.modular.hmj.domain;

import cn.harry.common.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 * 充值记录实体对象
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Getter
@Setter
@TableName("t_recharge_record")
public class RechargeRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 车库ID
     */
    @Schema(description = "车库ID")
    private Long garageId;
    /**
     * 车位ID
     */
    @Schema(description = "车位ID")
    private Long parkId;
    /**
     * 车库名称
     */
    private String garageName;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 登记ID
     */
    @Schema(description = "登记ID")
    private Long registrationId;
    /**
     * 是否续费 0:登记 1:续费
     */
    @Schema(description = "是否续费 0:登记 1:续费")
    private Integer isRenewal;
    /**
     * 车主
     */
    private String pname;
    /**
     * openid
     */
    private String openid;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 车牌号码
     */
    private String plateNum;
    /**
     * 车位号
     */
    private String parkNum;

    /**
     * 二级商户号
     */
    private String subMchid;
    /**
     * 充值流水号
     */
    private String rechargeSn;
    /**
     * 充值金额
     */
    private String rechargeAmount;
    /**
     * 充值类型
     */
    private String rechargeType;
    /**
     * 微信系统中的交易流水号
     */
    private String tradeNo;
    /**
     * 支付状态 1.待支付 2.已支付 3.已取消
     */
    private String payStatus;

    /**
     * 支付完成时间 yyyyMMddHHmmss
     */
    private String timeEnd;
    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态：0->无效；1->有效
     */
    private Integer valid;
}
