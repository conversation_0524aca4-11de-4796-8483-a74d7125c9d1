package cn.harry.modular.hmj.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class CalculateFeeParam {

    // 类型
    @Schema(description = "类型")
    private String billingType;
    // 开始时间
    @Schema(description = "开始时间")
    private String startTime;
    // 选择数
    @Schema(description = "选择数")
    private Integer periodCount;

}
