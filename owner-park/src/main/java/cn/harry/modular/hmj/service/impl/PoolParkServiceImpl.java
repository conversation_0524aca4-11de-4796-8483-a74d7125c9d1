package cn.harry.modular.hmj.service.impl;

import cn.harry.common.exception.ApiException;
import cn.harry.modular.deliyun.req.FindPoolListRequest;
import cn.harry.modular.deliyun.req.FindPoolParkListRequest;
import cn.harry.modular.deliyun.req.ParkDelayRequest;
import cn.harry.modular.deliyun.res.FindPoolListResponse;
import cn.harry.modular.deliyun.res.FindPoolParkListResponse;
import cn.harry.modular.deliyun.service.DeliyunService;
import cn.harry.modular.hmj.domain.ParkingLot;
import cn.harry.modular.hmj.domain.PoolPark;
import cn.harry.modular.hmj.domain.PoolParkPlateNums;
import cn.harry.modular.hmj.dto.BatchAddPoolParkRequest;
import cn.harry.modular.hmj.enums.PoolParkStatusEnums;
import cn.harry.modular.hmj.enums.SyncStatusEnums;
import cn.harry.modular.hmj.mapper.PoolParkMapper;
import cn.harry.modular.hmj.service.ParkingLotService;
import cn.harry.modular.hmj.service.PoolParkPlateNumsService;
import cn.harry.modular.hmj.service.PoolParkService;
import cn.harry.modular.hmj.utils.HmjUtils;
import cn.harry.modular.hmj.vo.GarageVO;
import cn.harry.modular.hmj.vo.PoolParkVO;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_pool_park(车位信息表)】的数据库操作Service实现
 * @createDate 2025-06-26 17:23:36
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PoolParkServiceImpl extends ServiceImpl<PoolParkMapper, PoolPark> implements PoolParkService {
    private final DeliyunService deliyunService;
    private final PoolParkPlateNumsService poolParkPlateNumsService;
    private final ParkingLotService parkingLotService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncPoolParkByGarage(Long garageId) {
        // 先将指定车库的同步状态更新为 0
        update(Wrappers.<PoolPark>lambdaUpdate()
                .eq(PoolPark::getGarageId, garageId)
                .set(PoolPark::getSyncStatus, SyncStatusEnums.NOT_SYNC.getCode()));
        poolParkPlateNumsService.update(Wrappers.<PoolParkPlateNums>lambdaUpdate()
                .eq(PoolParkPlateNums::getGarageId, garageId)
                .set(PoolParkPlateNums::getSyncStatus, SyncStatusEnums.NOT_SYNC.getCode()));

        // 获取指定车库信息
        GarageVO garageVO = parkingLotService.getGarageById(garageId);
        if (garageVO == null) {
            throw new RuntimeException("车库不存在");
        }

        int page = 1;
        int size = 100;
        syncPoolPark(garageVO, page, size);
        syncPoolParkNums(garageVO, page, size);

        // 关联数据到 PoolPark 表中
        associatedData(garageVO);
    }

    @Override
    public void parkDelay(ParkDelayRequest entity) {

        // 查询车库
        if (entity.getGarageId() == null) {
            log.error("车库ID为空");
            return;
        }
        ParkingLot parkingLot = parkingLotService.getById(entity.getGarageId());
        if (parkingLot == null) {
            log.error("车库不存在");
            return;
        }
        String commKey = parkingLot.getCommKey();
        if (StrUtil.isBlank(commKey)) {
            log.error("车库项目唯一编码为空");
            return;
        }
        PoolPark park = getByPoolName(entity.getPoolName());
        if (park == null) {
            throw new ApiException("车位不存在");
        }
        park.setEndDate(entity.getEndDate());
        updateById(park);


        ParkDelayRequest request = new ParkDelayRequest();
        request.setPoolName(entity.getPoolName());
        request.setParkName(entity.getParkName());
        request.setEndDate(entity.getEndDate());
        request.setBeginDate(entity.getBeginDate());
        request.setPayMode(Byte.valueOf("1"));
        request.setChargeMoney(entity.getChargeMoney());
        deliyunService.parkDelay(request, commKey);

    }

    @Override
    public PoolParkVO getByGarageIdAndPlateNum(Long garageId, String plateNum) {
        PoolPark poolPark = getOne(Wrappers.<PoolPark>lambdaQuery()
                .eq(PoolPark::getGarageId, garageId)
                .like(PoolPark::getPlateNums, plateNum)
        );
        if (poolPark != null) {
            PoolParkVO vo = new PoolParkVO();
            BeanUtil.copyProperties(poolPark, vo);
            // 将时间戳转换为日期字符串 yyyy-MM-dd
            vo.setBeginDateStr(HmjUtils.timestamp2Date(poolPark.getBeginDate()));
            vo.setEndDateStr(HmjUtils.timestamp2Date(poolPark.getEndDate()));

            Integer expireStatus = HmjUtils.funExpiryStatus(poolPark.getEndDate());
            vo.setExpireStatus(expireStatus);
            return vo;
        }
        return null;
    }

    @Override
    public void updateRenewal(Long parkId, Long endDate) {
        PoolPark park = getById(parkId);
        if (park == null) {
            throw new ApiException("车位不存在");
        }
        park.setEndDate(endDate);
        updateById(park);
    }

    @Override
    public void updateStatus(Long parkId, String code) {
        update(Wrappers.<PoolPark>lambdaUpdate().eq(PoolPark::getId, parkId).set(PoolPark::getStatus, code));
    }

    @Override
    public IPage<PoolParkVO> getPageVO(Page<PoolPark> page, PoolPark entity) {
        LambdaQueryWrapper<PoolPark> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StrUtil.isNotEmpty(entity.getPoolName()), PoolPark::getPoolName, entity.getPoolName());
        wrapper.like(StrUtil.isNotEmpty(entity.getParkName()), PoolPark::getParkName, entity.getParkName());
        wrapper.like(StrUtil.isNotEmpty(entity.getPname()), PoolPark::getPname, entity.getPname());
        wrapper.like(StrUtil.isNotEmpty(entity.getMobile()), PoolPark::getMobile, entity.getMobile());
        wrapper.like(StrUtil.isNotEmpty(entity.getPlateNums()), PoolPark::getPlateNums, entity.getPlateNums());
        wrapper.eq(ObjectUtil.isNotEmpty(entity.getGarageId()), PoolPark::getGarageId, entity.getGarageId());
        wrapper.eq(StrUtil.isNotEmpty(entity.getStatus()), PoolPark::getStatus, entity.getStatus());

        wrapper.orderByDesc(PoolPark::getId);
        IPage<PoolPark> pageData = page(page, wrapper);
        return pageData.convert(item -> {
            PoolParkVO vo = new PoolParkVO();
            BeanUtil.copyProperties(item, vo);

            // 将时间戳转换为日期字符串 yyyy-MM-dd
            vo.setBeginDateStr(HmjUtils.timestamp2Date(item.getBeginDate()));
            vo.setEndDateStr(HmjUtils.timestamp2Date(item.getEndDate()));

            Integer expireStatus = HmjUtils.funExpiryStatus(item.getEndDate());
            vo.setExpireStatus(expireStatus);
            return vo;
        });
    }

    @Override
    public String getParkNumByGarageIdAndParkName(Long garageId, String parkName) {
        PoolPark poolPark = getOne(Wrappers.<PoolPark>lambdaQuery().eq(PoolPark::getGarageId, garageId).eq(PoolPark::getParkName, parkName));

        if (poolPark != null) {
            String plateNums = poolPark.getPlateNums();
            if (plateNums != null) {
                return poolPark.getParkName();
            }
        }
        return null;
    }

    @Override
    public IPage<PoolParkVO> getUnassociatedParking(Page<PoolPark> page, String keyword) {
        LambdaQueryWrapper<PoolPark> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PoolPark::getUserId, 0).or().isNull(PoolPark::getUserId);
        if (StrUtil.isNotEmpty(keyword)) {
            wrapper.and(i -> i.like(StrUtil.isNotEmpty(keyword), PoolPark::getParkName, keyword)
                    .or().like(StrUtil.isNotEmpty(keyword), PoolPark::getPoolName, keyword)
                    .or().like(StrUtil.isNotEmpty(keyword), PoolPark::getPlateNums, keyword));
        }
        wrapper.orderByDesc(PoolPark::getCreateDate);
        IPage<PoolPark> pageData = page(page, wrapper);
        return pageData.convert(item -> {
            PoolParkVO vo = new PoolParkVO();
            BeanUtil.copyProperties(item, vo);

            // 将时间戳转换为日期字符串 yyyy-MM-dd
            vo.setBeginDateStr(HmjUtils.timestamp2Date(item.getBeginDate()));
            vo.setEndDateStr(HmjUtils.timestamp2Date(item.getEndDate()));

            Integer expireStatus = HmjUtils.funExpiryStatus(item.getEndDate());
            vo.setExpireStatus(expireStatus);
            return vo;
        });
    }

    private void syncPoolPark(GarageVO lot, int page, int size) {
        FindPoolParkListRequest findPoolParkListRequest = new FindPoolParkListRequest();
        findPoolParkListRequest.setPage(page);
        findPoolParkListRequest.setSize(size);
        List<FindPoolParkListResponse> res = deliyunService.findPoolParkList(findPoolParkListRequest, lot.getCommKey());
        if (res != null && !res.isEmpty()) {
            for (FindPoolParkListResponse item : res) {
                String poolName = item.getPoolName();
                PoolPark poolPark = getByPoolName(poolName);
                if (poolPark == null) {
                    poolPark = new PoolPark();
                }
                poolPark.setGarageId(lot.getId());

                poolPark.setPoolName(item.getPoolName());
                poolPark.setPgname(item.getPgname());
                poolPark.setParkName(item.getParkName());
                poolPark.setParkType(item.getParkType());
                poolPark.setBeginDate(item.getBeginDate());
                poolPark.setEndDate(item.getEndDate());
                poolPark.setCreateDate(item.getCreateDate());
                poolPark.setLastUpdateDate(item.getLastUpdateDate());

                poolPark.setSyncStatus(SyncStatusEnums.SYNC.getCode());
                poolPark.setSyncLastDate(DateUtil.date());

                saveOrUpdate(poolPark);
            }
            syncPoolPark(lot, page + 1, size);
        }
    }

    private PoolPark getByPoolName(String poolName) {
        return getOne(Wrappers.<PoolPark>lambdaQuery().eq(PoolPark::getPoolName, poolName));
    }


    @Transactional(rollbackFor = Exception.class)
    protected void associatedData(GarageVO lot) {
        List<PoolPark> list = list(Wrappers.<PoolPark>lambdaQuery().eq(PoolPark::getGarageId, lot.getId()));
        for (PoolPark item : list) {
            String poolName = item.getPoolName();
            PoolParkPlateNums poolParkPlateNums = poolParkPlateNumsService.getByGarageIdAndPoolName(lot.getId(), poolName);
            if (poolParkPlateNums != null) {
                item.setPoolName(poolName);
                item.setChargeType(poolParkPlateNums.getChargeType());
                item.setFullMode(poolParkPlateNums.getFullMode());
                item.setTotalNum(poolParkPlateNums.getTotalNum());
                item.setValidNum(poolParkPlateNums.getValidNum());
                item.setParkedNum(poolParkPlateNums.getParkedNum());
                item.setEmptyNum(poolParkPlateNums.getEmptyNum());
                item.setPlateNums(poolParkPlateNums.getPlateNums());
                item.setHourseAddr(poolParkPlateNums.getHourseAddr());
                item.setPname(poolParkPlateNums.getPname());
                item.setMobile(poolParkPlateNums.getMobile());
                item.setFreeTime(poolParkPlateNums.getFreeTime());

                if (StrUtil.isNotEmpty(poolParkPlateNums.getPlateNums())) {
                    item.setStatus(PoolParkStatusEnums.OCCUPIED.getCode());
                } else {
                    item.setStatus(PoolParkStatusEnums.FREE.getCode());
                }
            }
        }
        updateBatchById(list);
    }

    private void syncPoolParkNums(GarageVO lot, int page, int size) {
        FindPoolListRequest findPoolParkListRequest = new FindPoolListRequest();

        findPoolParkListRequest.setPage(page);
        findPoolParkListRequest.setSize(size);
        List<FindPoolListResponse> res = deliyunService.findPoolList(findPoolParkListRequest, lot.getCommKey());
        if (res != null && !res.isEmpty()) {

            for (FindPoolListResponse response : res) {
                String poolName = response.getPoolName();

                PoolParkPlateNums poolParkPlateNums = poolParkPlateNumsService.getByGarageIdAndPoolName(lot.getId(), poolName);
                if (poolParkPlateNums == null) {
                    poolParkPlateNums = new PoolParkPlateNums();
                }
                poolParkPlateNums.setGarageId(lot.getId());
                poolParkPlateNums.setPoolName(response.getPoolName());
                poolParkPlateNums.setChargeType(response.getChargeType());
                poolParkPlateNums.setFullMode(response.getFullMode());
                poolParkPlateNums.setTotalNum(response.getTotalNum());
                poolParkPlateNums.setValidNum(response.getValidNum());
                poolParkPlateNums.setParkedNum(response.getParkedNum());
                poolParkPlateNums.setEmptyNum(response.getEmptyNum());
                poolParkPlateNums.setPlateNums(response.getPlateNums());
                poolParkPlateNums.setHourseAddr(response.getHourseAddr());
                poolParkPlateNums.setPname(response.getPname());
                poolParkPlateNums.setMobile(response.getMobile());
                poolParkPlateNums.setFreeTime(response.getFreeTime());

                poolParkPlateNums.setSyncStatus(SyncStatusEnums.SYNC.getCode());
                poolParkPlateNums.setSyncLastDate(DateUtil.date());
                poolParkPlateNumsService.saveOrUpdate(poolParkPlateNums);

            }
            syncPoolParkNums(lot, page + 1, size);

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchAdd(BatchAddPoolParkRequest request) {
        // 解析车位号列表（支持逗号分隔或换行分隔）
        String[] parkNames = request.getParkNames()
                .replaceAll("\\r\\n", "\n") // 统一换行符
                .replaceAll("\\r", "\n")
                .split("[,，\n]"); // 支持中英文逗号和换行分隔

        List<PoolPark> poolParks = new ArrayList<>();
        int successCount = 0;

        for (String parkName : parkNames) {
            parkName = parkName.trim();
            if (StrUtil.isBlank(parkName)) {
                continue; // 跳过空字符串
            }

            // 检查车位是否已存在
            PoolPark existingPark = getOne(Wrappers.<PoolPark>lambdaQuery()
                    .eq(PoolPark::getGarageId, request.getGarageId())
                    .eq(PoolPark::getPoolName, parkName)
                    .eq(PoolPark::getParkName, parkName));

            if (existingPark != null) {
                log.warn("车位已存在，跳过：车位池={}, 车位号={}",parkName, parkName);
                continue;
            }

            // 创建新车位
            PoolPark poolPark = new PoolPark();
            poolPark.setGarageId(request.getGarageId());
            poolPark.setPoolName(parkName);
            poolPark.setParkName(parkName);
            poolPark.setStatus(request.getStatus());
            poolPark.setSyncStatus(SyncStatusEnums.NOT_SYNC.getCode());


            poolParks.add(poolPark);
        }

        // 批量保存
        if (!poolParks.isEmpty()) {
            saveBatch(poolParks);
            successCount = poolParks.size();
            log.info("批量添加车位成功，共添加{}个车位", successCount);
        }

        return successCount;
    }
}




