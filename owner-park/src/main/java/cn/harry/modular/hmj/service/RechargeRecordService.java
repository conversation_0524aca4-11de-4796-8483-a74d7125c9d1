package cn.harry.modular.hmj.service;

import cn.harry.modular.hmj.domain.RechargeRecord;
import cn.harry.modular.hmj.domain.UserRegistration;
import cn.harry.modular.hmj.vo.PaymentRecordVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 充值记录服务类
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
public interface RechargeRecordService extends IService<RechargeRecord> {

    /**
     * 保存充值记录
     *
     * @param outTradeNo
     * @param userRegistration
     * @param subMchId
     */
    void saveRecord(String outTradeNo, UserRegistration userRegistration, String subMchId);

    /**
     * 获取用户的支付记录
     *
     * @param userId 用户ID
     * @return 支付记录列表
     */
    List<PaymentRecordVO> getMyPaymentRecords(Long userId);


    /**
     * 检查支付状态
     */
    void checkPaymentStatus();
}
