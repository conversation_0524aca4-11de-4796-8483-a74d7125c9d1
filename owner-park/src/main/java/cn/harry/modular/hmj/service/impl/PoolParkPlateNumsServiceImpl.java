package cn.harry.modular.hmj.service.impl;

import cn.harry.modular.hmj.domain.PoolParkPlateNums;
import cn.harry.modular.hmj.mapper.PoolParkPlateNumsMapper;
import cn.harry.modular.hmj.service.PoolParkPlateNumsService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【t_pool_park_plate_nums(车位池关联车位车牌信息表)】的数据库操作Service实现
 * @createDate 2025-06-30 19:27:01
 */
@Service
@RequiredArgsConstructor
public class PoolParkPlateNumsServiceImpl extends ServiceImpl<PoolParkPlateNumsMapper, PoolParkPlateNums>
        implements PoolParkPlateNumsService {

    @Override
    public PoolParkPlateNums getByGarageIdAndPoolName(Long garageId, String poolName) {
        return getOne(Wrappers.<PoolParkPlateNums>lambdaQuery()
                .eq(PoolParkPlateNums::getGarageId, garageId)
                .eq(PoolParkPlateNums::getPoolName, poolName));
    }
}




