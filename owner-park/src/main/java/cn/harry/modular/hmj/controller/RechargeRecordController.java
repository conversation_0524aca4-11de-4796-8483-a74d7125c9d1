package cn.harry.modular.hmj.controller;

import cn.harry.common.annotation.SysLog;
import cn.harry.common.api.R;
import cn.harry.common.base.BaseEntity;
import cn.harry.common.enums.BusinessType;
import cn.harry.modular.hmj.domain.RechargeRecord;
import cn.harry.modular.hmj.service.RechargeRecordService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * 充值记录前端控制层
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Tag(name = "充值记录接口")
@RestController
@RequestMapping("/hmj/rechargeRecord")
@RequiredArgsConstructor
@Slf4j
public class RechargeRecordController  {

    private final RechargeRecordService rechargeRecordService;

    /**
     * 分页查询列表
     */
    @Operation(summary = "分页查询")
    @PreAuthorize("@ss.hasPermission('t_recharge_record_page')")
    @GetMapping(value = "/page")
    public R<IPage<RechargeRecord>> page(@ParameterObject Page<RechargeRecord> page,@ParameterObject  RechargeRecord entity) {
        return R.success(rechargeRecordService.page(page, Wrappers.lambdaQuery(entity).orderByDesc(BaseEntity::getId)));
    }

    /**
     * 根据id获取详情
     */
    @Operation(summary = "根据id获取详情")
    @PreAuthorize("@ss.hasPermission('t_recharge_record_get')")
    @GetMapping(value = "/{id}")
    public R<RechargeRecord> getById(@PathVariable Long id) {
        return R.success(rechargeRecordService.getById(id));
    }

    /**
     * 新增
     */
    @Operation(summary = "新增")
    @PreAuthorize("@ss.hasPermission('t_recharge_record_add')")
    @SysLog(title = "t_recharge_record", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Integer> save(@RequestBody RechargeRecord entity) {
        return rechargeRecordService.save(entity) ? R.success() : R.failed();
    }

    /**
     * 更新
     */
    @Operation(summary = "更新")
    @PreAuthorize("@ss.hasPermission('t_recharge_record_edit')")
    @SysLog(title = "t_recharge_record", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Integer> update(@RequestBody RechargeRecord entity) {
        return rechargeRecordService.updateById(entity) ? R.success() : R.failed();
    }

    /**
     * 删除
     */
    @Operation(summary = "删除")
    @PreAuthorize("@ss.hasPermission('t_recharge_record_del')")
    @SysLog(title = "t_recharge_record", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/{ids}")
    public R<Integer> deleteByIds(@Parameter(description = "多个以英文逗号(,)拼接") @PathVariable Long[] ids) {
        return rechargeRecordService.removeBatchByIds(Arrays.asList(ids)) ? R.success() : R.failed();
    }
}
