package cn.harry.modular.wx.wxpay.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class CreateRechargeOrderParam implements Serializable {
    /**
     * openid
     */
    @Schema(description = "openid")
    private String openid;
    /**
     * 登记ID
     */
    @Schema(description = "登记ID")
    private Long registrationId;
    /**
     * 支付记录ID
     */
    @Schema(description = "支付记录ID")
    private Long rechargeId;

    @Serial
    private static final long serialVersionUID = 1L;
}
