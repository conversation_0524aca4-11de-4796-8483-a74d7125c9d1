package cn.harry.modular.wx.wxpay.service.impl;

import cn.harry.common.exception.ApiException;
import cn.harry.modular.hmj.domain.RechargeRecord;
import cn.harry.modular.hmj.domain.UserRegistration;
import cn.harry.modular.hmj.enums.PayStatusEnums;
import cn.harry.modular.hmj.service.RechargeRecordService;
import cn.harry.modular.hmj.service.UserRegistrationService;
import cn.harry.modular.wx.config.property.WxPayProperties;
import cn.harry.modular.wx.wxpay.param.CreateRechargeOrderParam;
import cn.harry.modular.wx.wxpay.service.PayService;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.binarywang.wxpay.bean.ecommerce.PartnerTransactionsRequest;
import com.github.binarywang.wxpay.bean.ecommerce.PartnerTransactionsResult;
import com.github.binarywang.wxpay.bean.ecommerce.TransactionsResult;
import com.github.binarywang.wxpay.bean.ecommerce.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Slf4j
@Service
@AllArgsConstructor
public class PayServiceImpl implements PayService {
    private final WxPayService wxPayService;
    private final WxPayProperties wxPayProperties;
    private final RechargeRecordService rechargeRecordService;
    private final UserRegistrationService userRegistrationService;

    /**
     * 微信小程序 通知地址
     */
    private final static String APPLET_NOTIFY_PATH = "applet/wxpay/notify/order";

    @Override
    public TransactionsResult.JsapiResult createOrder(CreateRechargeOrderParam param) { // 商户订单号
        String outTradeNo = IdUtil.objectId().toUpperCase();

        // 店铺是否启用二级商户
        String subMchId = wxPayProperties.getSubMchId();
        // 二级appid
        String subAppId = wxPayProperties.getSubAppId();

        try {
            String openId = param.getOpenid();
            Long registrationId = param.getRegistrationId();
            Long rechargeId = param.getRechargeId();
            UserRegistration userRegistration = userRegistrationService.getById(registrationId);
            if (userRegistration == null) {
                log.error("用户登记信息不存在，registrationId:{}", registrationId);
                return null;
            }
            // 获取续费金额
            String rechargeAmount = userRegistration.getShouldPay();

            if (rechargeId != null) {
                RechargeRecord record = rechargeRecordService.getById(rechargeId);
                if (record != null) {
                    outTradeNo = record.getRechargeSn();
                }
            }


            // 封装下单参数
            PartnerTransactionsRequest request = convertPayUnifiedOrderV3(outTradeNo, openId, String.valueOf(registrationId), subMchId, subAppId, rechargeAmount);

            if (rechargeId == null){
                // 保存充值记录
                rechargeRecordService.saveRecord(outTradeNo, userRegistration, subMchId);
            }

            // 服务商模式普通支付
            return wxPayService.getEcommerceService().partnerTransactions(TradeTypeEnum.JSAPI, request);
        } catch (WxPayException e) {
            log.error("微信支付下单失败，outTradeNo：{}，e:{}", outTradeNo, e.getMessage());
        }
        return null;
    }


    @Override
    public void saveOrderNotify(PartnerTransactionsResult notifyResult) {
        String outTradeNo = notifyResult.getOutTradeNo();
        log.info("微信支付回调通知，outTradeNo：{}", outTradeNo);
        RechargeRecord record = rechargeRecordService.getOne(Wrappers.<RechargeRecord>lambdaQuery().eq(RechargeRecord::getRechargeSn, outTradeNo));
        if (record == null) {
            log.error("充值记录不存在，outTradeNo:{}", outTradeNo);
            throw new ApiException("支付记录不存在");
        }
        if (PayStatusEnums.PAID.getCode().equals(record.getPayStatus())) {
            log.error("支付记录已处理，outTradeNo：{}", outTradeNo);
            throw new ApiException("支付记录已处理");
        }
        // 更新 支付记录 信息
        record.setTradeNo(notifyResult.getTransactionId());
        record.setTimeEnd(notifyResult.getSuccessTime());
        record.setSubMchid(notifyResult.getSubMchid());
        record.setPayStatus(PayStatusEnums.PAID.getCode());
        rechargeRecordService.updateById(record);

        // 更新登记状态 以及车位状态 同步信息到德立云
        userRegistrationService.sendMessage(record);

    }

    /**
     * @param outTradeNo     交易流水号
     * @param openId         spOpenId
     * @param description    描述
     * @param subMchId       二级商户ID
     * @param subAppId       二级商户 appid
     * @param rechargeAmount 充值金额（元）
     * @return
     */
    private PartnerTransactionsRequest convertPayUnifiedOrderV3(String outTradeNo, String openId, String description, String subMchId, String subAppId, String rechargeAmount) {
        PartnerTransactionsRequest result = new PartnerTransactionsRequest();

        result.setSpAppid(wxPayProperties.getAppId());
        result.setSpMchid(wxPayProperties.getMchId());

        result.setSubAppid(subAppId);
        result.setSubMchid(subMchId);
        result.setOutTradeNo(outTradeNo);
        result.setDescription(description);
        result.setNotifyUrl(wxPayProperties.getNotifyDomain() + PayServiceImpl.APPLET_NOTIFY_PATH);
        // 需要将元 转为 分
        Integer total = new BigDecimal(rechargeAmount).multiply(new BigDecimal(100)).setScale(0, RoundingMode.DOWN).intValue();
        PartnerTransactionsRequest.Amount amount = new PartnerTransactionsRequest.Amount();
        amount.setTotal(total);
        amount.setCurrency("CNY");
        result.setAmount(amount);

        PartnerTransactionsRequest.Payer payer = new PartnerTransactionsRequest.Payer();
        payer.setSubOpenid(openId);
        result.setPayer(payer);

        return result;
    }

}
