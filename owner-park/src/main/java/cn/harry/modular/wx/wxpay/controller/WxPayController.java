package cn.harry.modular.wx.wxpay.controller;

import cn.harry.common.api.R;
import cn.harry.common.exception.ApiException;
import cn.harry.modular.wx.wxpay.param.CreateRechargeOrderParam;
import cn.harry.modular.wx.wxpay.service.PayService;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.binarywang.wxpay.bean.ecommerce.PartnerTransactionsNotifyResult;
import com.github.binarywang.wxpay.bean.ecommerce.SignatureHeader;
import com.github.binarywang.wxpay.bean.ecommerce.TransactionsResult;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Slf4j
@Tag(name = "applet-pay => 微信支付")
@RestController
@RequestMapping("/applet/wxpay")
@AllArgsConstructor
public class WxPayController {
    private final WxPayService wxPayService;
    private final PayService payService;

    //    创建缴费订单
    @Operation(summary = "创建缴费订单")
    @PostMapping("/createPaymentOrder")
    public R<TransactionsResult.JsapiResult> createRechargeOrder(@RequestBody CreateRechargeOrderParam param) {
        String openId = param.getOpenid();
        Long registrationId = param.getRegistrationId();
        if (StrUtil.isEmpty(openId) || ObjectUtil.isEmpty(registrationId) ) {
            log.error("参数不完整 openId:{},registrationId:{}", openId, registrationId);
            throw new ApiException("参数不完整");
        }
        TransactionsResult.JsapiResult result = payService.createOrder(param);
        return R.success(result);
    }

    //    支付回调通知处理
    @Operation(summary = "支付回调通知处理")
    @PostMapping("/notify/order")
    public String parseOrderNotifyResult(@RequestBody String notifyData, HttpServletRequest request) throws WxPayException {
        log.info("支付回调通知参数：{}", notifyData);

        String timestamp = request.getHeader("Wechatpay-Timestamp");
        String nonce = request.getHeader("Wechatpay-Nonce");
        String serialNo = request.getHeader("Wechatpay-Serial");
        String signature = request.getHeader("Wechatpay-Signature");
        SignatureHeader header = new SignatureHeader();
        header.setTimeStamp(timestamp);
        header.setNonce(nonce);
        header.setSerialNo(serialNo);
        header.setSigned(signature);

        final PartnerTransactionsNotifyResult notifyResult = this.wxPayService.getEcommerceService().parsePartnerNotifyResult(notifyData, header);
        log.info("支付回调通知解析后的对象：{}", notifyResult);
        payService.saveOrderNotify(notifyResult.getResult());
        return WxPayNotifyResponse.success("成功");
    }
}
