package cn.harry.modular.wx.miniapp.controller;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import cn.harry.common.api.R;
import cn.harry.component.security.utils.SecurityUtils;
import cn.harry.modular.system.service.SysUserOauthService;
import cn.harry.modular.wx.miniapp.enums.OauthTypeEnums;
import cn.harry.modular.wx.miniapp.vo.AuthResult;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "认证中心(小程序)")
@RequestMapping("/auth/miniapp")
public class MiniappController {
    private final WxMaService wxMaService;
    private final SysUserOauthService sysUserOauthService;

    @Operation(summary = "login 登录")
    @PostMapping("/{appid}/login")
    public R<AuthResult> login(@PathVariable String appid, String code) {
        if (StrUtil.isBlank(code)) {
            return R.failed("empty jscode");
        }
        if (!wxMaService.switchover(appid)) {
            throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appid));
        }
        try {
            WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(code);
            log.info(session.getSessionKey());
            log.info(session.getOpenid());
            AuthResult result = sysUserOauthService.getAuthResult(appid, session, OauthTypeEnums.WX_XCX.getType());
            result.setAppid(appid);
            result.setOpenid(session.getOpenid());
            return R.success(result);
        } catch (WxErrorException e) {
            log.error(e.getMessage(), e);
        } finally {
            WxMaConfigHolder.remove();//清理ThreadLocal
        }
        return R.failed();
    }

}
