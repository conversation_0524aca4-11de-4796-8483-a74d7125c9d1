package cn.harry.modular.wx.miniapp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class AuthResult {

    @Schema(description = "token")
    private String token;

    @Schema(description = "token 类型", example = "Bearer")
    private String tokenType;

    @Schema(description = "过期时间(单位：秒)", example = "604800")
    private Long expiration;

    @Schema(description = "刷新token")
    private String refreshToken;

    @Schema(description = "appid")
    private String appid;

    @Schema(description = "openid")
    private String openid;

}
