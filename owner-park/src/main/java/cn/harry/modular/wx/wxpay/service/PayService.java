package cn.harry.modular.wx.wxpay.service;

import cn.harry.modular.wx.wxpay.param.CreateRechargeOrderParam;
import com.github.binarywang.wxpay.bean.ecommerce.PartnerTransactionsResult;
import com.github.binarywang.wxpay.bean.ecommerce.TransactionsResult;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
public interface PayService {
    /**
     * 根据店铺信息 充值套餐 openId 封装预充值参数
     *
     * @param param         参数
     * @return
     */
    TransactionsResult.JsapiResult createOrder(CreateRechargeOrderParam param );

    /**
     * 支付回调信息
     *
     * @param notifyResult
     */
    void saveOrderNotify(PartnerTransactionsResult notifyResult);
}
