package cn.harry.modular.job;

import cn.harry.modular.hmj.service.RechargeRecordService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class PaymentStatusCheckJobHandler {
    private final RechargeRecordService rechargeRecordService;


    // 每分钟检查一次是否存在超过半个小时未支付的订单，存在 置为无效
    @Scheduled(cron = "0 * * * * ?")
    public void checkPaymentStatus() {
        rechargeRecordService.checkPaymentStatus();
        log.info("检查支付状态");
    }
}