package cn.harry.modular.deliyun.service;

import cn.harry.modular.deliyun.req.*;
import cn.harry.modular.deliyun.res.*;

import java.util.List;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
public interface DeliyunService {
    /**
     * 4.1. findPunitInfo获取停车场基本信息
     *
     * @return
     */
    FindPunitInfoResponse findPunitInfo();

    /**
     * 4.2. findParkGarage获取停车场车库列表
     */
    List<FindParkGarageResponse> findParkGarage(FindParkGarageRequest findParkGarageRequest);

    /**
     * 4.3. findParkInfo临停车辆计费/获取在场车辆停车信息
     */
    FindParkInfoResponse findParkInfo(FindParkInfoRequest findParkInfoRequest);

    /**
     * 4.4. findParkingInfo获取在场车辆列表
     */
    List<FindParkingInfoResponse> findParkingInfo(FindParkingInfoRequest findParkingInfoRequest);

    /**
     * 4.5. findCarList获取登记车辆列表
     */
    List<FindCarListResponse> findCarList(FindCarListRequest findCarListRequest, String commKey);

    /**
     * 4.6. findCarGroup查询登记车辆的分组列表
     */
    List<FindCarGroupResponse> findCarGroup();

    /**
     * 4.7. findCarChargeRule获取登记车辆的缴费规则(月票车)
     */
    List<FindCarChargeRuleResponse> findCarChargeRule(FindCarChargeRuleRequest findCarChargeRuleRequest);

    /**
     * 4.8. findPoolParkList获取车位池的车位列表
     */
    List<FindPoolParkListResponse> findPoolParkList(FindPoolParkListRequest findPoolParkListRequest, String commKey);

    /**
     * 4.9. findPoolParkRule获取登记车辆车位池的车位缴费规则
     */
    List<FindPoolParkRuleResponse> findPoolParkRule(FindPoolParkRuleRequest findPoolParkRuleRequest);

    /**
     * 4.10. findBlackList获取黑名单车列表
     */
    List<FindBlackListResponse> findBlackList(FindBlackListRequest findBlackListRequest);

    /**
     * 4.11. findCycPayLog获取月票车续费记录
     */
    List<FindCycPayLogResponse> findCycPayLog(FindCycPayLogRequest findCycPayLogRequest);

    /**
     * 4.12. findReChargeLog获取储值票车充值记录
     */
    List<FindReChargeLogResponse> findReChargeLog(FindReChargeLogRequest findReChargeLogRequest);

    /**
     * 4.13. findPoolParkPayLog获取车位池-车位续费记录
     */
    List<FindPoolParkPayLogResponse> findPoolParkPayLog(FindPoolParkPayLogRequest findPoolParkPayLogRequest);

    /**
     * 4.14. findPlateByCid获取通道当前识别的车牌
     */
    FindPlateByCidResponse findPlateByCid(FindPlateByCidRequest findPlateByCidRequest);

    /**
     * 4.15. findParkingDevice获取停车场设备列表
     */
    List<FindParkingDeviceResponse> findParkingDevice();

    /**
     * 4.16. findParkingRate传参出入场时间查停车费
     */
    FindParkingRateResponse findParkingRate(FindParkingRateRequest findParkingRateRequest);

    /**
     * 4.17. findParkingSpace获取车位引导系统的车位停车信息
     */
    List<FindParkingSpaceResponse> findParkingSpace(FindParkingSpaceRequest findParkingSpaceRequest);

    /**
     * 4.18. findChannels获取车场通道信息
     */
    List<FindChannelsResponse> findChannels();

    /**
     * 4.19. findBookCars获取预约车辆列表
     */
    List<FindBookCarsResponse> findBookCars(FindBookCarsRequest findBookCarsRequest);

    /**
     * 4.20. findShopList获取商户列表
     */
    List<FindShopListResponse> findShopList(FindShopListRequest findShopListRequest);

    /**
     * 4.21. findPreSaleMoney预打折查询
     */
    FindPreSaleMoneyResponse findPreSaleMoney(FindPreSaleMoneyRequest findPreSaleMoneyRequest);

    /**
     * 4.22. findPoolList获取车位池列表
     *
     * @param findPoolListRequest
     * @param commKey
     * @return
     */
    List<FindPoolListResponse> findPoolList(FindPoolListRequest findPoolListRequest, String commKey);
    //    CAR_SALE("carSale", "5.1. carSale车辆打折"),
    CarSaleResponse carSale(CarSaleRequest carSaleRequest);

    //    CANCEL_SALE("cancelSale", "5.2. cancelSale取消车辆打折"),
    CancelSaleResponse cancelSale(CancelSaleRequest cancelSaleRequest);

    //    SAVE_CAR("saveCar", "5.3. saveCar添加及修改登记车辆信息"),
    SaveCarResponse saveCar(SaveCarRequest saveCarRequest);

    //    SAVE_PARKING_SPACE_CAR("saveParkingSpaceCar", "5.4. saveParkingSpaceCar添加或修改车位池车信息"),
    SaveParkingSpaceCarResponse saveParkingSpaceCar(SaveParkingSpaceCarRequest saveParkingSpaceCarRequest, String commKey);

    //    DELETE_CAR("deleteCar", "5.5. deleteCar删除/注销登记车辆信息"),
    DeleteCarResponse deleteCar(DeleteCarRequest deleteCarRequest);

    //    DELETE_PARKING_SPACE("deleteParkingSpace", "5.6. deleteParkingSpace删除车位池信息"),
    DeleteParkingSpaceResponse deleteParkingSpace(DeleteParkingSpaceRequest deleteParkingSpaceRequest);

    //    VEHICLE_DELAY("vehicleDelay", "5.7. vehicleDelay月票车/月卡延期"),
    VehicleDelayResponse vehicleDelay(VehicleDelayRequest vehicleDelayRequest);

    //    PARK_DELAY("parkDelay", "5.8. parkDelay车位池的车位延期"),
    void parkDelay(ParkDelayRequest parkDelayRequest, String commKey);

    //    SAVE_BLACK_LIST("saveBlackList", "5.9. saveBlackList添加黑名单"),
    SaveBlackListResponse saveBlackList(SaveBlackListRequest saveBlackListRequest);

    //    DELETE_BLACK_LIST("deleteBlackList", "5.10. deleteBlackList删除黑名单"),
    DeleteBlackListResponse deleteBlackList(DeleteBlackListRequest deleteBlackListRequest);

    //    PARK_CAR_BOOK("parkCarBook", "5.11. parkCarBook车辆预约"),
    ParkCarBookResponse parkCarBook(ParkCarBookRequest parkCarBookRequest);

    //    CANCEL_CAR_RESERVATION("cancelCarReservation", "5.12. cancelCarReservation车辆取消预约"),
    CancelCarReservationResponse cancelCarReservation(CancelCarReservationRequest cancelCarReservationRequest);

    //    RELEASE_COUPONS("releaseCoupons", "5.13. releaseCoupons发布二维码优惠券"),
    ReleaseCouponsResponse releaseCoupons(ReleaseCouponsRequest releaseCouponsRequest);

    //    HAND_CAR_IN("handCarIn", "5.14. handCarIn手动补录入场车辆"),
    HandCarInResponse handCarIn(HandCarInRequest handCarInRequest);

    //    LOCK_CAR("lockCar", "5.15. lockCar车辆上锁或解锁"),
    LockCarResponse lockCar(LockCarRequest lockCarRequest);

    //    CAR_IN("carIn", "5.16. carIn车辆扫码入场"),
    CarInResponse carIn(CarInRequest carInRequest);

    //    CAR_OUT("carOut", "5.17. carOut车辆扫码出场"),
    CarOutResponse carOut(CarOutRequest carOutRequest);

    //    DELETE_PARKING_CARINFO("deleteParkingCarinfo", "5.18. deleteParkingCarinfo删除在场车辆"),
    DeleteParkingCarinfoResponse deleteParkingCarinfo(DeleteParkingCarinfoRequest deleteParkingCarinfoRequest);

    //    SAVE_CAR_GROUP("saveCarGroup", "5.19. saveCarGroup添加或修改车辆分组"),
    SaveCarGroupResponse saveCarGroup(SaveCarGroupRequest saveCarGroupRequest);

    //    DELETE_CAR_GROUP("deleteCarGroup", "5.20. deleteCarGroup删除车辆分组"),
    DeleteCarGroupResponse deleteCarGroup(DeleteCarGroupRequest deleteCarGroupRequest);

    //    SAVE_GARAGE("saveGarage", "5.21. saveGarage添加或修改车库管理"),
    SaveGarageResponse saveGarage(SaveGarageRequest saveGarageRequest);

    //    DELETE_GARAGE("deleteGarage", "5.22. deleteGarage删除车库"),
    DeleteGarageResponse deleteGarage(DeleteGarageRequest deleteGarageRequest);

    //    SAVE_CAR_GARAGE("saveCarGarage", "5.23. saveCarGarage添加车辆授权"),
    SaveCarGarageResponse saveCarGarage(SaveCarGarageRequest saveCarGarageRequest);

    //    DEL_CAR_GARAGE("delCarGarage", "5.24. delCarGarage删除车辆授权"),
    DelCarGarageResponse delCarGarage(DelCarGarageRequest delCarGarageRequest);

    //    UPDATE_CARD("updateCard", "5.25. updateCard修改车牌号和注册号"),
    UpdateCardResponse updateCard(UpdateCardRequest updateCardRequest);

    //    UPDATE_PARKING_INFO("updateParkingInfo", "5.26. updateParkingInfo修改在场车辆车牌"),
    UpdateParkingInfoResponse updateParkingInfo(UpdateParkingInfoRequest updateParkingInfoRequest);
}
