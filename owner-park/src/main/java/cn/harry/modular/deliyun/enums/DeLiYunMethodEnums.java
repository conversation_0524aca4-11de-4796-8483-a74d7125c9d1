package cn.harry.modular.deliyun.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Getter
@AllArgsConstructor
public enum DeLiYunMethodEnums {
    FIND_PUNIT_INFO("findPunitInfo", "4.1. findPunitInfo获取停车场基本信息"),
    FIND_PARK_GARAGE("findParkGarage", "4.2. findParkGarage获取停车场车库列表"),
    FIND_PARK_INFO("findParkInfo", "4.3. findParkInfo临停车辆计费/获取在场车辆停车信息"),
    FIND_PARKING_INFO("findParkingInfo", "4.4. findParkingInfo获取在场车辆列表"),
    FIND_CAR_LIST("findCarList", "4.5. findCarList获取登记车辆列表"),
    FIND_CAR_GROUP("findCarGroup", " 4.6. findCarGroup查询登记车辆的分组列表"),
    FIND_CAR_CHARGE_RULE("findCarChargeRule", "4.7. findCarChargeRule获取登记车辆的缴费规则(月票车)"),
    FIND_POOL_PARK_LIST("findPoolParkList", "4.8. findPoolParkList获取车位池的车位列表"),
    FIND_POOL_PARK_RULE("findPoolParkRule", "4.9. findPoolParkRule获取登记车辆车位池的车位缴费规则"),
    FIND_BLACK_LIST("findBlackList", "4.10. findBlackList获取黑名单车列表"),
    FIND_CYC_PAY_LOG("findCycPayLog", "4.11. findCycPayLog获取月票车续费记录"),
    FIND_RE_CHARGE_LOG("findReChargeLog", "4.12. findReChargeLog获取储值票车充值记录"),
    FIND_POOL_PARK_PAY_LOG("findPoolParkPayLog", "4.13. findPoolParkPayLog获取车位池-车位续费记录"),
    FIND_PLATE_BY_CID("findPlateByCid", "4.14. findPlateByCid获取通道当前识别的车牌"),
    FIND_PARKING_DEVICE("findParkingDevice", "4.15. findParkingDevice获取停车场设备列表"),
    FIND_PARKING_RATE("findParkingRate", "4.16. findParkingRate传参出入场时间查停车费"),
    FIND_PARKING_SPACE("findParkingSpace", "4.17. findParkingSpace获取车位引导系统的车位停车信息"),
    FIND_CHANNELS("findChannels", "4.18. findChannels获取车场通道信息"),
    FIND_BOOK_CARS("findBookCars", "4.19. findBookCars获取预约车辆列表"),
    FIND_SHOP_LIST("findShopList", "4.20. findShopList获取商户列表"),
    FIND_PRE_SALE_MONEY("findPreSaleMoney", "4.21. findPreSaleMoney预打折查询"),
    FIND_POOL_LIST("findPoolList", "4.22. findPoolList 获取车位池列表"),

    CAR_SALE("carSale", "5.1. carSale车辆打折"),
    CANCEL_SALE("cancelSale", "5.2. cancelSale取消车辆打折"),
    SAVE_CAR("saveCar", "5.3. saveCar添加及修改登记车辆信息"),
    SAVE_PARKING_SPACE_CAR("saveParkingSpaceCar", "5.4. saveParkingSpaceCar添加或修改车位池车信息"),
    DELETE_CAR("deleteCar", "5.5. deleteCar删除/注销登记车辆信息"),
    DELETE_PARKING_SPACE("deleteParkingSpace", "5.6. deleteParkingSpace删除车位池信息"),
    VEHICLE_DELAY("vehicleDelay", "5.7. vehicleDelay月票车/月卡延期"),
    PARK_DELAY("parkDelay", "5.8. parkDelay车位池的车位延期"),
    SAVE_BLACK_LIST("saveBlackList", "5.9. saveBlackList添加黑名单"),
    DELETE_BLACK_LIST("deleteBlackList", "5.10. deleteBlackList删除黑名单"),
    PARK_CAR_BOOK("parkCarBook", "5.11. parkCarBook车辆预约"),
    CANCEL_CAR_RESERVATION("cancelCarReservation", "5.12. cancelCarReservation车辆取消预约"),
    RELEASE_COUPONS("releaseCoupons", "5.13. releaseCoupons发布二维码优惠券"),
    HAND_CAR_IN("handCarIn", "5.14. handCarIn手动补录入场车辆"),
    LOCK_CAR("lockCar", "5.15. lockCar车辆上锁或解锁"),
    CAR_IN("carIn", "5.16. carIn车辆扫码入场"),
    CAR_OUT("carOut", "5.17. carOut车辆扫码出场"),
    DELETE_PARKING_CARINFO("deleteParkingCarinfo", "5.18. deleteParkingCarinfo删除在场车辆"),
    SAVE_CAR_GROUP("saveCarGroup", "5.19. saveCarGroup添加或修改车辆分组"),
    DELETE_CAR_GROUP("deleteCarGroup", "5.20. deleteCarGroup删除车辆分组"),
    SAVE_GARAGE("saveGarage", "5.21. saveGarage添加或修改车库管理"),
    DELETE_GARAGE("deleteGarage", "5.22. deleteGarage删除车库"),
    SAVE_CAR_GARAGE("saveCarGarage", "5.23. saveCarGarage添加车辆授权"),
    DEL_CAR_GARAGE("delCarGarage", "5.24. delCarGarage删除车辆授权"),
    UPDATE_CARD("updateCard", "5.25. updateCard修改车牌号和注册号"),
    UPDATE_PARKING_INFO("updateParkingInfo", "5.26. updateParkingInfo修改在场车辆车牌"),

    ;
    private final String method;
    private final String desc;
}
