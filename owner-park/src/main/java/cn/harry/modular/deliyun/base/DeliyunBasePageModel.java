package cn.harry.modular.deliyun.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class DeliyunBasePageModel {
    /**
     * 页码
     */
    @Schema(description = "页码", defaultValue = "1")
    private Integer page = 1;
    /**
     * 每页条数（默认15条，最多200条）
     */
    @Schema(description = "每页条数",defaultValue = "15")
    private Integer size = 15;
}
