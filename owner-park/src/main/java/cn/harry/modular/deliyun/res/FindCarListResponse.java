package cn.harry.modular.deliyun.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class FindCarListResponse {

    @Schema(description = "车牌号码")
    private String plateNum;
    @Schema(description = "车牌颜色(1蓝色，2黄色，3白色，4黑色,5:绿色,6: 黄绿色)")
    private Byte plateColor;
    @Schema(description = "注册号/卡号")
    private String cardNo;
    @Schema(description = "车位号")
    private String parkNum;
    @Schema(description = "车类型ID（详见车类型说明）")
    private Long carTypeId;
    @Schema(description = "计费类型ID（详见计费类型说明）")
    private Long cardTypeId;
    @Schema(description = "车辆分组")
    private String gname;
    @Schema(description = "车主")
    private String pname;
    @Schema(description = "手机号码")
    private String mobile;
    @Schema(description = "地址")
    private String addr;
    @Schema(description = "有效期开始时间（yyyy-MM-dd）")
    private String beginDate;
    @Schema(description = "有效期结束时间（yyyy-MM-dd）")
    private String endDate;
    @Schema(description = "余额，只限于储值票车")
    private Double balance;
    @Schema(description = "授权车库，多个以逗号隔开，例如：A车库,B车库")
    private String authPgName;
    @Schema(description = "最后入场时间(yyyy-MM-ddHH:mm:ss)")
    private String lastInDate;
    @Schema(description = "最后出场时间(yyyy-MM-ddHH:mm:ss)")
    private String lastOutDate;
    @Schema(description = "最后修改人")
    private String realName;
    @Schema(description = "备注")
    private String remark;
    @Schema(description = "状态(0:离场,1:在场)")
    private Byte ioState;
    @Schema(description = "创建时间（yyyy-MM-ddHH:mm:ss）")
    private String createDate;
    @Schema(description = "最后修改时间（yyyy-MM-ddHH:mm:ss）")
    private String lastUpdateDate;

}
