package cn.harry.modular.deliyun.base;

import cn.harry.modular.deliyun.res.FindPunitInfoResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class ResponsesModel<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "响应状态码", example = "0")
    private long ecode;

    @Schema(description = "响应消息")
    private String msg;

    @Schema(description = "响应数据")
    private T data;

    protected ResponsesModel(long ecode, String msg, T data) {
        this.ecode = ecode;
        this.msg = msg;
        this.data = data;
    }

}
