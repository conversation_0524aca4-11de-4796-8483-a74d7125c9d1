package cn.harry.modular.deliyun.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class FindPoolListResponse {

    /**
     * 所属车位池
     */
    @Schema(description = "所属车位池")
    private String poolName;
    /**
     * 车位满缴费方式(1：先出收费，2：后进收费)
     */
    @Schema(description = "车位满缴费方式(1：先出收费，2：后进收费)")
    private Byte chargeType;
    /**
     * 车位池满统计规则（1：按停车场统计 2：按车库统计）
     */
    @Schema(description = "车位池满统计规则（1：按停车场统计 2：按车库统计）")
    private Byte fullMode;
    /**
     * 总车位数
     */
    @Schema(description = "总车位数")
    private Integer totalNum;
    /**
     * 有效车位数
     */
    @Schema(description = "有效车位数")
    private Integer validNum;
    /**
     * 在场车辆数
     */
    @Schema(description = "在场车辆数")
    private Integer parkedNum;
    /**
     * 剩余车位数
     */
    @Schema(description = "剩余车位数")
    private Integer emptyNum;
    /**
     * 绑定的车牌号
     */
    @Schema(description = "绑定的车牌号")
    private String plateNums;
    /**
     * 所属楼栋单元房铺
     */
    @Schema(description = "所属楼栋单元房铺")
    private String hourseAddr;
    /**
     * 管理员姓名
     */
    @Schema(description = "管理员姓名")
    private String pname;
    /**
     * 管理员电话
     */
    @Schema(description = "管理员电话")
    private String mobile;
    /**
     * 免费换车时间(单位分钟)
     */
    @Schema(description = "免费换车时间(单位分钟)")
    private Integer freeTime;

}
