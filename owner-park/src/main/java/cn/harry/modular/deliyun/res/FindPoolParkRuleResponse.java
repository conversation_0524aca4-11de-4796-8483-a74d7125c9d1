package cn.harry.modular.deliyun.res;

import cn.hutool.json.JSONArray;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class FindPoolParkRuleResponse {
//    poolName String 20所属车位池
//    parkName String 20车位名称、车位号
//    beginDate Long有效期开始时间（时间戳，单位:秒）
//    endDate Long有效期结束时间（时间戳，单位:秒）
//    rules array[]
//    rname String 50缴费规则名称
//    money Double所需缴费金额（单位：元）
//    delayNum int延期月数
    @Schema(description = "所属车位池")
    private String poolName;
    @Schema(description = "车位名称、车位号")
    private String parkName;
    @Schema(description = "有效期开始时间（时间戳，单位:秒）")
    private Long beginDate;
    @Schema(description = "有效期结束时间（时间戳，单位:秒）")
    private Long endDate;
    @Schema(description = "缴费规则")
    private JSONArray rules;
    @Schema(description = "缴费规则名称")
    private String rname;
    @Schema(description = "所需缴费金额（单位：元）")
    private Double money;
    @Schema(description = "延期月数")
    private Integer delayNum;
}
