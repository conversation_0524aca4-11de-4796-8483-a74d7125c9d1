package cn.harry.modular.deliyun.req;

import cn.harry.modular.deliyun.base.DeliyunBasePageModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FindCarListRequest extends DeliyunBasePageModel {
    @Schema(description = "车牌号码")
    private String plateNum;
    @Schema(description = "计费类型ID")
    private Integer cardTypeId;
    @Schema(description = "commKey")
    private String commKey;

}
