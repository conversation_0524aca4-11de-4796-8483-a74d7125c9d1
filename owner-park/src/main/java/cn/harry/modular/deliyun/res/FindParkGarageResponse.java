package cn.harry.modular.deliyun.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 4.2. findParkGarage获取停车场车库列表
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class FindParkGarageResponse {
    //    {"emptyTempCount":2675,"counttype9":1,"totalOtherCount":0,"totalCount":3000,"emptyCount":2675,"totalTempCount":3000,"pgnum":1,"counttype8":1,"counttype7":1,"counttype6":1,"id":13622,"counttype5":1,"attr":0,"counttype4":1,"counttype3":1,"emptyOtherCount":0,"counttype2":1,"pgname":"主车库","counttype1":1}

    @Schema(description = "车库ID")
    private Long id;
    @Schema(description = "车库编号")
    private Integer pgnum;
    @Schema(description = "车库名称")
    private String pgname;
    @Schema(description = "总车位数")
    private Integer totalCount;
    @Schema(description = "非临停总车位数")
    private Integer totalOtherCount;
    @Schema(description = "临时车总车位数")
    private Integer totalTempCount;
    @Schema(description = "空车位数")
    private Integer emptyCount;
    @Schema(description = "临停空车位数")
    private Integer emptyTempCount;
    @Schema(description = "非临停空车位数")
    private Integer emptyOtherCount;
    @Schema(description = "车库属性(0:主车库,1:子车库)")
    private Integer attr;
    @Schema(description = "临时车车位是否统计(0:不统计,1:统计)")
    private Integer counttype1;
    @Schema(description = "月票车车位是否统计(0:不统计,1:统计)")
    private Integer counttype2;
    @Schema(description = "贵宾车车位是否统计(0:不统计,1:统计)")
    private Integer counttype3;
    @Schema(description = "免费车车位是否统计(0:不统计,1:统计)")
    private Integer counttype4;
    @Schema(description = "储值票车车位是否统计(0:不统计,1:统计)")
    private Integer counttype5;
    @Schema(description = "其他车类型车位是否统计(0:不统计,1:统计)")
    private Integer counttype6;
    @Schema(description = "是否统计重复车位(0:不统计,1:统计)")
    private Integer counttype7;
    @Schema(description = "时租车车位是否统计(0:不统计,1:统计)")
    private Integer counttype8;
    @Schema(description = "车位池车车位是否统计(0:不统计,1:统计)")
    private Integer counttype9;
}
