package cn.harry.modular.deliyun.req;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class SaveCarRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录主键ID（新添加时可以不传或传0）
     */
    private Long id;

    /**
     * 车牌号(与注册号二选一)
     */
    private String plateNum;

    /**
     * 注册号/卡号(与车牌号二选一)
     */
    private String cardNo;

    /**
     * 车牌颜色(1:蓝色, 2:黄色, 3:白色, 4:黑色, 5:绿色, 6:黄绿色)
     */
    private Byte plateNumColor;

    /**
     * 计费类型ID（详见计费类型说明）
     */
    private Integer cardTypeId;

    /**
     * 车类型ID（详见车类型说明）
     */
    private Long carTypeId;

    /**
     * 有效期开始时间 (时间戳，计费类型是贵宾车时可不填)
     */
    private Long beginDate;

    /**
     * 有效期结束时间 (时间戳，计费类型是贵宾车时可不填)
     */
    private Long endDate;

    /**
     * 时租车收费开始时间(计费类型是时租车的时候必填) 格式 08:00
     */
    private String szkBeginDate;

    /**
     * 时租车收费结束时间(计费类型是时租车的时候必填) 格式 20:00
     */
    private String szkEndDate;
    /**
     * 注册车辆收费金额（单位：元）
     */
    private Double money;

    /**
     * 车辆分组名称
     */
    private String gname;

    /**
     * 车辆备注
     */
    private String remark;

    /**
     * 车主姓名 (当车辆有人员信息时必填)
     */
    private String pname;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 住址信息
     */
    private String addr;

    /**
     * 人员信息备注
     */
    private String premark;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 授权车库名称（4.2 接口返回）多个用英文逗号隔开，比如：xxx,xxx,xxx
     */
    private String authParkGarage;

}
