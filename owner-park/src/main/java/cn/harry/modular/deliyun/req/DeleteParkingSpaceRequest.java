package cn.harry.modular.deliyun.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class DeleteParkingSpaceRequest {
//    id Long 记录主键 ID（新添加时可以不传或传 0）
//    poolName String 必填    车位池名称(请输入车位池全名，不支持模糊删除)

    /**
     *  id
     */
    @Schema(description = "id")
    private Long id;
    /**
     *  车位池名称
     */
    @Schema(description = "车位池名称")
    private String poolName;
}
