package cn.harry.modular.deliyun.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * parkDelay 车位池的车位延期 请求参数
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class ParkDelayRequest {
    @Schema(description = "所属车位池名称")
    private String poolName;
    @Schema(description = "车位名称、车位号")
    private String parkName;
    @Schema(description = "有效期开始时间（时间戳，单位:秒）")
    private Long beginDate;
    @Schema(description = "有效期结束时间（时间戳，单位:秒）")
    private Long endDate;
    @Schema(description = "支付方式（详见支付方式说明）")
    private Byte payMode;
    @Schema(description = "收费金额（单位：元）")
    private Double chargeMoney;

    @Schema(description = "commKey")
    private String commKey;

    /**
     * 车库ID
     */
    @Schema(description = "车库ID(仅前端使用)")
    private Long garageId;
}
