package cn.harry.modular.deliyun.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 计费类型
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Getter
@AllArgsConstructor
public enum CardTypeEnums {
    // 1 贵宾车    //2 月票车    //3 储值票车    //4 临时车    //5 免费车    //6 时租车    //7 车位池车    //8 公务车    //9 月结车
    GUEST_CAR(1L, "贵宾车"),
    MONTH_TICKET_CAR(2L, "月票车"),
    STORED_VALUE_TICKET_CAR(3L, "储值票车"),
    TEMPORARY_CAR(4L, "临时车"),
    FREE_CAR(5L, "免费车"),
    TIME_CAR(6L, "时租车"),
    PARKING_SPACE_POOL_CAR(7L, "车位池车"),
    OFFICIAL_CAR(8L, "公务车"),
    MONTH_SETTLEMENT_CAR(9L, "月结车");
    private final Long code;
    private final String message;
}
