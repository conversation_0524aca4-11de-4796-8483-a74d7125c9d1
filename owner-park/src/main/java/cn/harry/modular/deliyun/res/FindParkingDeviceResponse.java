package cn.harry.modular.deliyun.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class FindParkingDeviceResponse {
    @Schema(description = "设备IP")
    private String deviceIp;
    @Schema(description = "设备名称")
    private String dname;
    @Schema(description = "设备物理地址")
    private String mac;
    @Schema(description = "设备序列号")
    private String sn;
    @Schema(description = "在线状态（0.离线,1.在线")
    private Byte isOnLine;
}
