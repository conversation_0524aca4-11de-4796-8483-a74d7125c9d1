package cn.harry.modular.deliyun.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class FindPoolParkListResponse {

    @Schema(description = "所属车位池")
    private String poolName;

    @Schema(description = "所属车库名称")
    private String pgname;

    @Schema(description = "车位名称、车位号")
    private String parkName;

    @Schema(description = "车位类型(0:公用车位,1:私家车位)")
    private Integer parkType;

    @Schema(description = "有效期开始时间（时间戳，单位:秒）")
    private Long beginDate;

    @Schema(description = "有效期结束时间（时间戳，单位:秒）")
    private Long endDate;

    @Schema(description = "创建时间（yyyy-MM-ddHH:mm:ss）")
    private String createDate;

    @Schema(description = "最后修改时间（yyyy-MM-ddHH:mm:ss）")
    private String lastUpdateDate;
}
