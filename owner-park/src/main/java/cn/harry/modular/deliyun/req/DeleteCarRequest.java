package cn.harry.modular.deliyun.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class DeleteCarRequest {
    //    id Long 记录主键 ID
//    plateNum String 20 二选一 车牌号(与注册号二选一)
//    cardNo String 20 二选一 注册号/卡号(与车牌号二选一)
//    money double 默认：0 退款金额
    /**
     * id Long 必填 记录主键 ID
     */
    @Schema(description = "必填 记录主键 ID")
    private Long id;
    /**
     * plateNum String 20 二选一 车牌号(与注册号二选一)
     */
    @Schema(description = "车牌号(与注册号二选一)")
    private String plateNum;
    /**
     * cardNo String 20 二选一 注册号/卡号(与车牌号二选一)
     */
    @Schema(description = "注册号/卡号(与车牌号二选一)")
    private String cardNo;
    /**
     * money double 默认：0 退款金额
     */
    @Schema(description = "退款金额")
    private Double money;
}
