package cn.harry.modular.deliyun.req;

import cn.harry.modular.deliyun.base.DeliyunBasePageModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FindPoolParkListRequest extends DeliyunBasePageModel {
    //    plateNum String 20车牌号(精准查询)
//    poolName String 20所属车位池名称(模糊查询)
//    parkName String 20车位名称、车位号(模糊查询
    @Schema(description = "车牌号(精准查询)")
    private String plateNum;
    @Schema(description = "所属车位池名称(模糊查询)")
    private String poolName;
    @Schema(description = "车位名称、车位号(模糊查询)")
    private String parkName;

    @Schema(description = "车位类型(0:公用车位,1:私家车位)")
    private Integer parkType;

    @Schema(description = "commKey")
    private String commKey;
}
