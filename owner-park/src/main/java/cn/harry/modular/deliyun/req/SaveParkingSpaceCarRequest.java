package cn.harry.modular.deliyun.req;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class SaveParkingSpaceCarRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录主键ID（新添加时可以不传或传0）
     */
    private Long id;

    /**
     * 车位池名称（必填）
     */
    private String poolName;

    /**
     * 车位池满时收费方式(1:先出收费, 2:后进收费)（必填）
     */
    private Byte chargeType;

    /**
     * 车位池满统计规则(1:按停车场统计, 2:按车库统计)（必填）
     */
    private Byte fullMode;

    /**
     * 所属车库名称(如果 fullMode 为 1 时必填, 为 2 时可以不填)
     */
    private String pgname;

    /**
     * 车位池类型(0:默认类型, 1:子母车位)
     */
    private Byte cpType;

    /**
     * 车位池备注
     */
    private String poolRemark;

    /**
     * 车辆列表Json字符串
     */
    private List<CarInfo> carList;

    /**
     * 车位列表Json字符串
     */
    private List<ParkingSpace> parkList;

    /**
     * commKey
     */
    private String commKey;

    /**
     * 车辆信息内部类（用于carList解析）
     */
    @Data
    public static class CarInfo {
        /**
         * 车牌号(必填，已存在则修改，否则添加)
         */
        private String plateNum;

        /**
         * 车牌颜色(1:蓝色, 2:黄色, 3:白色, 4:黑色, 5:绿色, 6:黄绿色)
         */
        private Byte plateNumColor;

        /**
         * 车类型ID（必填）
         */
        private Long carTypeId;

        /**
         * 车辆分组名称
         */
        private String gname;

        /**
         * 车辆备注
         */
        private String remark;

        /**
         * 车主姓名 (当车辆有人员信息时必填)
         */
        private String pname;

        /**
         * 手机号码
         */
        private String mobile;

        /**
         * 住址信息
         */
        private String addr;

        /**
         * 人员信息备注
         */
        private String premark;
    }

    /**
     * 车位信息内部类（用于parkList解析）
     */
    @Data
    public static class ParkingSpace {
        /**
         * 车位名称、车位号(必填，同一个车位池下唯一)
         */
        private String parkName;

        /**
         * 车位所属车库名称(当 fullMode 为 2 时必填)
         */
        private String pgname;

        /**
         * 车位类型(1:公共车位, 2:私家车位)
         */
        private Byte parkType;

        /**
         * 车位有效期开始时间（时间戳，单位:秒，必填）
         */
        private Long beginDate;

        /**
         * 车位有效期结束时间（时间戳，单位:秒，必填）
         */
        private Long endDate;

        /**
         * 车位收费金额（必填）
         */
        private Double chargeMoney;

        /**
         * 车位备注
         */
        private String remark;
    }
}
