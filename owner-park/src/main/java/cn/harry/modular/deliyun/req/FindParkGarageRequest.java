package cn.harry.modular.deliyun.req;

import cn.harry.modular.deliyun.base.DeliyunBasePageModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 4.2. findParkGarage获取停车场车库列表
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FindParkGarageRequest extends DeliyunBasePageModel {
    /**
     * 车库名称
     */
    @Schema(description = "车库名称")
    private String pgname;
}
