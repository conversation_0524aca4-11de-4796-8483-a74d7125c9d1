package cn.harry.modular.deliyun.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class FindCarChargeRuleResponse {
//    rname String 50 缴费规则名称
//    money Double 所需缴费金额（单位：元）
//    delayNum int 延期月数
    /**
     * 缴费规则名称
     */
    @Schema(description = "缴费规则名称")
    private String rname;
    /**
     * 所需缴费金额（单位：元）
     */
    @Schema(description = "所需缴费金额（单位：元）")
    private Double money;
    /**
     * 延期月数
     */
    @Schema(description = "延期月数")
    private Integer delayNum;
}
