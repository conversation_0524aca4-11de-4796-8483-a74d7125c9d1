package cn.harry.modular.deliyun.controller;

import cn.harry.common.api.R;
import cn.harry.modular.deliyun.req.*;
import cn.harry.modular.deliyun.res.*;
import cn.harry.modular.deliyun.service.DeliyunService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "德立云接口")
@RequestMapping("/deliyun")
public class DeliyunController {
    private final DeliyunService deliyunService;

    @Operation(summary = "4.1. findPunitInfo获取停车场基本信息")
    @ApiOperationSupport(order = 401)
    @PostMapping("/findPunitInfo")
    public R<FindPunitInfoResponse> findPunitInfo() {
        FindPunitInfoResponse res = deliyunService.findPunitInfo();
        return R.success(res);
    }

    @Operation(summary = "4.2. findParkGarage获取停车场车库列表")
    @ApiOperationSupport(order = 402)
    @PostMapping("/findParkGarage")
    public R<List<FindParkGarageResponse>> findParkGarage(@RequestBody FindParkGarageRequest findParkGarageRequest) {
        List<FindParkGarageResponse> res = deliyunService.findParkGarage(findParkGarageRequest);
        return R.success(res);
    }

    // TODO 接口待完善
    @Operation(summary = "4.3. findParkInfo临停车辆计费/获取在场车辆停车信息")
    @ApiOperationSupport(order = 403)
    @PostMapping("/findParkInfo")
    public R<FindParkInfoResponse> findParkInfo(@RequestBody FindParkInfoRequest findParkInfoRequest) {
        FindParkInfoResponse res = deliyunService.findParkInfo(findParkInfoRequest);
        return R.success(res);
    }

    @Operation(summary = "4.4. findParkingInfo获取在场车辆列表")
    @ApiOperationSupport(order = 404)
    @PostMapping("/findParkingInfo")
    public R<List<FindParkingInfoResponse>> findParkingInfo(@RequestBody FindParkingInfoRequest findParkingInfoRequest) {
        List<FindParkingInfoResponse> res = deliyunService.findParkingInfo(findParkingInfoRequest);
        return R.success(res);
    }

    @PostMapping("/findCarList")
    @Operation(summary = "4.5. findCarList获取登记车辆列表")
    @ApiOperationSupport(order = 405)
    public R<List<FindCarListResponse>> findCarList(@RequestBody FindCarListRequest findCarListRequest) {
        if (findCarListRequest.getCommKey() == null || findCarListRequest.getCommKey().isEmpty()) {
            return R.failed("commKey不能为空");
        }
        List<FindCarListResponse> res = deliyunService.findCarList(findCarListRequest, findCarListRequest.getCommKey());
        return R.success(res);
    }

    @PostMapping("/findCarGroup")
    @Operation(summary = "4.6. findCarGroup查询登记车辆的分组列表")
    @ApiOperationSupport(order = 406)
    public R<List<FindCarGroupResponse>> findCarGroup() {
        List<FindCarGroupResponse> res = deliyunService.findCarGroup();
        return R.success(res);
    }

    @PostMapping("/findCarChargeRule")
    @Operation(summary = "4.7. findCarChargeRule获取登记车辆的缴费规则(月票车)")
    @ApiOperationSupport(order = 407)
    public R<List<FindCarChargeRuleResponse>> findCarChargeRule(@RequestBody FindCarChargeRuleRequest findCarChargeRuleRequest) {
        List<FindCarChargeRuleResponse> res = deliyunService.findCarChargeRule(findCarChargeRuleRequest);
        return R.success(res);
    }

    @PostMapping("/findPoolParkList")
    @Operation(summary = "4.8. findPoolParkList获取车位池的车位列表")
    @ApiOperationSupport(order = 408)
    public R<List<FindPoolParkListResponse>> findPunitInfo(@RequestBody FindPoolParkListRequest findPoolParkListRequest) {
        if (findPoolParkListRequest.getCommKey() == null || findPoolParkListRequest.getCommKey().equals("")) {
            return R.failed("commKey不能为空");
        }
        List<FindPoolParkListResponse> res = deliyunService.findPoolParkList(findPoolParkListRequest, findPoolParkListRequest.getCommKey());
        return R.success(res);
    }

    @PostMapping("/findPoolParkRule")
    @Operation(summary = "4.9. findPoolParkRule获取登记车辆车位池的车位缴费规则")
    @ApiOperationSupport(order = 409)
    public R<List<FindPoolParkRuleResponse>> findPoolParkRule(@RequestBody FindPoolParkRuleRequest findPoolParkRuleRequest) {
        List<FindPoolParkRuleResponse> res = deliyunService.findPoolParkRule(findPoolParkRuleRequest);
        return R.success(res);
    }

    @Operation(summary = "4.15. findParkingDevice获取停车场设备列表")
    @ApiOperationSupport(order = 415)
    @PostMapping("/findParkingDevice")
    public R<List<FindParkingDeviceResponse>> findParkingDevice() {
        List<FindParkingDeviceResponse> res = deliyunService.findParkingDevice();
        return R.success(res);
    }

    @PostMapping("/findParkingRate")
    @Operation(summary = "4.16. findParkingRate 传参出入场时间查停车费")
    @ApiOperationSupport(order = 416)
    public R<FindParkingRateResponse> findParkingRate(@RequestBody FindParkingRateRequest findParkingRateRequest) {
        FindParkingRateResponse res = deliyunService.findParkingRate(findParkingRateRequest);
        return R.success(res);
    }

    @PostMapping("/findParkingSpace")
    @Operation(summary = "4.17. findParkingSpace 获取车位引导系统的车位停车信息")
    @ApiOperationSupport(order = 417)
    public R<List<FindParkingSpaceResponse>> findParkingSpace(@RequestBody FindParkingSpaceRequest findParkingSpaceRequest) {
        List<FindParkingSpaceResponse> res = deliyunService.findParkingSpace(findParkingSpaceRequest);
        return R.success(res);
    }
//    FIND_POOL_LIST("findPoolList", "4.22. findPoolList 获取车位池列表"),

    @PostMapping("/findPoolList")
    @Operation(summary = "4.22. findPoolList 获取车位池列表")
    @ApiOperationSupport(order = 422)
    public R<List<FindPoolListResponse>> findPoolList(@RequestBody FindPoolListRequest findPoolListRequest) {
        if (findPoolListRequest.getCommKey() == null || findPoolListRequest.getCommKey().isEmpty()) {
            return R.failed("commKey不能为空");
        }
        List<FindPoolListResponse> res = deliyunService.findPoolList(findPoolListRequest, findPoolListRequest.getCommKey());
        return R.success(res);
    }

    /**
     * 功能说明：添加及修改登记车辆信息(可通过车牌号，注册号/卡号修改)，车位池车不能通过本接口添加，如需添加车位池车，调用本文档 5.4 saveParkingSpaceCar 接口
     *
     * @param saveCarRequest
     * @return
     */
    @PostMapping("/saveCar")
    @Operation(summary = "5.3. saveCar添加及修改登记车辆信息")
    @ApiOperationSupport(order = 503)
    public R<SaveCarResponse> saveCar(@RequestBody SaveCarRequest saveCarRequest) {
        SaveCarResponse res = deliyunService.saveCar(saveCarRequest);
        return R.success(res);
    }

    //    SAVE_PARKING_SPACE_CAR("saveParkingSpaceCar", "5.4. saveParkingSpaceCar添加或修改车位池车信息"),
    @PostMapping("/saveParkingSpaceCar")
    @Operation(summary = "5.4. saveParkingSpaceCar添加或修改车位池车信息")
    @ApiOperationSupport(order = 504)
    public R<SaveParkingSpaceCarResponse> saveParkingSpaceCar(@RequestBody SaveParkingSpaceCarRequest saveParkingSpaceCarRequest) {
        SaveParkingSpaceCarResponse res = deliyunService.saveParkingSpaceCar(saveParkingSpaceCarRequest, saveParkingSpaceCarRequest.getCommKey());
        return R.success(res);
    }

    //    DELETE_CAR("deleteCar", "5.5. deleteCar删除/注销登记车辆信息"),
    @PostMapping("/deleteCar")
    @Operation(summary = "5.5. deleteCar删除/注销登记车辆信息")
    @ApiOperationSupport(order = 505)
    public R<DeleteCarResponse> deleteCar(@RequestBody DeleteCarRequest deleteCarRequest) {
        DeleteCarResponse res = deliyunService.deleteCar(deleteCarRequest);
        return R.success(res);
    }

    //    DELETE_PARKING_SPACE("deleteParkingSpace", "5.6. deleteParkingSpace删除车位池信息"),
    @PostMapping("/deleteParkingSpace")
    @Operation(summary = "5.6. deleteParkingSpace删除车位池信息")
    @ApiOperationSupport(order = 506)
    public R<DeleteParkingSpaceResponse> deleteParkingSpace(@RequestBody DeleteParkingSpaceRequest deleteParkingSpaceRequest) {
        DeleteParkingSpaceResponse res = deliyunService.deleteParkingSpace(deleteParkingSpaceRequest);
        return R.success(res);
    }

    //    VEHICLE_DELAY("vehicleDelay", "5.7. vehicleDelay月票车/月卡延期"),
    @PostMapping("/vehicleDelay")
    @Operation(summary = "5.7. vehicleDelay月票车/月卡延期")
    @ApiOperationSupport(order = 507)
    public R<VehicleDelayResponse> vehicleDelay(@RequestBody VehicleDelayRequest vehicleDelayRequest) {
        VehicleDelayResponse res = deliyunService.vehicleDelay(vehicleDelayRequest);
        return R.success(res);
    }

    //    PARK_DELAY("parkDelay", "5.8. parkDelay车位池的车位延期"),
    @PostMapping("/parkDelay")
    @Operation(summary = "5.8. parkDelay车位池的车位延期")
    @ApiOperationSupport(order = 508)
    public R parkDelay(@RequestBody ParkDelayRequest parkDelayRequest) {
        deliyunService.parkDelay(parkDelayRequest, parkDelayRequest.getCommKey());
        return R.success();
    }

    //    SAVE_BLACK_LIST("saveBlackList", "5.9. saveBlackList添加黑名单"),
    @PostMapping("/saveBlackList")
    @Operation(summary = "5.9. saveBlackList添加黑名单")
    @ApiOperationSupport(order = 509)
    public R<SaveBlackListResponse> saveBlackList(@RequestBody SaveBlackListRequest saveBlackListRequest) {
        SaveBlackListResponse res = deliyunService.saveBlackList(saveBlackListRequest);
        return R.success(res);
    }

    //    DELETE_BLACK_LIST("deleteBlackList", "5.10. deleteBlackList删除黑名单"),
    @PostMapping("/deleteBlackList")
    @Operation(summary = "5.10. deleteBlackList删除黑名单")
    @ApiOperationSupport(order = 510)
    public R<DeleteBlackListResponse> deleteBlackList(@RequestBody DeleteBlackListRequest deleteBlackListRequest) {
        DeleteBlackListResponse res = deliyunService.deleteBlackList(deleteBlackListRequest);
        return R.success(res);
    }

    //    PARK_CAR_BOOK("parkCarBook", "5.11. parkCarBook车辆预约"),
    @PostMapping("/parkCarBook")
    @Operation(summary = "5.11. parkCarBook车辆预约")
    @ApiOperationSupport(order = 511)
    public R<ParkCarBookResponse> parkCarBook(@RequestBody ParkCarBookRequest parkCarBookRequest) {
        ParkCarBookResponse res = deliyunService.parkCarBook(parkCarBookRequest);
        return R.success(res);
    }

    //    CANCEL_CAR_RESERVATION("cancelCarReservation", "5.12. cancelCarReservation车辆取消预约"),
    @PostMapping("/cancelCarReservation")
    @Operation(summary = "5.12. cancelCarReservation车辆取消预约")
    @ApiOperationSupport(order = 512)
    public R<CancelCarReservationResponse> cancelCarReservation(@RequestBody CancelCarReservationRequest cancelCarReservationRequest) {
        CancelCarReservationResponse res = deliyunService.cancelCarReservation(cancelCarReservationRequest);
        return R.success(res);
    }

    //    RELEASE_COUPONS("releaseCoupons", "5.13. releaseCoupons发布二维码优惠券"),
    @PostMapping("/releaseCoupons")
    @Operation(summary = "5.13. releaseCoupons发布二维码优惠券")
    @ApiOperationSupport(order = 513)
    public R<ReleaseCouponsResponse> releaseCoupons(@RequestBody ReleaseCouponsRequest releaseCouponsRequest) {
        ReleaseCouponsResponse res = deliyunService.releaseCoupons(releaseCouponsRequest);
        return R.success(res);
    }

    //    HAND_CAR_IN("handCarIn", "5.14. handCarIn手动补录入场车辆"),
    @PostMapping("/handCarIn")
    @Operation(summary = "5.14. handCarIn手动补录入场车辆")
    @ApiOperationSupport(order = 514)
    public R<HandCarInResponse> handCarIn(@RequestBody HandCarInRequest handCarInRequest) {
        HandCarInResponse res = deliyunService.handCarIn(handCarInRequest);
        return R.success(res);
    }

    //   LOCK_CAR("lockCar", "5.15. lockCar车辆上锁或解锁"),
    @PostMapping("/lockCar")
    @Operation(summary = "5.15. lockCar车辆上锁或解锁")
    @ApiOperationSupport(order = 515)
    public R<LockCarResponse> lockCar(@RequestBody LockCarRequest lockCarRequest) {
        LockCarResponse res = deliyunService.lockCar(lockCarRequest);
        return R.success(res);
    }

    //    CAR_IN("carIn", "5.16. carIn车辆扫码入场"),
    @PostMapping("/carIn")
    @Operation(summary = "5.16. carIn车辆扫码入场")
    @ApiOperationSupport(order = 516)
    public R<CarInResponse> carIn(@RequestBody CarInRequest carInRequest) {
        CarInResponse res = deliyunService.carIn(carInRequest);
        return R.success(res);
    }

    //    CAR_OUT("carOut", "5.17. carOut车辆扫码出场"),
    @PostMapping("/carOut")
    @Operation(summary = "5.17. carOut车辆扫码出场")
    @ApiOperationSupport(order = 517)
    public R<CarOutResponse> carOut(@RequestBody CarOutRequest carOutRequest) {
        CarOutResponse res = deliyunService.carOut(carOutRequest);
        return R.success(res);
    }

    //    DELETE_PARKING_CARINFO("deleteParkingCarinfo", "5.18. deleteParkingCarinfo删除在场车辆"),
    @PostMapping("/deleteParkingCarinfo")
    @Operation(summary = "5.18. deleteParkingCarinfo删除在场车辆")
    @ApiOperationSupport(order = 518)
    public R<DeleteParkingCarinfoResponse> deleteParkingCarinfo(@RequestBody DeleteParkingCarinfoRequest deleteParkingCarinfoRequest) {
        DeleteParkingCarinfoResponse res = deliyunService.deleteParkingCarinfo(deleteParkingCarinfoRequest);
        return R.success(res);
    }

    //    SAVE_CAR_GROUP("saveCarGroup", "5.19. saveCarGroup添加或修改车辆分组"),
    @PostMapping("/saveCarGroup")
    @Operation(summary = "5.19. saveCarGroup添加或修改车辆分组")
    @ApiOperationSupport(order = 519)
    public R<SaveCarGroupResponse> saveCarGroup(@RequestBody SaveCarGroupRequest saveCarGroupRequest) {
        SaveCarGroupResponse res = deliyunService.saveCarGroup(saveCarGroupRequest);
        return R.success(res);
    }

    //    DELETE_CAR_GROUP("deleteCarGroup", "5.20. deleteCarGroup删除车辆分组"),
    @PostMapping("/deleteCarGroup")
    @Operation(summary = "5.20. deleteCarGroup删除车辆分组")
    @ApiOperationSupport(order = 520)
    public R<DeleteCarGroupResponse> deleteCarGroup(@RequestBody DeleteCarGroupRequest deleteCarGroupRequest) {
        DeleteCarGroupResponse res = deliyunService.deleteCarGroup(deleteCarGroupRequest);
        return R.success(res);
    }

    //    SAVE_GARAGE("saveGarage", "5.21. saveGarage添加或修改车库管理"),
    @PostMapping("/saveGarage")
    @Operation(summary = "5.21. saveGarage添加或修改车库管理")
    @ApiOperationSupport(order = 521)
    public R<SaveGarageResponse> saveGarage(@RequestBody SaveGarageRequest saveGarageRequest) {
        SaveGarageResponse res = deliyunService.saveGarage(saveGarageRequest);
        return R.success(res);
    }

    //    DELETE_GARAGE("deleteGarage", "5.22. deleteGarage删除车库"),
    @PostMapping("/deleteGarage")
    @Operation(summary = "5.22. deleteGarage删除车库")
    @ApiOperationSupport(order = 522)
    public R<DeleteGarageResponse> deleteGarage(@RequestBody DeleteGarageRequest deleteGarageRequest) {
        DeleteGarageResponse res = deliyunService.deleteGarage(deleteGarageRequest);
        return R.success(res);
    }

    //    SAVE_CAR_GARAGE("saveCarGarage", "5.23. saveCarGarage添加车辆授权"),
    @PostMapping("/saveCarGarage")
    @Operation(summary = "5.23. saveCarGarage添加车辆授权")
    @ApiOperationSupport(order = 523)
    public R<SaveCarGarageResponse> saveCarGarage(@RequestBody SaveCarGarageRequest saveCarGarageRequest) {
        SaveCarGarageResponse res = deliyunService.saveCarGarage(saveCarGarageRequest);
        return R.success(res);
    }

    //    DEL_CAR_GARAGE("delCarGarage", "5.24. delCarGarage删除车辆授权"),
    @PostMapping("/delCarGarage")
    @Operation(summary = "5.24. delCarGarage删除车辆授权")
    @ApiOperationSupport(order = 524)
    public R<DelCarGarageResponse> delCarGarage(@RequestBody DelCarGarageRequest delCarGarageRequest) {
        DelCarGarageResponse res = deliyunService.delCarGarage(delCarGarageRequest);
        return R.success(res);
    }

    //    UPDATE_CARD("updateCard", "5.25. updateCard修改车牌号和注册号"),
    @PostMapping("/updateCard")
    @Operation(summary = "5.25. updateCard修改车牌号和注册号")
    @ApiOperationSupport(order = 525)
    public R<UpdateCardResponse> updateCard(@RequestBody UpdateCardRequest updateCardRequest) {
        UpdateCardResponse res = deliyunService.updateCard(updateCardRequest);
        return R.success(res);
    }

    //    UPDATE_PARKING_INFO("updateParkingInfo", "5.26. updateParkingInfo修改在场车辆车牌"),
    @PostMapping("/updateParkingInfo")
    @Operation(summary = "5.26. updateParkingInfo修改在场车辆车牌")
    @ApiOperationSupport(order = 526)
    public R<UpdateParkingInfoResponse> updateParkingInfo(@RequestBody UpdateParkingInfoRequest updateParkingInfoRequest) {
        UpdateParkingInfoResponse res = deliyunService.updateParkingInfo(updateParkingInfoRequest);
        return R.success(res);
    }

}
