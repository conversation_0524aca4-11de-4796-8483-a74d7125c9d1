package cn.harry.modular.deliyun.service.impl;

import cn.harry.common.exception.ApiException;
import cn.harry.modular.deliyun.enums.DeLiYunMethodEnums;
import cn.harry.modular.deliyun.req.*;
import cn.harry.modular.deliyun.res.*;
import cn.harry.modular.deliyun.service.DeliyunService;
import cn.harry.modular.hmj.domain.ParkingLot;
import cn.harry.modular.hmj.service.ParkingLotService;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeliyunServiceImpl implements DeliyunService {
    public static final String API_URL = "https://openapi.deliyun.cn/parking/";
    public static final String API_KEY = "cvENc3fQtWOX23v5";
    public static final String API_SECRET = "LiBYxcdbsfel3OufTkmqlDNi053oOVSV";
    // 默认项目唯一编码（兼容性保留）
    public static final String DEFAULT_COMM_KEY = "hKcsKOBYoYvAYThO";

    private final ParkingLotService parkingLotService;

    /**
     * 获取项目唯一编码
     * 优先从数据库中获取第一个停车场的commKey，如果没有则使用默认值
     *
     * @return 项目唯一编码
     */
    private String getCommKey() {
//        try {
//            List<ParkingLot> parkingLots = parkingLotService.list();
//            if (!parkingLots.isEmpty()) {
//                ParkingLot parkingLot = parkingLots.get(0);
//                if (parkingLot.getCommKey() != null && !parkingLot.getCommKey().trim().isEmpty()) {
//                    return parkingLot.getCommKey();
//                }
//            }
//        } catch (Exception e) {
//            log.warn("获取停车场项目唯一编码失败，使用默认值", e);
//        }
        return DEFAULT_COMM_KEY;
    }

    /**
     * 根据停车场ID获取项目唯一编码
     *
     * @param parkingLotId 停车场ID
     * @return 项目唯一编码
     */
    private String getCommKeyByParkingLotId(Long parkingLotId) {
        try {
            if (parkingLotId != null) {
                ParkingLot parkingLot = parkingLotService.getById(parkingLotId);
                if (parkingLot != null && parkingLot.getCommKey() != null && !parkingLot.getCommKey().trim().isEmpty()) {
                    return parkingLot.getCommKey();
                }
            }
        } catch (Exception e) {
            log.warn("根据停车场ID获取项目唯一编码失败，使用默认值", e);
        }
        return getCommKey();
    }

    @Override
    public FindPunitInfoResponse findPunitInfo() {
        String data = "";
        String res = post(DeLiYunMethodEnums.FIND_PUNIT_INFO.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, FindPunitInfoResponse.class);
        }
        return null;
    }

    @Override
    public List<FindParkGarageResponse> findParkGarage(FindParkGarageRequest findParkGarageRequest) {
        String data = JSONUtil.toJsonStr(findParkGarageRequest);
        String res = post(DeLiYunMethodEnums.FIND_PARK_GARAGE.getMethod(), data);
        if (res != null) {
            return JSONUtil.toList(res, FindParkGarageResponse.class);
        }
        return List.of();
    }

    @Override
    public FindParkInfoResponse findParkInfo(FindParkInfoRequest findParkInfoRequest) {
        String data = JSONUtil.toJsonStr(findParkInfoRequest);
        String res = post(DeLiYunMethodEnums.FIND_PARK_INFO.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, FindParkInfoResponse.class);
        }
        return null;
    }

    @Override
    public List<FindParkingInfoResponse> findParkingInfo(FindParkingInfoRequest findParkingInfoRequest) {
        String data = JSONUtil.toJsonStr(findParkingInfoRequest);
        String res = post(DeLiYunMethodEnums.FIND_PARKING_INFO.getMethod(), data);
        if (res != null) {
            return JSONUtil.toList(res, FindParkingInfoResponse.class);
        }
        return List.of();
    }

    @Override
    public List<FindCarListResponse> findCarList(FindCarListRequest findCarListRequest, String commKey) {
        String data = JSONUtil.toJsonStr(findCarListRequest);
        String res = post(DeLiYunMethodEnums.FIND_CAR_LIST.getMethod(), data, commKey);
        if (res != null) {
            return JSONUtil.toList(res, FindCarListResponse.class);
        }
        return List.of();
    }

    @Override
    public List<FindCarGroupResponse> findCarGroup() {
        String data = "";
        String res = post(DeLiYunMethodEnums.FIND_CAR_GROUP.getMethod(), data);
        if (res != null) {
            return JSONUtil.toList(res, FindCarGroupResponse.class);
        }
        return List.of();
    }

    @Override
    public List<FindCarChargeRuleResponse> findCarChargeRule(FindCarChargeRuleRequest findCarChargeRuleRequest) {
        String data = JSONUtil.toJsonStr(findCarChargeRuleRequest);
        String res = post(DeLiYunMethodEnums.FIND_CAR_CHARGE_RULE.getMethod(), data);
        if (res != null) {
            return JSONUtil.toList(res, FindCarChargeRuleResponse.class);
        }
        return List.of();
    }

    @Override
    public List<FindPoolParkListResponse> findPoolParkList(FindPoolParkListRequest request, String commKey) {
        String data = JSONUtil.toJsonStr(request);
        String res = post(DeLiYunMethodEnums.FIND_POOL_PARK_LIST.getMethod(), data, commKey);
        if (res != null) {
            // 是否可以转换为 FindPoolParkListResponse
            if (JSONUtil.isTypeJSONArray(res)) {
                return JSONUtil.toList(res, FindPoolParkListResponse.class);
            }
        }
        return List.of();
    }

    @Override
    public List<FindPoolParkRuleResponse> findPoolParkRule(FindPoolParkRuleRequest findPoolParkRuleRequest) {
        String data = JSONUtil.toJsonStr(findPoolParkRuleRequest);
        String res = post(DeLiYunMethodEnums.FIND_POOL_PARK_RULE.getMethod(), data);
        if (res != null) {
            return JSONUtil.toList(res, FindPoolParkRuleResponse.class);
        }
        return List.of();
    }

    @Override
    public List<FindBlackListResponse> findBlackList(FindBlackListRequest findBlackListRequest) {
        String data = JSONUtil.toJsonStr(findBlackListRequest);
        String res = post(DeLiYunMethodEnums.FIND_BLACK_LIST.getMethod(), data);
        if (res != null) {
            return JSONUtil.toList(res, FindBlackListResponse.class);
        }
        return List.of();
    }

    @Override
    public List<FindCycPayLogResponse> findCycPayLog(FindCycPayLogRequest findCycPayLogRequest) {
        String data = JSONUtil.toJsonStr(findCycPayLogRequest);
        String res = post(DeLiYunMethodEnums.FIND_CYC_PAY_LOG.getMethod(), data);
        if (res != null) {
            return JSONUtil.toList(res, FindCycPayLogResponse.class);
        }
        return List.of();
    }

    @Override
    public List<FindReChargeLogResponse> findReChargeLog(FindReChargeLogRequest findReChargeLogRequest) {
        String data = JSONUtil.toJsonStr(findReChargeLogRequest);
        String res = post(DeLiYunMethodEnums.FIND_RE_CHARGE_LOG.getMethod(), data);
        if (res != null) {
            return JSONUtil.toList(res, FindReChargeLogResponse.class);
        }
        return List.of();
    }

    @Override
    public List<FindPoolParkPayLogResponse> findPoolParkPayLog(FindPoolParkPayLogRequest findPoolParkPayLogRequest) {
        String data = JSONUtil.toJsonStr(findPoolParkPayLogRequest);
        String res = post(DeLiYunMethodEnums.FIND_POOL_PARK_PAY_LOG.getMethod(), data);
        if (res != null) {
            return JSONUtil.toList(res, FindPoolParkPayLogResponse.class);
        }
        return List.of();
    }

    @Override
    public FindPlateByCidResponse findPlateByCid(FindPlateByCidRequest findPlateByCidRequest) {
        String data = JSONUtil.toJsonStr(findPlateByCidRequest);
        String res = post(DeLiYunMethodEnums.FIND_PLATE_BY_CID.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, FindPlateByCidResponse.class);
        }
        return null;
    }

    @Override
    public List<FindParkingDeviceResponse> findParkingDevice() {
        String data = "";
        String res = post(DeLiYunMethodEnums.FIND_PARKING_DEVICE.getMethod(), data);
        if (res != null) {
            return JSONUtil.toList(res, FindParkingDeviceResponse.class);
        }
        return List.of();
    }

    @Override
    public FindParkingRateResponse findParkingRate(FindParkingRateRequest findParkingRateRequest) {
        String data = JSONUtil.toJsonStr(findParkingRateRequest);
        String res = post(DeLiYunMethodEnums.FIND_PARKING_RATE.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, FindParkingRateResponse.class);
        }
        return null;
    }

    @Override
    public List<FindParkingSpaceResponse> findParkingSpace(FindParkingSpaceRequest findParkingSpaceRequest) {
        String data = JSONUtil.toJsonStr(findParkingSpaceRequest);
        String res = post(DeLiYunMethodEnums.FIND_PARKING_SPACE.getMethod(), data);
        if (res != null) {
            return JSONUtil.toList(res, FindParkingSpaceResponse.class);
        }
        return null;
    }

    @Override
    public List<FindChannelsResponse> findChannels() {
        String data = "";
        String res = post(DeLiYunMethodEnums.FIND_CHANNELS.getMethod(), data);
        if (res != null) {
            return JSONUtil.toList(res, FindChannelsResponse.class);
        }
        return List.of();
    }

    @Override
    public List<FindBookCarsResponse> findBookCars(FindBookCarsRequest findBookCarsRequest) {
        String data = JSONUtil.toJsonStr(findBookCarsRequest);
        String res = post(DeLiYunMethodEnums.FIND_BOOK_CARS.getMethod(), data);
        if (res != null) {
            return JSONUtil.toList(res, FindBookCarsResponse.class);
        }
        return List.of();
    }

    @Override
    public List<FindShopListResponse> findShopList(FindShopListRequest findShopListRequest) {
        String data = JSONUtil.toJsonStr(findShopListRequest);
        String res = post(DeLiYunMethodEnums.FIND_SHOP_LIST.getMethod(), data);
        if (res != null) {
            return JSONUtil.toList(res, FindShopListResponse.class);
        }
        return List.of();
    }

    @Override
    public FindPreSaleMoneyResponse findPreSaleMoney(FindPreSaleMoneyRequest findPreSaleMoneyRequest) {
        String data = JSONUtil.toJsonStr(findPreSaleMoneyRequest);
        String res = post(DeLiYunMethodEnums.FIND_PRE_SALE_MONEY.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, FindPreSaleMoneyResponse.class);
        }
        return null;
    }

    @Override
    public List<FindPoolListResponse> findPoolList(FindPoolListRequest findPoolListRequest, String commKey) {
        String data = JSONUtil.toJsonStr(findPoolListRequest);
        String res = post(DeLiYunMethodEnums.FIND_POOL_LIST.getMethod(), data, commKey);
        if (res != null) {
            return JSONUtil.toList(res, FindPoolListResponse.class);
        }
        return List.of();
    }

    @Override
    public CarSaleResponse carSale(CarSaleRequest carSaleRequest) {
        String data = JSONUtil.toJsonStr(carSaleRequest);
        String res = post(DeLiYunMethodEnums.CAR_SALE.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, CarSaleResponse.class);
        }
        return null;
    }

    @Override
    public CancelSaleResponse cancelSale(CancelSaleRequest cancelSaleRequest) {
        String data = JSONUtil.toJsonStr(cancelSaleRequest);
        String res = post(DeLiYunMethodEnums.CANCEL_SALE.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, CancelSaleResponse.class);
        }
        return null;
    }

    @Override
    public SaveCarResponse saveCar(SaveCarRequest saveCarRequest) {
        String data = JSONUtil.toJsonStr(saveCarRequest);
        String res = post(DeLiYunMethodEnums.SAVE_CAR.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, SaveCarResponse.class);
        }
        return null;
    }

    @Override
    public SaveParkingSpaceCarResponse saveParkingSpaceCar(SaveParkingSpaceCarRequest saveParkingSpaceCarRequest, String commKey) {
        String data = JSONUtil.toJsonStr(saveParkingSpaceCarRequest);
        String res = post(DeLiYunMethodEnums.SAVE_PARKING_SPACE_CAR.getMethod(), data, commKey);
        if (res != null) {
            return JSONUtil.toBean(res, SaveParkingSpaceCarResponse.class);
        }
        return null;
    }

    @Override
    public DeleteCarResponse deleteCar(DeleteCarRequest deleteCarRequest) {
        String data = JSONUtil.toJsonStr(deleteCarRequest);
        String res = post(DeLiYunMethodEnums.DELETE_CAR.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, DeleteCarResponse.class);
        }
        return null;
    }

    @Override
    public DeleteParkingSpaceResponse deleteParkingSpace(DeleteParkingSpaceRequest deleteParkingSpaceRequest) {
        String data = JSONUtil.toJsonStr(deleteParkingSpaceRequest);
        String res = post(DeLiYunMethodEnums.DELETE_PARKING_SPACE.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, DeleteParkingSpaceResponse.class);
        }
        return null;
    }

    @Override
    public VehicleDelayResponse vehicleDelay(VehicleDelayRequest vehicleDelayRequest) {
        String data = JSONUtil.toJsonStr(vehicleDelayRequest);
        String res = post(DeLiYunMethodEnums.VEHICLE_DELAY.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, VehicleDelayResponse.class);
        }
        return null;
    }

    @Override
    public void parkDelay(ParkDelayRequest parkDelayRequest, String commKey) {
        String data = JSONUtil.toJsonStr(parkDelayRequest);
        String res = post(DeLiYunMethodEnums.PARK_DELAY.getMethod(), data, commKey);
        log.info("车位延期结果：{}", res);
    }

    @Override
    public SaveBlackListResponse saveBlackList(SaveBlackListRequest saveBlackListRequest) {
        String data = JSONUtil.toJsonStr(saveBlackListRequest);
        String res = post(DeLiYunMethodEnums.SAVE_BLACK_LIST.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, SaveBlackListResponse.class);
        }
        return null;
    }

    @Override
    public DeleteBlackListResponse deleteBlackList(DeleteBlackListRequest deleteBlackListRequest) {
        String data = JSONUtil.toJsonStr(deleteBlackListRequest);
        String res = post(DeLiYunMethodEnums.DELETE_BLACK_LIST.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, DeleteBlackListResponse.class);
        }
        return null;
    }

    @Override
    public ParkCarBookResponse parkCarBook(ParkCarBookRequest parkCarBookRequest) {
        String data = JSONUtil.toJsonStr(parkCarBookRequest);
        String res = post(DeLiYunMethodEnums.PARK_CAR_BOOK.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, ParkCarBookResponse.class);
        }
        return null;
    }

    @Override
    public CancelCarReservationResponse cancelCarReservation(CancelCarReservationRequest cancelCarReservationRequest) {
        String data = JSONUtil.toJsonStr(cancelCarReservationRequest);
        String res = post(DeLiYunMethodEnums.CANCEL_CAR_RESERVATION.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, CancelCarReservationResponse.class);
        }
        return null;
    }

    @Override
    public ReleaseCouponsResponse releaseCoupons(ReleaseCouponsRequest releaseCouponsRequest) {
        String data = JSONUtil.toJsonStr(releaseCouponsRequest);
        String res = post(DeLiYunMethodEnums.RELEASE_COUPONS.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, ReleaseCouponsResponse.class);
        }
        return null;
    }

    @Override
    public HandCarInResponse handCarIn(HandCarInRequest handCarInRequest) {
        String data = JSONUtil.toJsonStr(handCarInRequest);
        String res = post(DeLiYunMethodEnums.HAND_CAR_IN.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, HandCarInResponse.class);
        }
        return null;
    }

    @Override
    public LockCarResponse lockCar(LockCarRequest lockCarRequest) {
        String data = JSONUtil.toJsonStr(lockCarRequest);
        String res = post(DeLiYunMethodEnums.LOCK_CAR.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, LockCarResponse.class);
        }
        return null;
    }

    @Override
    public CarInResponse carIn(CarInRequest carInRequest) {
        String data = JSONUtil.toJsonStr(carInRequest);
        String res = post(DeLiYunMethodEnums.CAR_IN.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, CarInResponse.class);
        }
        return null;
    }

    @Override
    public CarOutResponse carOut(CarOutRequest carOutRequest) {
        String data = JSONUtil.toJsonStr(carOutRequest);
        String res = post(DeLiYunMethodEnums.CAR_OUT.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, CarOutResponse.class);
        }
        return null;
    }

    @Override
    public DeleteParkingCarinfoResponse deleteParkingCarinfo(DeleteParkingCarinfoRequest deleteParkingCarinfoRequest) {
        String data = JSONUtil.toJsonStr(deleteParkingCarinfoRequest);
        String res = post(DeLiYunMethodEnums.DELETE_PARKING_CARINFO.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, DeleteParkingCarinfoResponse.class);
        }
        return null;
    }

    @Override
    public SaveCarGroupResponse saveCarGroup(SaveCarGroupRequest saveCarGroupRequest) {
        String data = JSONUtil.toJsonStr(saveCarGroupRequest);
        String res = post(DeLiYunMethodEnums.SAVE_CAR_GROUP.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, SaveCarGroupResponse.class);
        }
        return null;
    }

    @Override
    public DeleteCarGroupResponse deleteCarGroup(DeleteCarGroupRequest deleteCarGroupRequest) {
        String data = JSONUtil.toJsonStr(deleteCarGroupRequest);
        String res = post(DeLiYunMethodEnums.DELETE_CAR_GROUP.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, DeleteCarGroupResponse.class);
        }
        return null;
    }

    @Override
    public SaveGarageResponse saveGarage(SaveGarageRequest saveGarageRequest) {
        String data = JSONUtil.toJsonStr(saveGarageRequest);
        String res = post(DeLiYunMethodEnums.SAVE_GARAGE.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, SaveGarageResponse.class);
        }
        return null;
    }

    @Override
    public DeleteGarageResponse deleteGarage(DeleteGarageRequest deleteGarageRequest) {
        String data = JSONUtil.toJsonStr(deleteGarageRequest);
        String res = post(DeLiYunMethodEnums.DELETE_GARAGE.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, DeleteGarageResponse.class);
        }
        return null;
    }

    @Override
    public SaveCarGarageResponse saveCarGarage(SaveCarGarageRequest saveCarGarageRequest) {
        String data = JSONUtil.toJsonStr(saveCarGarageRequest);
        String res = post(DeLiYunMethodEnums.SAVE_CAR_GARAGE.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, SaveCarGarageResponse.class);
        }
        return null;
    }

    @Override
    public DelCarGarageResponse delCarGarage(DelCarGarageRequest delCarGarageRequest) {
        String data = JSONUtil.toJsonStr(delCarGarageRequest);
        String res = post(DeLiYunMethodEnums.DEL_CAR_GARAGE.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, DelCarGarageResponse.class);
        }
        return null;
    }

    @Override
    public UpdateCardResponse updateCard(UpdateCardRequest updateCardRequest) {
        String data = JSONUtil.toJsonStr(updateCardRequest);
        String res = post(DeLiYunMethodEnums.UPDATE_CARD.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, UpdateCardResponse.class);
        }
        return null;
    }

    @Override
    public UpdateParkingInfoResponse updateParkingInfo(UpdateParkingInfoRequest updateParkingInfoRequest) {
        String data = JSONUtil.toJsonStr(updateParkingInfoRequest);
        String res = post(DeLiYunMethodEnums.UPDATE_PARKING_INFO.getMethod(), data);
        if (res != null) {
            return JSONUtil.toBean(res, UpdateParkingInfoResponse.class);
        }
        return null;
    }

    public static String sign(String data, String timestamp, String commKey) {
        StringBuilder signData = new StringBuilder();
        signData.append("accessKeyID=").append(API_KEY);
        signData.append("&commKey=").append(commKey);
        signData.append("&data=").append(data);
        signData.append("&timestamp=").append(timestamp);
        signData.append("&version=").append("3.0");
        signData.append("&accessKeySecret=").append(API_SECRET);
        log.info("signData:{}", signData);
        return MD5.create().digestHex(signData.toString());
    }

    public String post(String method, String data) {
        return post(method, data, getCommKey());
    }

    public static String post(String method, String data, String commKey) {
        try {
            if (StrUtil.isBlank(commKey)) {
                throw new ApiException("commKey is null");
            }
            String timestamp = String.valueOf(DateUtil.currentSeconds());
            String param = URLEncoder.encode(data, StandardCharsets.UTF_8);
            String url_param = API_URL + method + "?accessKeyID=" + API_KEY + "&version=3.0&timestamp=" + timestamp + "&commKey=" + commKey + "&sign=" + sign(param, timestamp, commKey);
            log.info("请求地址：{}", url_param);
            HttpRequest request = HttpRequest.post(url_param);
            request.form("data", param);
            log.info("请求参数：{}", param);
            String res = request.execute().body();
            log.info("请求结果：{}", res);
            if (res.contains("\"ecode\":0")) {
                JSONObject jsonObject = JSONUtil.parseObj(res);
                if (!"null".equals(jsonObject.get("data")) && jsonObject.get("data") != null) {
                    return jsonObject.get("data").toString();
                }
                return null;
            } else {
                log.error("请求失败：{}", res);
            }
            return res;
        } catch (Exception e) {
            log.error("请求异常：{}", e.getMessage());
        }
        return null;
    }
}
