package cn.harry.modular.deliyun.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class FindCarGroupResponse {
//    id Long 分组 ID
//    gname String 分组名称
//    cardTypeId String 计费类型
//    createDate Long 创建时间（yyyy-MM-dd HH:mm:ss）
//    lastUpdateDate Long 最后修改时间（yyyy-MM-dd HH:mm:ss）
    /**
     * id
     */
    @Schema(description = "id")
    private Long id;
    /**
     * 分组名称
     */
    @Schema(description = "分组名称")
    private String gname;
    /**
     * 计费类型
     */
    @Schema(description = "计费类型")
    private String cardTypeId;
    /**
     * 创建时间（yyyy-MM-dd HH:mm:ss）
     */
    @Schema(description = "创建时间（yyyy-MM-dd HH:mm:ss）")
    private String createDate;
    /**
     * 最后修改时间（yyyy-MM-dd HH:mm:ss）
     */
    @Schema(description = "最后修改时间（yyyy-MM-dd HH:mm:ss）")
    private String lastUpdateDate;

}
