package cn.harry.modular.deliyun.req;

import cn.harry.modular.deliyun.base.DeliyunBasePageModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FindParkingInfoRequest extends DeliyunBasePageModel {
    @Schema(description = "车牌号码")
    private String plateNum;
    @Schema(description = "计费类型ID（详见计费类型说明）")
    private Long cardTypeId;
    @Schema(description = "车类型ID（详见车类型说明）")
    private Long carTypeId;
    @Schema(description = "入场开始时间（时间戳，单位:秒）")
    private Long inTimeStart;
    @Schema(description = "入场结束时间（时间戳，单位:秒）")
    private Long inTimeEnd;
}
