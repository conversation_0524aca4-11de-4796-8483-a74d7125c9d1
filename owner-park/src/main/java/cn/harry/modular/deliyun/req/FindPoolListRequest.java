package cn.harry.modular.deliyun.req;

import cn.harry.modular.deliyun.base.DeliyunBasePageModel;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @公众号 Harry技术
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FindPoolListRequest extends DeliyunBasePageModel {
    //    plateNum String 20 车牌号(精准查询)
//    poolName String 20 所属车位池名称(模糊查询)
    @Schema(description = "车牌号码")
    private String plateNum;

    @Schema(description = "所属车位池名称")
    private String poolName;

    @NotNull
    @Schema(description = "commKey")
    private String commKey;
}
