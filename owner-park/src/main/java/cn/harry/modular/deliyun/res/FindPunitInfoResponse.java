package cn.harry.modular.deliyun.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 4.1.findPunitInfo获取停车场基本信息
 *
 * 参数 {"area":"道里区","address":"哈尔滨市香坊区通乡街174号","lng":"126.543435","city":"哈尔滨市","pname":"康泰大库","parkEmptyCount":2677,"isOnline":1,"id":"16653","parkEmptyUpdateDate":1746782633,"parkTotalCount":3000,"lat":"45.719924","outDelay":15}
 *
 * <AUTHOR>
 * @公众号 Harry技术
 */
@Data
public class FindPunitInfoResponse {
    /**
     * 停车场名称
     */
    @Schema(description = "停车场名称")
    private String area;
    /**
     * 停车场地址
     */
    @Schema(description = "停车场地址")
    private String address;
    /**
     * 经度
     */
    @Schema(description = "经度")
    private String lng;
    /**
     * 城市
     */
    @Schema(description = "城市")
    private String city;
    /**
     * 停车场名称
     */
    @Schema(description = "停车场名称")
    private String pname;
    /**
     * 剩余车位
     */
    @Schema(description = "剩余车位")
    private Integer parkEmptyCount;
    /**
     * 1-在线 0-离线
     */
    @Schema(description = "1-在线 0-离线")
    private Integer isOnline;
    /**
     * 停车场id
     */
    @Schema(description = "停车场id")
    private String id;
    /**
     * 最后更新时间
     */
    @Schema(description = "最后更新时间")
    private Integer parkEmptyUpdateDate;
    /**
     * 总车位
     */
    @Schema(description = "总车位")
    private Integer parkTotalCount;
    /**
     * 纬度
     */
    @Schema(description = "纬度")
    private String lat;
    /**
     * 缴费后允许滞留时间(单位：分钟)
     */
    @Schema(description = "缴费后允许滞留时间(单位：分钟)")
    private Integer outDelay;
}
