<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="Vue3 + Vite5 + TypeScript5 + Element-Plus 的后台管理模板，配套接口文档和后端源码，vue-element-admin 的 Vue3 版本"
    />
    <meta
      name="keywords"
      content="vue,element-plus,typescript,vue-element-admin,harry-vue"
    />
    <title>harry-vue</title>
  </head>

  <body>
    <div id="app">
      <!--加载动画-->
      <div class="mesh-loader">
        <div class="set-one">
          <div class="circle"></div>
          <div class="circle"></div>
        </div>
        <div class="set-two">
          <div class="circle"></div>
          <div class="circle"></div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>

    <style>
      html,
      body,
      #app {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
      }

      .mesh-loader {
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      .mesh-loader .circle {
        position: absolute;
        width: 25px;
        height: 25px;
        margin: -12.5px;
        background: #03a9f4;
        border-radius: 50%;
        animation: mesh 3s ease-in-out infinite -1.5s;
      }

      .mesh-loader > div .circle:last-child {
        animation-delay: 0s;
      }

      .mesh-loader > div {
        position: absolute;
        top: 50%;
        left: 50%;
      }

      .mesh-loader > div:last-child {
        transform: rotate(90deg);
      }

      @keyframes mesh {
        0% {
          transform: rotate(0);
          transform-origin: 50% -100%;
        }

        50% {
          transform: rotate(360deg);
          transform-origin: 50% -100%;
        }

        50.00001% {
          transform: rotate(0deg);
          transform-origin: 50% 200%;
        }

        100% {
          transform: rotate(360deg);
          transform-origin: 50% 200%;
        }
      }
    </style>
  </body>
</html>
