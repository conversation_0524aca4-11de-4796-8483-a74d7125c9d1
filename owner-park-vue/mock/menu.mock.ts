import { defineMock } from "./base";

export default defineMock([
  {
    url: "/sys/menu/getRouters",
    method: ["GET"],
    body: {
      "code": 200,
      "message": "操作成功",
      "data": [
        {
          "name": "System",
          "path": "/system",
          "component": "Layout",
          "meta": {
            "title": "系统管理",
            "icon": "system",
            "hidden": false,
            "alwaysShow": false,
            "params": null
          },
          "children": [
            {
              "name": "User",
              "path": "user",
              "component": "system/user/index",
              "meta": {
                "title": "用户管理",
                "icon": "user",
                "hidden": false,
                "keepAlive": true,
                "alwaysShow": false,
                "params": null
              }
            },
            {
              "name": "Role",
              "path": "role",
              "component": "system/role/index",
              "meta": {
                "title": "角色管理",
                "icon": "role",
                "hidden": false,
                "keepAlive": true,
                "alwaysShow": false,
                "params": null
              }
            },
            {
              "name": "Menu",
              "path": "menu",
              "component": "system/menu/index",
              "meta": {
                "title": "菜单管理",
                "icon": "menu",
                "hidden": false,
                "keepAlive": true,
                "alwaysShow": false,
                "params": null
              }
            },
            {
              "name": "Dept",
              "path": "dept",
              "component": "system/dept/index",
              "meta": {
                "title": "部门管理",
                "icon": "tree",
                "hidden": false,
                "keepAlive": true,
                "alwaysShow": false,
                "params": null
              }
            },
            {
              "name": "Dict",
              "path": "dict",
              "component": "system/dict/index",
              "meta": {
                "title": "字典管理",
                "icon": "dict",
                "hidden": false,
                "keepAlive": true,
                "alwaysShow": false,
                "params": null
              }
            },
            {
              "name": "Dict-data",
              "path": "dict-data",
              "component": "system/dict/data",
              "meta": {
                "title": "字典数据",
                "icon": "dict-data",
                "hidden": true,
                "keepAlive": true,
                "alwaysShow": false,
                "params": null
              }
            },
            {
              "name": "Config",
              "path": "config",
              "component": "system/config/index",
              "meta": {
                "title": "系统配置",
                "icon": "config",
                "hidden": false,
                "keepAlive": true,
                "alwaysShow": false,
                "params": null
              }
            }
          ]
        }
      ]
    },
  },
  {
    url: "/sys/menu/list",
    method: ["GET"],
    body:
      {
        "code": 200,
        "message": "操作成功",
        "data": [
          {
            "id": 1,
            "pid": 0,
            "name": "系统管理",
            "permission": "",
            "icon": "system",
            "type": 0,
            "uri": "",
            "sort": 1,
            "path": "system",
            "status": "1",
            "alwaysShow": null,
            "keepAlive": null,
            "params": null,
            "createTime": "2024-12-02 14:56:50",
            "valid": 1,
            "children": [
              {
                "id": 100,
                "pid": 1,
                "name": "用户管理",
                "permission": "sys_user_page",
                "icon": "user",
                "type": 1,
                "uri": "system/user/index",
                "sort": 1,
                "path": "user",
                "status": "1",
                "alwaysShow": null,
                "keepAlive": 1,
                "params": null,
                "createTime": "2024-12-02 14:56:50",
                "valid": 1,
                "children": [
                  {
                    "id": 1001,
                    "pid": 100,
                    "name": "用户查询",
                    "permission": "sys_user_get",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 1,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1002,
                    "pid": 100,
                    "name": "用户新增",
                    "permission": "sys_user_add",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 2,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1003,
                    "pid": 100,
                    "name": "用户修改",
                    "permission": "sys_user_edit",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 3,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1004,
                    "pid": 100,
                    "name": "用户删除",
                    "permission": "sys_user_del",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 4,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1005,
                    "pid": 100,
                    "name": "用户导出",
                    "permission": "sys_user_export",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 5,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1006,
                    "pid": 100,
                    "name": "用户导入",
                    "permission": "sys_user_import",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 6,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1007,
                    "pid": 100,
                    "name": "重置密码",
                    "permission": "sys_user_reset",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 7,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  }
                ]
              },
              {
                "id": 101,
                "pid": 1,
                "name": "角色管理",
                "permission": "sys_role_page",
                "icon": "role",
                "type": 1,
                "uri": "system/role/index",
                "sort": 2,
                "path": "role",
                "status": "1",
                "alwaysShow": null,
                "keepAlive": 1,
                "params": null,
                "createTime": "2024-12-02 14:56:50",
                "valid": 1,
                "children": [
                  {
                    "id": 1008,
                    "pid": 101,
                    "name": "角色查询",
                    "permission": "sys_role_get",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 1,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1009,
                    "pid": 101,
                    "name": "角色新增",
                    "permission": "sys_role_add",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 2,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1010,
                    "pid": 101,
                    "name": "角色修改",
                    "permission": "sys_role_edit",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 3,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1011,
                    "pid": 101,
                    "name": "角色删除",
                    "permission": "sys_role_del",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 4,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1012,
                    "pid": 101,
                    "name": "角色导出",
                    "permission": "sys_role_export",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 5,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  }
                ]
              },
              {
                "id": 102,
                "pid": 1,
                "name": "菜单管理",
                "permission": "sys_menu_page",
                "icon": "menu",
                "type": 1,
                "uri": "system/menu/index",
                "sort": 3,
                "path": "menu",
                "status": "1",
                "alwaysShow": null,
                "keepAlive": 1,
                "params": null,
                "createTime": "2024-12-02 14:56:50",
                "valid": 1,
                "children": [
                  {
                    "id": 1013,
                    "pid": 102,
                    "name": "菜单查询",
                    "permission": "sys_menu_get",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 1,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1014,
                    "pid": 102,
                    "name": "菜单新增",
                    "permission": "sys_menu_add",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 2,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1015,
                    "pid": 102,
                    "name": "菜单修改",
                    "permission": "sys_menu_edit",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 3,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1016,
                    "pid": 102,
                    "name": "菜单删除",
                    "permission": "sys_menu_del",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 4,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  }
                ]
              },
              {
                "id": 103,
                "pid": 1,
                "name": "部门管理",
                "permission": "sys_dept_page",
                "icon": "tree",
                "type": 1,
                "uri": "system/dept/index",
                "sort": 4,
                "path": "dept",
                "status": "1",
                "alwaysShow": null,
                "keepAlive": 1,
                "params": null,
                "createTime": "2024-12-02 14:56:50",
                "valid": 1,
                "children": [
                  {
                    "id": 1017,
                    "pid": 103,
                    "name": "部门查询",
                    "permission": "sys_dept_get",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 1,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1018,
                    "pid": 103,
                    "name": "部门新增",
                    "permission": "sys_dept_add",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 2,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1019,
                    "pid": 103,
                    "name": "部门修改",
                    "permission": "sys_dept_edit",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 3,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1020,
                    "pid": 103,
                    "name": "部门删除",
                    "permission": "sys_dept_del",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 4,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  }
                ]
              },
              {
                "id": 105,
                "pid": 1,
                "name": "字典管理",
                "permission": "sys_dict_page",
                "icon": "dict",
                "type": 1,
                "uri": "system/dict/index",
                "sort": 6,
                "path": "dict",
                "status": "1",
                "alwaysShow": null,
                "keepAlive": 1,
                "params": null,
                "createTime": "2024-12-02 14:56:50",
                "valid": 1,
                "children": [
                  {
                    "id": 1026,
                    "pid": 105,
                    "name": "字典查询",
                    "permission": "sys_dict_get",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 1,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1027,
                    "pid": 105,
                    "name": "字典新增",
                    "permission": "sys_dict_add",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 2,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1028,
                    "pid": 105,
                    "name": "字典修改",
                    "permission": "sys_dict_edit",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 3,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1029,
                    "pid": 105,
                    "name": "字典删除",
                    "permission": "sys_dict_del",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 4,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1030,
                    "pid": 105,
                    "name": "字典导出",
                    "permission": "sys_dict_export",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 5,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  }
                ]
              },
              {
                "id": 106,
                "pid": 1,
                "name": "字典数据",
                "permission": "sys_dict_data_page",
                "icon": "dict-data",
                "type": 1,
                "uri": "system/dict/data",
                "sort": 8,
                "path": "dict-data",
                "status": "0",
                "alwaysShow": null,
                "keepAlive": 1,
                "params": null,
                "createTime": "2024-12-20 08:33:50",
                "valid": 1,
                "children": []
              },
              {
                "id": 107,
                "pid": 1,
                "name": "系统配置",
                "permission": "sys_config_page",
                "icon": "config",
                "type": 1,
                "uri": "system/config/index",
                "sort": 7,
                "path": "config",
                "status": "1",
                "alwaysShow": null,
                "keepAlive": 1,
                "params": null,
                "createTime": "2024-12-02 14:56:50",
                "valid": 1,
                "children": [
                  {
                    "id": 1031,
                    "pid": 107,
                    "name": "系统配置查询",
                    "permission": "sys_config_get",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 1,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1032,
                    "pid": 107,
                    "name": "系统配置新增",
                    "permission": "sys_config_add",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 2,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1033,
                    "pid": 107,
                    "name": "系统配置修改",
                    "permission": "sys_config_edit",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 3,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1034,
                    "pid": 107,
                    "name": "系统配置删除",
                    "permission": "sys_config_del",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 4,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  },
                  {
                    "id": 1035,
                    "pid": 107,
                    "name": "系统配置导出",
                    "permission": "sys_config_export",
                    "icon": "",
                    "type": 2,
                    "uri": "",
                    "sort": 5,
                    "path": "",
                    "status": "1",
                    "alwaysShow": null,
                    "keepAlive": null,
                    "params": null,
                    "createTime": "2024-12-02 14:56:50",
                    "valid": 1,
                    "children": []
                  }
                ]
              }
            ]
          },
          {
            "id": 1043,
            "pid": 1036,
            "name": "外链",
            "permission": null,
            "icon": "close_other",
            "type": 3,
            "uri": null,
            "sort": 1,
            "path": "http://127.0.0.1:9000/doc.html",
            "status": "1",
            "alwaysShow": 0,
            "keepAlive": 1,
            "params": [],
            "createTime": "2024-12-20 13:25:46",
            "valid": 1,
            "children": []
          }
        ]
      }
    ,
  },

]);
