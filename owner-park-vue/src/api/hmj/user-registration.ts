import request from "@/utils/request";

const USERREGISTRATION_BASE_URL = "/hmj/userRegistration";

const UserRegistrationAPI = {
    /** 获取用户信息登记分页数据 */
    getPage(queryParams?: UserRegistrationPageQuery) {
        return request<any, PageResult<UserRegistrationPageVO[]>>({
            url: `${USERREGISTRATION_BASE_URL}/page`,
            method: "get",
            params: queryParams,
        });
    },
    /**
     * 获取用户信息登记表单数据
     *
     * @param id UserRegistrationID
     * @returns UserRegistration表单数据
     */
    getFormData(id: number) {
        return request<any, UserRegistrationForm>({
            url: `${USERREGISTRATION_BASE_URL}/${id}`,
            method: "get",
        });
    },

    /** 添加用户信息登记*/
    add(data: UserRegistrationForm) {
        return request({
            url: `${USERREGISTRATION_BASE_URL}`,
            method: "post",
            data: data,
        });
    },

    /**
     * 更新用户信息登记
     *
     * @param id UserRegistrationID
     * @param data UserRegistration表单数据
     */
     update(data: UserRegistrationForm) {
        return request({
            url: `${USERREGISTRATION_BASE_URL}`,
            method: "put",
            data: data,
        });
    },

    /**
     * 批量删除用户信息登记，多个以英文逗号(,)分割
     *
     * @param ids 用户信息登记ID字符串，多个以英文逗号(,)分割
     */
     deleteByIds(ids: string) {
        return request({
            url: `${USERREGISTRATION_BASE_URL}/${ids}`,
            method: "delete",
        });
    }
}

export default UserRegistrationAPI;

/** 用户信息登记分页查询参数 */
export interface UserRegistrationPageQuery extends PageQuery {
  /** 车库ID */
  garageId?: number;
}

/** 用户信息登记表单对象 */
export interface UserRegistrationForm {
    id?:  number;
    /** 车库ID */
    garageId?:  number;
    /** 车主 */
    pname?:  string;
    /** 手机号 */
    mobile?:  string;
    /** 地址 */
    addr?:  string;
    /** 业主凭证 */
    ownerCertificate?:  string;
    /** 车牌号码 */
    plateNum?:  string;
    /** 车位ID */
    parkId?:  number;
    /** 车位号 */
    parkNum?:  string;
    /** 计费类型 1.日租 2.月租 3.季租 4.年租 */
    billingType?:  string;
    /** 开始时间（yyyy-MM-dd） */
    beginDate?:  string;
    /** 结束时间（yyyy-MM-dd） */
    endDate?:  string;
    /** 应缴费用 */
    shouldPay?:  string;
    /** 用户ID */
    userId?:  number;
    /** openid */
    openid?:  string;
    /** 状态 */
    status?:  string;
    /** 是否续费 0:登记 1:续费 */
    isRenewal?:  number;
    /** 创建时间 */
    createTime?:  Date;
    /** 创建人 */
    createBy?:  string;
    /** 修改时间 */
    modifyTime?:  Date;
    /** 修改人 */
    modifyBy?:  string;
}

/** 用户信息登记分页对象 */
export interface UserRegistrationPageVO {
    id?: number;
    /** 车库ID */
    garageId?: number;
    /** 车主 */
    pname?: string;
    /** 手机号 */
    mobile?: string;
    /** 地址 */
    addr?: string;
    /** 业主凭证 */
    ownerCertificate?: string;
    /** 车牌号码 */
    plateNum?: string;
    /** 车位ID */
    parkId?: number;
    /** 车位号 */
    parkNum?: string;
    /** 计费类型 1.日租 2.月租 3.季租 4.年租 */
    billingType?: string;
    /** 开始时间（yyyy-MM-dd） */
    beginDate?: string;
    /** 结束时间（yyyy-MM-dd） */
    endDate?: string;
    /** 应缴费用 */
    shouldPay?: string;
    /** 用户ID */
    userId?: number;
    /** openid */
    openid?: string;
    /** 状态 */
    status?: string;
    /** 是否续费 0:登记 1:续费 */
    isRenewal?: number;
    /** 创建时间 */
    createTime?: Date;
    /** 创建人 */
    createBy?: string;
    /** 修改时间 */
    modifyTime?: Date;
    /** 修改人 */
    modifyBy?: string;
}
