import request from "@/utils/request";

const USER_BASE_URL = "/hmj/owner";

const OwnerAPI = {

  /**
   * 获取用户分页列表
   *
   * @param queryParams 查询参数
   */
  getPage(queryParams: UserPageQuery) {
    return request<any, PageResult<UserPageVO[]>>({
      url: `${USER_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 获取用户表单详情
   *
   * @param userId 用户ID
   * @returns 用户表单详情
   */
  getFormData(userId: number) {
    return request<any, UserForm>({
      url: `${USER_BASE_URL}/${userId}`,
      method: "get",
    });
  },

  /**
   * 更新用户信息
   * @param data
   */
  updateUserInfo(data: UserForm) {
    return request({
      url: `/sys/user/updateUserInfo`,
      method: "put",
      data: data,
    });
  },

  /**
   * 关联车位
   *
   * @param userId 用户ID
   * @param parkingIds 车位ID数组
   */
  associateParking(userId: number, parkingIds: number[]) {
    return request({
      url: `${USER_BASE_URL}/${userId}/parking/associate`,
      method: "post",
      data: { parkingIds },
    });
  },

  /**
   * 解绑车位
   *
   * @param userId 用户ID
   * @param parkingIds 车位ID数组
   */
  unbindParking(userId: number, parkingIds: number[]) {
    return request({
      url: `${USER_BASE_URL}/${userId}/parking/unbind`,
      method: "post",
      data: { parkingIds },
    });
  },

  /**
   * 获取未关联的车位列表
   *
   * @param queryParams 查询参数
   */
  getUnassociatedParking(queryParams: UnassociatedParkingQuery) {
    return request<any, PageResult<ParkingInfo[]>>({
      url: `/hmj/poolPark/unassociated-parking`,
      method: "get",
      params: queryParams,
    });
  },
};

export default OwnerAPI;
/**
 * 用户分页查询对象
 */
export interface UserPageQuery extends PageQuery {
  /** 搜索关键字 */
  username?: string;

  /** 用户状态 */
  status?: string;
  /** 手机号 */
  phone?: string;
}

/** 车位信息 */
export interface ParkingInfo {
  /** 车位ID */
  id?: number;
  /** 车位号 */
  parkName?: string;
  /** 车位池名称 */
  poolName?: string;
  /** 车库名称 */
  pgname?: string;
  /** 车位类型(0:公用车位, 1:私家车位) */
  parkType?: number;
  /** 绑定的车牌号 */
  plateNums?: string;
  /** 剩余车位数 */
  emptyNum?: number;
  /** 有效期开始时间 */
  beginDateStr?: string;
  /** 有效期结束时间 */
  endDateStr?: string;
  /** 过期状态：0-正常，1-即将到期，2-已到期，3-未知 */
  expireStatus?: number;
}

/** 用户分页对象 */
export interface UserPageVO {
  /** 用户头像URL */
  avatar?: string;
  /** 创建时间 */
  createTime?: Date;
  /** 部门名称 */
  deptName?: string;
  /** 用户邮箱 */
  email?: string;
  /** 性别 */
  sexLabel?: string;
  /** 用户ID */
  id?: number;
  /** 手机号 */
  phone?: string;
  /** 用户昵称 */
  nickName?: string;
  /** 角色名称，多个使用英文逗号(,)分割 */
  roleNames?: string;
  /** 用户状态(1:启用;0:禁用) */
  status?: string;
  /** 用户名 */
  username?: string;
  /** 关联的车位信息列表 */
  parkingList?: ParkingInfo[];
  /** 关联的车牌号列表 */
  plateNumbers?: string[];
}

/** 用户表单类型 */
export interface UserForm {
  /** 用户头像 */
  avatar?: string;
  /** 部门ID */
  deptId?: number;
  /** 部门名称 */
  deptName?: string;
  /** 邮箱 */
  email?: string;
  /** 性别 */
  sex?: string;
  /** 用户ID */
  id?: number;
  /** 手机号 */
  phone?: string;
  /** 昵称 */
  nickName?: string;
  /** 角色ID集合 */
  roleIds?: number[];
  /** 用户状态(1:正常;0:禁用) */
  status?: string;
  /** 用户名 */
  username?: string;
  /** 密码 */
  password?: string;
}
/** 未关联车位查询参数 */
export interface UnassociatedParkingQuery extends PageQuery {
  /** 搜索关键词（车位号、车位池、车牌号） */
  keyword?: string;
}
