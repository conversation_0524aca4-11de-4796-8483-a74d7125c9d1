import request from "@/utils/request";

const PARKINGLOT_BASE_URL = "/hmj/parkingLot";

const ParkingLotAPI = {
    /** 获取停车场信息分页数据 */
    getPage(queryParams?: ParkingLotPageQuery) {
        return request<any, PageResult<ParkingLotPageVO[]>>({
            url: `${PARKINGLOT_BASE_URL}/page`,
            method: "get",
            params: queryParams,
        });
    },
    /**
     * 获取停车场信息表单数据
     *
     * @param id ParkingLotID
     * @returns ParkingLot表单数据
     */
    getFormData(id: number) {
        return request<any, ParkingLotForm>({
            url: `${PARKINGLOT_BASE_URL}/${id}`,
            method: "get",
        });
    },

    /** 添加停车场信息*/
    add(data: ParkingLotForm) {
        return request({
            url: `${PARKINGLOT_BASE_URL}`,
            method: "post",
            data: data,
        });
    },

    /**
     * 更新停车场信息
     *
     * @param id ParkingLotID
     * @param data ParkingLot表单数据
     */
     update(data: ParkingLotForm) {
        return request({
            url: `${PARKINGLOT_BASE_URL}`,
            method: "put",
            data: data,
        });
    },

    /**
     * 批量删除停车场信息，多个以英文逗号(,)分割
     *
     * @param ids 停车场信息ID字符串，多个以英文逗号(,)分割
     */
     deleteByIds(ids: string) {
        return request({
            url: `${PARKINGLOT_BASE_URL}/${ids}`,
            method: "delete",
        });
    },

    /**
     * 获取车库列表
     */
    getGarageList() {
        return request<any, GarageOption[]>({
            url: `${PARKINGLOT_BASE_URL}/garage`,
            method: "get",
        });
    },

    /**
     * 获取车库二维码
     * @param id 车库ID
     * @param scene 场景值
     * @param pages 页面路径
     */
    getGarageQrcode(id: number, scene?: string, pages?: string) {
        return request<any, string>({
            url: `${PARKINGLOT_BASE_URL}/garage/${id}/qrcode`,
            method: "get",
            params: { scene, pages }
        });
    }
}

export default ParkingLotAPI;

/** 车库选项 */
export interface GarageOption {
    /** 车库ID */
    id: number;
    /** 车库名称 */
    pname: string;
    /** 项目唯一编码 */
    commKey?: string;
    /** 状态 0 禁用 1 启用 */
    status?: number;
}

/** 停车场信息分页查询参数 */
export interface ParkingLotPageQuery extends PageQuery {
    /** 停车场名称 */
    pname?: string;
}

/** 停车场信息表单对象 */
export interface ParkingLotForm {
    /** 停车场ID */
    id?:  number;
    /** 停车场名称 */
    pname?:  string;
    /** 所在区（道里区等） */
    parea?:  string;
    /** 城市（哈尔滨市等） */
    city?:  string;
    /** 详细地址 */
    address?:  string;
    /** 经度 */
    lng?:  number;
    /** 纬度 */
    lat?:  number;
    /** 总车位数量 */
    parkTotalCount?:  number;
    /** 空车位数量 */
    parkEmptyCount?:  number;
    /** 空车位信息更新时间戳 */
    parkEmptyUpdateDate?:  number;
    /** 是否在线（1：在线，0：离线） */
    isOnline?:  number;
    /** 出场延迟时间（分钟） */
    outDelay?:  number;
    /** 项目唯一编码 */
    commKey?:  string;
}

/** 停车场信息分页对象 */
export interface ParkingLotPageVO {
    /** 停车场ID */
    id?: number;
    /** 停车场名称 */
    pname?: string;
    /** 所在区（道里区等） */
    parea?: string;
    /** 城市（哈尔滨市等） */
    city?: string;
    /** 详细地址 */
    address?: string;
    /** 经度 */
    lng?: number;
    /** 纬度 */
    lat?: number;
    /** 总车位数量 */
    parkTotalCount?: number;
    /** 空车位数量 */
    parkEmptyCount?: number;
    /** 空车位信息更新时间戳 */
    parkEmptyUpdateDate?: number;
    /** 是否在线（1：在线，0：离线） */
    isOnline?: number;
    /** 出场延迟时间（分钟） */
    outDelay?: number;
    /** 项目唯一编码 */
    commKey?: string;
}
