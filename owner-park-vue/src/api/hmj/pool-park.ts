import request from "@/utils/request";

const POOLPARK_BASE_URL = "/hmj/poolPark";

const PoolParkAPI = {
    /** 获取车位信息分页数据 */
    getPage(queryParams?: PoolParkPageQuery) {
        return request<any, PageResult<PoolParkPageVO[]>>({
            url: `${POOLPARK_BASE_URL}/page`,
            method: "get",
            params: queryParams,
        });
    },
    /**
     * 获取车位信息表单数据
     *
     * @param id PoolParkID
     * @returns PoolPark表单数据
     */
    getFormData(id: number) {
        return request<any, PoolParkForm>({
            url: `${POOLPARK_BASE_URL}/${id}`,
            method: "get",
        });
    },

    /** 添加车位信息*/
    add(data: PoolParkForm) {
        return request({
            url: `${POOLPARK_BASE_URL}`,
            method: "post",
            data: data,
        });
    },

    /**
     * 更新车位信息
     *
     * @param id PoolParkID
     * @param data PoolPark表单数据
     */
     update(data: PoolParkForm) {
        return request({
            url: `${POOLPARK_BASE_URL}`,
            method: "put",
            data: data,
        });
    },

    /**
     * 批量删除车位信息，多个以英文逗号(,)分割
     *
     * @param ids 车位信息ID字符串，多个以英文逗号(,)分割
     */
     deleteByIds(ids: string) {
        return request({
            url: `${POOLPARK_BASE_URL}/${ids}`,
            method: "delete",
        });
    },
    /** 同步车位信息
     * @param garageId 车库ID，可选参数
     */
    syncPoolPark(garageId?: number) {
      return request({
        url: `${POOLPARK_BASE_URL}/syncPoolPark`,
        method: "post",
        params: garageId ? { garageId } : undefined,
      });
    },

    /** 车位延期 */
    parkDelay(data: ParkDelayForm) {
      return request({
        url: `${POOLPARK_BASE_URL}/parkDelay`,
        method: "post",
        data: data,
      });
    },

    /** 批量添加车位 */
    batchAdd(data: BatchAddPoolParkForm) {
      return request({
        url: `${POOLPARK_BASE_URL}/batchAdd`,
        method: "post",
        data: data,
      });
    }
}

export default PoolParkAPI;

/** 车位信息分页查询参数 */
export interface PoolParkPageQuery extends PageQuery {
  /** 所属车位池 */
  poolName?: string;
  /** 车位名称、车位号 */
  parkName?: string;
  /** 绑定的车牌号 */
  plateNums?: string;
  /** 管理员姓名 */
  pname?: string;
  /** 管理员电话 */
  mobile?: string;
  /** 车库ID */
  garageId?: number;
  /** 状态 */
  status?: string;
}

/** 车位信息表单对象 */
export interface PoolParkForm {
    /** id */
    id?:  number;
    /** 车库ID */
    garageId?: number;
    /** 所属车位池 */
    poolName?:  string;
    /** 所属车库名称 */
    pgname?:  string;
    /** 车位名称、车位号 */
    parkName?:  string;
    /** 车位类型(0:公用车位, 1:私家车位) */
    parkType?:  number;
    /** 有效期开始时间（时间戳，单位:秒） */
    beginDate?:  number;
    /** 有效期结束时间（时间戳，单位:秒） */
    endDate?:  number;
    /** 创建时间（yyyy-MM-dd HH:mm:ss） */
    createDate?:  string;
    /** 最后修改时间（yyyy-MM-dd HH:mm:ss） */
    lastUpdateDate?:  string;
    /** 车位满缴费方式(1：先出收费，2：后进收费) */
    chargeType?:  number;
    /** 车位池满统计规则（1：按停车场统计 2：按车库统计） */
    fullMode?:  number;
    /** 总车位数 */
    totalNum?:  number;
    /** 有效车位数 */
    validNum?:  number;
    /** 在场车辆数 */
    parkedNum?:  number;
    /** 剩余车位数 */
    emptyNum?:  number;
    /** 绑定的车牌号 */
    plateNums?:  string;
    /** 所属楼栋单元房铺 */
    hourseAddr?:  string;
    /** 管理员姓名 */
    pname?:  string;
    /** 管理员电话 */
    mobile?:  string;
    /** 免费换车时间(单位分钟) */
    freeTime?:  number;
    /** 状态 */
  status?: string;
}

/** 车位延期表单对象 */
export interface ParkDelayForm {
    /** 所属车位池名称 */
    poolName: string;
    /** 车位名称、车位号 */
    parkName: string;
    /** 有效期开始时间（时间戳，单位:秒） */
    beginDate?: number;
    /** 有效期结束时间（时间戳，单位:秒） */
    endDate: number;
    /** 支付方式（详见支付方式说明） */
    payMode?: number;
    /** 收费金额（单位：元） */
    chargeMoney?: number;
    /** 车库ID */
    garageId?: number;
}

/** 批量添加车位表单对象 */
export interface BatchAddPoolParkForm {
    /** 车库ID */
    garageId: number;
    /** 车位池名称 */
    poolName: string;
    /** 车位号列表（逗号分隔或换行分隔） */
    parkNames: string;
    /** 状态：0 空闲 1 占用 2 待支付 */
    status?: string;
}

/** 车位信息分页对象 */
export interface PoolParkPageVO {
    /** id */
    id?: number;
    /** 车库ID */
    garageId?: number;
    /** 所属车位池 */
    poolName?: string;
    /** 所属车库名称 */
    pgname?: string;
    /** 车位名称、车位号 */
    parkName?: string;
    /** 车位类型(0:公用车位, 1:私家车位) */
    parkType?: number;
    /** 有效期开始时间（时间戳，单位:秒） */
    beginDate?: number;
    /** 有效期结束时间（时间戳，单位:秒） */
    endDate?: number;
    /** 创建时间（yyyy-MM-dd HH:mm:ss） */
    createDate?: string;
    /** 最后修改时间（yyyy-MM-dd HH:mm:ss） */
    lastUpdateDate?: string;
    /** 车位满缴费方式(1：先出收费，2：后进收费) */
    chargeType?: number;
    /** 车位池满统计规则（1：按停车场统计 2：按车库统计） */
    fullMode?: number;
    /** 总车位数 */
    totalNum?: number;
    /** 有效车位数 */
    validNum?: number;
    /** 在场车辆数 */
    parkedNum?: number;
    /** 剩余车位数 */
    emptyNum?: number;
    /** 绑定的车牌号 */
    plateNums?: string;
    /** 所属楼栋单元房铺 */
    hourseAddr?: string;
    /** 管理员姓名 */
    pname?: string;
    /** 管理员电话 */
    mobile?: string;
    /** 免费换车时间(单位分钟) */
    freeTime?: number;
    /** 状态 */
    status?: string;
}
