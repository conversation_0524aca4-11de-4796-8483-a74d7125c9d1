import request from "@/utils/request";

const CARINFO_BASE_URL = "/hmj/carInfo";

const CarInfoAPI = {
    /** 获取车辆信息分页数据 */
    getPage(queryParams?: CarInfoPageQuery) {
        return request<any, PageResult<CarInfoPageVO[]>>({
            url: `${CARINFO_BASE_URL}/page`,
            method: "get",
            params: queryParams,
        });
    },
    /**
     * 获取车辆信息表单数据
     *
     * @param id CarInfoID
     * @returns CarInfo表单数据
     */
    getFormData(id: number) {
        return request<any, CarInfoForm>({
            url: `${CARINFO_BASE_URL}/${id}`,
            method: "get",
        });
    },

    /** 添加车辆信息*/
    add(data: CarInfoForm) {
        return request({
            url: `${CARINFO_BASE_URL}`,
            method: "post",
            data: data,
        });
    },

    /**
     * 更新车辆信息
     *
     * @param id CarInfoID
     * @param data CarInfo表单数据
     */
     update(data: CarInfoForm) {
        return request({
            url: `${CARINFO_BASE_URL}`,
            method: "put",
            data: data,
        });
    },

    /**
     * 批量删除车辆信息，多个以英文逗号(,)分割
     *
     * @param ids 车辆信息ID字符串，多个以英文逗号(,)分割
     */
     deleteByIds(ids: string) {
        return request({
            url: `${CARINFO_BASE_URL}/${ids}`,
            method: "delete",
        });
    },
  /**
   * 同步车辆信息
   * @param garageId 车库ID，可选参数
   */
  syncCarInfo(garageId?: number) {
    return request({
      url: `${CARINFO_BASE_URL}/syncCarInfo`,
      method: "post",
      params: garageId ? { garageId } : undefined,
    });
  },

  /**
   * 根据车牌号同步车辆信息
   * @param data 同步车辆信息
   */
  syncByPlateNum(data: CarInfoForm) {
    return request<any, boolean>({
      url: `${CARINFO_BASE_URL}/syncByPlateNum`,
      method: "get",
      params: data,
    });
  }
}

export default CarInfoAPI;

/** 车辆信息分页查询参数 */
export interface CarInfoPageQuery extends PageQuery {
  /** 车牌号码 */
  plateNum?: string;
  /** 车位号 */
  parkNum?: string;
  /** 计费类型ID */
  cardTypeId?: number;
  /** 车主 */
  pname?: string;
  /** 手机号 */
  mobile?: string;
  /** 车库ID */
  garageId?: number;
}

/** 车辆信息表单对象 */
export interface CarInfoForm {
    id?:  number;
    /** 车牌号码 */
    plateNum?:  string;
    /** 车牌颜色(1 蓝色，2 黄色，3 白色，4 黑色, 5:绿色, 6:
黄绿色) */
    plateColor?:  number;
    /** 注册号/卡号 */
    cardNo?:  string;
    /** 车位号 */
    parkNum?:  string;
    /** 计费类型ID */
    cardTypeId?:  number;
    /** 车类型ID */
    carTypeId?:  number;
    /** 车辆分组 */
    gname?:  string;
    /** 车主 */
    pname?:  string;
    /** 手机号 */
    mobile?:  string;
    /** 地址 */
    addr?:  string;
    /** 有效期开始时间（yyyy-MM-dd） */
    beginDate?:  string;
    /** 有效期结束时间（yyyy-MM-dd） */
    endDate?:  string;
    /** 余额，只限于储值票车 */
    balance?:  number;
    /** 授权车库，多个以逗号隔开，例如：A车库,B车库 */
    authPgName?:  string;
    /** 最后入场时间(yyyy-MM-dd HH:mm:ss) */
    lastInDate?:  string;
    /** 最后出场时间(yyyy-MM-dd HH:mm:ss) */
    lastOutDate?:  string;
    /** 最后修改人 */
    realName?:  string;
    /** 备注 */
    remark?:  string;
    /** 状态(0:离场, 1:在场) */
    ioState?:  number;
    /** 创建时间（yyyy-MM-dd HH:mm:ss） */
    createDate?:  string;
    /** 最后修改时间（yyyy-MM-dd HH:mm:ss） */
    lastUpdateDate?:  string;
}

/** 车辆信息分页对象 */
export interface CarInfoPageVO {
    id?: number;
    /** 车牌号码 */
    plateNum?: string;
    /** 车牌颜色(1 蓝色，2 黄色，3 白色，4 黑色, 5:绿色, 6:
黄绿色) */
    plateColor?: number;
    /** 注册号/卡号 */
    cardNo?: string;
    /** 车位号 */
    parkNum?: string;
    /** 计费类型ID */
    cardTypeId?: number;
    /** 车类型ID */
    carTypeId?: number;
    /** 车辆分组 */
    gname?: string;
    /** 车主 */
    pname?: string;
    /** 手机号 */
    mobile?: string;
    /** 地址 */
    addr?: string;
    /** 有效期开始时间（yyyy-MM-dd） */
    beginDate?: string;
    /** 有效期结束时间（yyyy-MM-dd） */
    endDate?: string;
    /** 余额，只限于储值票车 */
    balance?: number;
    /** 授权车库，多个以逗号隔开，例如：A车库,B车库 */
    authPgName?: string;
    /** 最后入场时间(yyyy-MM-dd HH:mm:ss) */
    lastInDate?: string;
    /** 最后出场时间(yyyy-MM-dd HH:mm:ss) */
    lastOutDate?: string;
    /** 最后修改人 */
    realName?: string;
    /** 备注 */
    remark?: string;
    /** 状态(0:离场, 1:在场) */
    ioState?: number;
    /** 创建时间（yyyy-MM-dd HH:mm:ss） */
    createDate?: string;
    /** 最后修改时间（yyyy-MM-dd HH:mm:ss） */
    lastUpdateDate?: string;
}
