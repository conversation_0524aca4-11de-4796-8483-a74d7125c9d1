import request from "@/utils/request";

const RECHARGERECORD_BASE_URL = "/hmj/rechargeRecord";

const RechargeRecordAPI = {
    /** 获取充值记录分页数据 */
    getPage(queryParams?: RechargeRecordPageQuery) {
        return request<any, PageResult<RechargeRecordPageVO[]>>({
            url: `${RECHARGERECORD_BASE_URL}/page`,
            method: "get",
            params: queryParams,
        });
    },
    /**
     * 获取充值记录表单数据
     *
     * @param id RechargeRecordID
     * @returns RechargeRecord表单数据
     */
    getFormData(id: number) {
        return request<any, RechargeRecordForm>({
            url: `${RECHARGERECORD_BASE_URL}/${id}`,
            method: "get",
        });
    },

    /** 添加充值记录*/
    add(data: RechargeRecordForm) {
        return request({
            url: `${RECHARGERECORD_BASE_URL}`,
            method: "post",
            data: data,
        });
    },

    /**
     * 更新充值记录
     *
     * @param id RechargeRecordID
     * @param data RechargeRecord表单数据
     */
     update(data: RechargeRecordForm) {
        return request({
            url: `${RECHARGERECORD_BASE_URL}`,
            method: "put",
            data: data,
        });
    },

    /**
     * 批量删除充值记录，多个以英文逗号(,)分割
     *
     * @param ids 充值记录ID字符串，多个以英文逗号(,)分割
     */
     deleteByIds(ids: string) {
        return request({
            url: `${RECHARGERECORD_BASE_URL}/${ids}`,
            method: "delete",
        });
    }
}

export default RechargeRecordAPI;

/** 充值记录分页查询参数 */
export interface RechargeRecordPageQuery extends PageQuery {
  /** 用户ID */
  userId?: number;
  /** 车主 */
  pname?: string;
  /** openid */
  openid?: string;
  /** 手机号 */
  phone?: string;
  /** 车位号 */
  poolId?: string;
  /** 车库名称 */
  garageName?: string;
  /** 二级商户号 */
  subMchid?: string;
  /** 充值流水号 */
  rechargeSn?: string;
  /** 充值金额 */
  rechargeAmount?: number;
  /** 充值类型 */
  rechargeType?: string;
  /** 微信系统中的交易流水号 */
  tradeNo?: string;
  /** 支付状态 1.待支付 2.已支付 3.已取消 */
  payStatus?: string;
  /** 支付完成时间 yyyyMMddHHmmss */
  timeEnd?: string;

}

/** 充值记录表单对象 */
export interface RechargeRecordForm {
    id?:  number;
    /** 用户ID */
    userId?:  number;
    /** 车主 */
    pname?:  string;
    /** openid */
    openid?:  string;
    /** 手机号 */
    phone?:  string;
    /** 车位号 */
    parkNum?:  string;
    /** 车库名称 */
    garageName?:  string;
    /** 二级商户号 */
    subMchid?:  string;
    /** 充值流水号 */
    rechargeSn?:  string;
    /** 充值金额 */
    rechargeAmount?:  number;
    /** 充值类型 */
    rechargeType?:  string;
    /** 微信系统中的交易流水号 */
    tradeNo?:  string;
    /** 支付状态 1.待支付 2.已支付 3.已取消 */
    payStatus?:  string;
    /** 支付完成时间 yyyyMMddHHmmss */
    timeEnd?:  string;
    /** 备注 */
    remark?:  string;
    /** 创建时间 */
    createTime?:  Date;
    /** 修改时间 */
    modifyTime?:  Date;
    /** 有效状态：0->无效；1->有效 */
    valid?:  number;
}

/** 充值记录分页对象 */
export interface RechargeRecordPageVO {
    id?: number;
    /** 用户ID */
    userId?: number;
    /** 车主 */
    pname?: string;
    /** openid */
    openid?: string;
    /** 手机号 */
    phone?: string;
    /** 车位号 */
    poolId?: string;
    /** 车库名称 */
    garageName?: string;
    /** 二级商户号 */
    subMchid?: string;
    /** 充值流水号 */
    rechargeSn?: string;
    /** 充值金额 */
    rechargeAmount?: number;
    /** 充值类型 */
    rechargeType?: string;
    /** 微信系统中的交易流水号 */
    tradeNo?: string;
    /** 支付状态 1.待支付 2.已支付 3.已取消 */
    payStatus?: string;
    /** 支付完成时间 yyyyMMddHHmmss */
    timeEnd?: string;
    /** 备注 */
    remark?: string;
    /** 创建时间 */
    createTime?: Date;
    /** 修改时间 */
    modifyTime?: Date;
    /** 有效状态：0->无效；1->有效 */
    valid?: number;
}
