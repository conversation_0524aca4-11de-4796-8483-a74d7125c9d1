<template>
  <div class="app-container">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="模块标题" prop="title">
          <el-input
            v-model="queryParams.title"
            placeholder="模块标题"
            clearable
            @keyup.enter="handleQuery()"
          />
        </el-form-item>
        <el-form-item label="方法名称" prop="method">
          <el-input
            v-model="queryParams.method"
            placeholder="方法名称"
            clearable
            @keyup.enter="handleQuery()"
          />
        </el-form-item>
        <el-form-item label="操作人员" prop="username">
          <el-input
            v-model="queryParams.username"
            placeholder="操作人员"
            clearable
            @keyup.enter="handleQuery()"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <template #icon>
              <Search/>
            </template>
            搜索
          </el-button>
          <el-button @click="handleResetQuery">
            <template #icon>
              <Refresh/>
            </template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
        <el-button
          v-hasPerm="['sys_log_del']"
          type="danger"
          :disabled="removeIds.length === 0"
          @click="handleDelete()"
        >
          <template #icon>
            <Delete/>
          </template>
          删除
        </el-button>
      </template>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column
          key="id"
          label="主键"
          prop="id"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="title"
          label="模块标题"
          prop="title"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="method"
          label="方法名称"
          prop="method"
          min-width="200"
          align="center"
        />
        <el-table-column
          key="requestMethod"
          label="请求方式"
          prop="requestMethod"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="username"
          label="操作人员"
          prop="username"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="url"
          label="请求URL"
          prop="url"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="ip"
          label="主机地址"
          prop="ip"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="location"
          label="操作地点"
          prop="location"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="status"
          label="操作状态"
          prop="status"
          min-width="150"
          align="center"
        >
          <template #default="scope">
            <DictLabel
              code="sys_common_status"
              v-model="scope.row.status"
              size="small"/>
          </template>
        </el-table-column>

        <el-table-column
          key="browser"
          label="浏览器"
          prop="browser"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="browserVersion"
          label="浏览器版本"
          prop="browserVersion"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="os"
          label="终端系统"
          prop="os"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="createTime"
          label="创建时间"
          prop="createTime"
          min-width="150"
          align="center"
        />
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button
              v-hasPerm="['sys_log_edit']"
              type="primary"
              size="small"
              link
              @click="handleOpenDialog(scope.row.id)"
            >
              <template #icon>
                <View/>
              </template>
              查看详情
            </el-button>
            <el-button
              v-hasPerm="['sys_log_del']"
              type="danger"
              size="small"
              link
              @click="handleDelete(scope.row.id)"
            >
              <template #icon>
                <Delete/>
              </template>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.current"
        v-model:limit="queryParams.size"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 系统日志表单弹窗 -->
    <el-dialog
      v-model="dialog.visible"
      :title="dialog.title"
      width="800px"
      @close="handleCloseDialog"
    >
      <el-form ref="dataFormRef" :model="formData" label-width="100px">
        <el-form-item label="主键" prop="id">
          {{ formData.id }}
        </el-form-item>
        <el-form-item label="模块标题" prop="title">
          {{ formData.title }}
        </el-form-item>
        <el-form-item label="方法名称" prop="method">
          {{ formData.method }}
        </el-form-item>
        <el-form-item label="请求方式" prop="requestMethod">
          {{ formData.requestMethod }}
        </el-form-item>

        <el-form-item label="操作人员" prop="username">
          {{ formData.username }}
        </el-form-item>
        <el-form-item label="请求URL" prop="url">
          {{ formData.url }}
        </el-form-item>
        <el-form-item label="主机地址" prop="ip">
          {{ formData.ip }}
        </el-form-item>
        <el-form-item label="操作地点" prop="location">
          {{ formData.location }}
        </el-form-item>
        <el-form-item label="请求参数" prop="param">
          <el-input type="textarea" v-model="formData.param" :rows="5"/>
        </el-form-item>
        <el-form-item label="返回参数" prop="jsonResult">
          <el-input type="textarea" v-model="formData.jsonResult" :rows="5"/>
        </el-form-item>
        <el-form-item label="操作状态" prop="status">
          <DictLabel
            code="sys_common_status"
            v-model="formData.status"
            size="small"/>
        </el-form-item>
        <el-form-item label="错误消息" prop="errorMsg">
          {{ formData.errorMsg }}
        </el-form-item>
        <el-form-item label="执行时间" prop="executionTime">
          {{ formData.executionTime }} ms
        </el-form-item>
        <el-form-item label="浏览器" prop="browser">
          {{ formData.browser }}
        </el-form-item>
        <el-form-item label="浏览器版本" prop="browserVersion">
          {{ formData.browserVersion }}
        </el-form-item>
        <el-form-item label="终端系统" prop="os">
          {{ formData.os }}
        </el-form-item>
        <el-form-item label="创建时间" prop="os">
          {{ formData.createTime }}
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseDialog()">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "SysLog",
  inheritAttrs: false,
});

import SysLogAPI, {SysLogPageVO, SysLogForm, SysLogPageQuery} from "@/api/monitor/log";

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);

const loading = ref(false);
const removeIds = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<SysLogPageQuery>({
  current: 1,
  size: 10,
});

// 系统日志表格数据
const pageData = ref<SysLogPageVO[]>([]);

// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

// 系统日志表单数据
const formData = reactive<SysLogForm>({});


/** 查询系统日志 */
function handleQuery() {
  loading.value = true;
  SysLogAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.records;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置系统日志查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.current = 1;
  handleQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  removeIds.value = selection.map((item: any) => item.id);
}

/** 打开系统日志弹窗 */
function handleOpenDialog(id?: number) {
  dialog.visible = true;
  if (id) {
    dialog.title = "查看系统日志";
    SysLogAPI.getFormData(id).then((data) => {
      Object.assign(formData, data);
    });
  }
}

/** 提交系统日志表单 */
function handleSubmit() {
  dataFormRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true;
      const id = formData.id;
      if (id) {
        SysLogAPI.update(formData)
          .then(() => {
            ElMessage.success("修改成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      } else {
        SysLogAPI.add(formData)
          .then(() => {
            ElMessage.success("新增成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}

/** 关闭系统日志弹窗 */
function handleCloseDialog() {
  dialog.visible = false;
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  formData.id = undefined;
}

/** 删除系统日志 */
function handleDelete(id?: number) {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      SysLogAPI.deleteByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

onMounted(() => {
  handleQuery();
});
</script>
