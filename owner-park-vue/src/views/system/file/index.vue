<template>
  <div class="app-container">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="文件名" prop="fileName">
          <el-input
            v-model="queryParams.fileName"
            placeholder="文件名"
            clearable
            @keyup.enter="handleQuery()"
          />
        </el-form-item>
        <el-form-item label="文件存储桶名称" prop="bucketName">
          <el-input
            v-model="queryParams.bucketName"
            placeholder="文件存储桶名称"
            clearable
            @keyup.enter="handleQuery()"
          />
        </el-form-item>
        <el-form-item label="桶类型" prop="bucketType">
          <dict v-model="queryParams.bucketType" code="sys_bucket_type" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <template #icon><Search /></template>
            搜索
          </el-button>
          <el-button @click="handleResetQuery">
            <template #icon><Refresh /></template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
        <el-button
          v-hasPerm="['sys_file_delete']"
          type="danger"
          :disabled="removeIds.length === 0"
          @click="handleDelete()"
        >
          <template #icon><Delete /></template>
          删除
        </el-button>
      </template>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          key="id"
          label="主键ID"
          prop="id"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="fileName"
          label="文件名"
          prop="fileName"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="original"
          label="原始文件名"
          prop="original"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="bucketName"
          label="文件存储桶名称"
          prop="bucketName"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="bucketType"
          label="桶类型"
          prop="bucketType"
          min-width="150"
          align="center"
        >
          <template #default="scope">
            <DictLabel v-model="scope.row.bucketType" code="sys_bucket_type"/>
          </template>
        </el-table-column>
        <el-table-column
          key="type"
          label="文件类型"
          prop="type"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="fileSize"
          label="文件大小"
          prop="fileSize"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="domain"
          label="访问地址"
          prop="domain"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="createTime"
          label="创建时间"
          prop="createTime"
          min-width="150"
          align="center"
        />
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button
              v-hasPerm="['sys_file_del']"
              type="danger"
              size="small"
              link
              @click="handleDelete(scope.row.id)"
            >
              <template #icon><Delete /></template>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.current"
        v-model:limit="queryParams.size"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 文件管理表单弹窗 -->
    <el-dialog
      v-model="dialog.visible"
      :title="dialog.title"
      width="500px"
      @close="handleCloseDialog"
    >
      <el-form ref="dataFormRef" :model="formData" :rules="rules" label-width="100px">
        <el-form-item label="文件名" prop="fileName">
          <el-input
            v-model="formData.fileName"
            placeholder="文件名"
          />
        </el-form-item>
        <el-form-item label="原始文件名" prop="original">
          <el-input
            v-model="formData.original"
            placeholder="原始文件名"
          />
        </el-form-item>
        <el-form-item label="文件存储桶名称" prop="bucketName">
          <el-input
            v-model="formData.bucketName"
            placeholder="文件存储桶名称"
          />
        </el-form-item>
        <el-form-item label="桶类型" prop="bucketType">
          <dict v-model="formData.bucketType" code="sys_bucket_type" />
        </el-form-item>
        <el-form-item label="文件类型" prop="type">
          <el-input
            v-model="formData.type"
            placeholder="文件类型"
          />
        </el-form-item>
        <el-form-item label="文件大小" prop="fileSize">
          <el-input
            v-model="formData.fileSize"
            placeholder="文件大小"
          />
        </el-form-item>
        <el-form-item label="访问地址" prop="domain">
          <el-input
            v-model="formData.domain"
            placeholder="访问地址"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit()">确定</el-button>
          <el-button @click="handleCloseDialog()">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "File",
  inheritAttrs: false,
});

import FileAPI, { FilePageVO, FileForm, FilePageQuery } from "@/api/system/file";

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);

const loading = ref(false);
const removeIds = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<FilePageQuery>({
  current: 1,
  size: 10,
});

// 文件管理表格数据
const pageData = ref<FilePageVO[]>([]);

// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

// 文件管理表单数据
const formData = reactive<FileForm>({});

// 文件管理表单校验规则
const rules = reactive({
  fileName: [{ required: true, message: "请输入文件名", trigger: "blur" }],
  original: [{ required: true, message: "请输入原始文件名", trigger: "blur" }],
  bucketName: [{ required: true, message: "请输入文件存储桶名称", trigger: "blur" }],
  bucketType: [{ required: true, message: "请输入桶类型", trigger: "blur" }],
  type: [{ required: true, message: "请输入文件类型", trigger: "blur" }],
  fileSize: [{ required: true, message: "请输入文件大小", trigger: "blur" }],
  domain: [{ required: true, message: "请输入访问地址", trigger: "blur" }],
});

/** 查询文件管理 */
function handleQuery() {
  loading.value = true;
  FileAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.records;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置文件管理查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.current = 1;
  handleQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  removeIds.value = selection.map((item: any) => item.id);
}

/** 打开文件管理弹窗 */
function handleOpenDialog(id?: number) {
  dialog.visible = true;
  if (id) {
    dialog.title = "修改文件管理";
    FileAPI.getFormData(id).then((data) => {
      Object.assign(formData, data);
    });
  } else {
    dialog.title = "新增文件管理";
  }
}

/** 提交文件管理表单 */
function handleSubmit() {
  dataFormRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true;
      const id = formData.id;
      if (id) {
        FileAPI.update(formData)
          .then(() => {
            ElMessage.success("修改成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      } else {
        FileAPI.add(formData)
          .then(() => {
            ElMessage.success("新增成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}

/** 关闭文件管理弹窗 */
function handleCloseDialog() {
  dialog.visible = false;
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  formData.id = undefined;
}

/** 删除文件管理 */
function handleDelete(id?: number) {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      FileAPI.deleteByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

onMounted(() => {
  handleQuery();
});
</script>
