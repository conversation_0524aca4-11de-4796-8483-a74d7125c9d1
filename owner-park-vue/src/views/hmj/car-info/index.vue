<template>
  <div class="app-container">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="车库" prop="garageId" required>
          <el-select
            v-model="queryParams.garageId"
            placeholder="请选择车库"
            style="width: 200px"
          >
            <el-option
              v-for="garage in garageList"
              :key="garage.id"
              :label="garage.pname"
              :value="garage.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="车牌号码" prop="plateNum">
          <el-input
            v-model="queryParams.plateNum"
            placeholder="车牌号码"
            clearable
            @keyup.enter="handleQuery()"
          />
        </el-form-item>
        <el-form-item label="车位号" prop="parkNum">
          <el-input
            v-model="queryParams.parkNum"
            placeholder="车位号"
            clearable
            @keyup.enter="handleQuery()"
          />
        </el-form-item>

        <el-form-item label="计费类型" prop="cardTypeId">
          <dict v-model="queryParams.cardTypeId" code="card_type" />
        </el-form-item>
        <el-form-item label="车主" prop="pname">
          <el-input
            v-model="queryParams.pname"
            placeholder="车主"
            clearable
            @keyup.enter="handleQuery()"
          />
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input
            v-model="queryParams.mobile"
            placeholder="手机号"
            clearable
            @keyup.enter="handleQuery()"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <template #icon>
              <Search />
            </template>
            搜索
          </el-button>
          <el-button @click="handleResetQuery">
            <template #icon>
              <Refresh />
            </template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>

        <el-button
          type="primary"
          @click="syncCarInfo()"
          :loading="syncLoading"
          :disabled="syncLoading"
        >
          <template #icon>
            <Refresh />
          </template>
          同步
        </el-button>
        <el-button
          v-hasPerm="['t_car_info_delete']"
          type="danger"
          :disabled="removeIds.length === 0"
          @click="handleDelete()"
        >
          <template #icon>
            <Delete />
          </template>
          删除
        </el-button>
      </template>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
          :index="
            (index) => (queryParams.current - 1) * queryParams.size + index + 1
          "
        />

        <el-table-column
          key="plateNum"
          label="车牌号码"
          prop="plateNum"
          min-width="150"
          align="center"
        >
          <template #default="{ row }">
            <div
              :class="[
                'px-2 py-1 rounded border-2 font-bold',
                {
                  'bg-blue-600 border-blue-800 text-white':
                    row.plateColor === 1,
                  'bg-yellow-500 border-yellow-700 text-black':
                    row.plateColor === 2,
                  'bg-white border-gray-300 text-black': row.plateColor === 3,
                  'bg-gray-900 border-black text-white': row.plateColor === 4,
                  'bg-green-600 border-green-800 text-black':
                    row.plateColor === 5,
                },
              ]"
            >
              {{ row.plateNum }}
            </div>
          </template>
        </el-table-column>
        key="plateColor" label="车牌颜色" prop="plateColor" min-width="150"
        align="center" />
        <el-table-column
          key="cardNo"
          label="注册号/卡号"
          prop="cardNo"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="parkNum"
          label="车位号/车位池"
          prop="parkNum"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="cardTypeId"
          label="计费类型"
          prop="cardTypeId"
          min-width="150"
          align="center"
        >
          <template #default="scope">
            <DictLabel v-model="scope.row.cardTypeId" code="card_type" />
          </template>
        </el-table-column>

        <el-table-column
          key="gname"
          label="车辆分组"
          prop="gname"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="pname"
          label="车主"
          prop="pname"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="mobile"
          label="手机号"
          prop="mobile"
          min-width="150"
          align="center"
        />

        <el-table-column
          key="beginDate"
          label="有效期"
          min-width="200"
          align="center"
        >
          <template #default="{ row }">
            <el-text v-if=" row.expireStatus === 1" type="warning">  {{ row.beginDate }} ~ {{ row.endDate }}</el-text>
            <el-text v-else-if=" row.expireStatus === 2" type="danger">  {{ row.beginDate }} ~ {{ row.endDate }}</el-text>
            <el-text v-else>  {{ row.beginDate }} ~ {{ row.endDate }}</el-text>
          </template>
        </el-table-column>

        <el-table-column
          key="poolParkBeginDate"
          label="有效期(车位池)"
          min-width="200"
          align="center"
        >
          <template #default="{ row }">
            <el-text v-if=" row.poolParkExpireStatus === 1" type="warning">  {{ row.poolParkBeginDate }} ~ {{ row.poolParkEndDate }}</el-text>
            <el-text v-else-if=" row.poolParkExpireStatus === 2" type="danger">  {{ row.poolParkBeginDate }} ~ {{ row.poolParkEndDate }}</el-text>
            <el-text v-else>  {{ row.poolParkBeginDate }} ~ {{ row.poolParkEndDate }}</el-text>
          </template>
        </el-table-column>

        <el-table-column
          key="authPgName"
          label="授权车库"
          prop="authPgName"
          min-width="150"
          align="center"
        />
        <el-table-column fixed="right" label="操作" width="300">
          <template #default="scope">
            <el-button
              v-hasPerm="['t_car_info_sync']"
              type="primary"
              size="small"
              link
              @click="handleSyncByPlateNum(scope.row)"
              :loading="syncPlateNumLoading && currentPlateNum === scope.row.plateNum"
              :disabled="syncPlateNumLoading && currentPlateNum === scope.row.plateNum"
            >
              <template #icon>
                <Refresh />
              </template>
              同步
            </el-button>
            <el-button
              v-hasPerm="['t_car_info_del']"
              type="danger"
              size="small"
              link
              @click="handleDelete(scope.row.id)"
            >
              <template #icon>
                <Delete />
              </template>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.current"
        v-model:limit="queryParams.size"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 车辆信息表单弹窗 -->
    <el-dialog
      v-model="dialog.visible"
      :title="dialog.title"
      width="500px"
      @close="handleCloseDialog"
    >
      <el-form
        ref="dataFormRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="" prop="id">
          <el-input v-model="formData.id" placeholder="" />
        </el-form-item>
        <el-form-item label="车牌号码" prop="plateNum">
          <el-input v-model="formData.plateNum" placeholder="车牌号码" />
        </el-form-item>
        <el-form-item label="车牌颜色" prop="plateColor">
          <el-input v-model="formData.plateColor" placeholder="车牌颜色" />
        </el-form-item>
        <el-form-item label="注册号/卡号" prop="cardNo">
          <el-input v-model="formData.cardNo" placeholder="注册号/卡号" />
        </el-form-item>
        <el-form-item label="车位号" prop="parkNum">
          <el-input v-model="formData.parkNum" placeholder="车位号" />
        </el-form-item>
        <el-form-item label="计费类型ID" prop="cardTypeId">
          <el-input v-model="formData.cardTypeId" placeholder="计费类型ID" />
        </el-form-item>
        <el-form-item label="车类型ID" prop="carTypeId">
          <el-input v-model="formData.carTypeId" placeholder="车类型ID" />
        </el-form-item>
        <el-form-item label="车辆分组" prop="gname">
          <el-input v-model="formData.gname" placeholder="车辆分组" />
        </el-form-item>
        <el-form-item label="车主" prop="pname">
          <el-input v-model="formData.pname" placeholder="车主" />
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="formData.mobile" placeholder="手机号" />
        </el-form-item>
        <el-form-item label="地址" prop="addr">
          <el-input v-model="formData.addr" placeholder="地址" />
        </el-form-item>
        <el-form-item label="有效期开始时间（yyyy-MM-dd）" prop="beginDate">
          <el-input
            v-model="formData.beginDate"
            placeholder="有效期开始时间（yyyy-MM-dd）"
          />
        </el-form-item>
        <el-form-item label="有效期结束时间（yyyy-MM-dd）" prop="endDate">
          <el-input
            v-model="formData.endDate"
            placeholder="有效期结束时间（yyyy-MM-dd）"
          />
        </el-form-item>
        <el-form-item label="余额，只限于储值票车" prop="balance">
          <el-input
            v-model="formData.balance"
            placeholder="余额，只限于储值票车"
          />
        </el-form-item>
        <el-form-item
          label="授权车库，多个以逗号隔开，例如：A车库,B车库"
          prop="authPgName"
        >
          <el-input
            v-model="formData.authPgName"
            placeholder="授权车库，多个以逗号隔开，例如：A车库,B车库"
          />
        </el-form-item>
        <el-form-item
          label="最后入场时间(yyyy-MM-dd HH:mm:ss)"
          prop="lastInDate"
        >
          <el-input
            v-model="formData.lastInDate"
            placeholder="最后入场时间(yyyy-MM-dd HH:mm:ss)"
          />
        </el-form-item>
        <el-form-item
          label="最后出场时间(yyyy-MM-dd HH:mm:ss)"
          prop="lastOutDate"
        >
          <el-input
            v-model="formData.lastOutDate"
            placeholder="最后出场时间(yyyy-MM-dd HH:mm:ss)"
          />
        </el-form-item>
        <el-form-item label="最后修改人" prop="realName">
          <el-input v-model="formData.realName" placeholder="最后修改人" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" placeholder="备注" />
        </el-form-item>
        <el-form-item label="状态(0:离场, 1:在场)" prop="ioState">
          <el-input
            v-model="formData.ioState"
            placeholder="状态(0:离场, 1:在场)"
          />
        </el-form-item>
        <el-form-item label="创建时间（yyyy-MM-dd HH:mm:ss）" prop="createDate">
          <el-input
            v-model="formData.createDate"
            placeholder="创建时间（yyyy-MM-dd HH:mm:ss）"
          />
        </el-form-item>
        <el-form-item
          label="最后修改时间（yyyy-MM-dd HH:mm:ss）"
          prop="lastUpdateDate"
        >
          <el-input
            v-model="formData.lastUpdateDate"
            placeholder="最后修改时间（yyyy-MM-dd HH:mm:ss）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit()">确定</el-button>
          <el-button @click="handleCloseDialog()">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { Refresh } from '@element-plus/icons-vue'

defineOptions({
  name: "CarInfo",
  inheritAttrs: false,
});

import CarInfoAPI, {
  CarInfoPageVO,
  CarInfoForm,
  CarInfoPageQuery,
} from "@/api/hmj/car-info";
import ParkingLotAPI, { GarageOption } from "@/api/hmj/parking-lot";

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);

const loading = ref(false);
const syncLoading = ref(false);
const syncPlateNumLoading = ref(false);
const currentPlateNum = ref("");
const removeIds = ref<number[]>([]);
const total = ref(0);

// 车库列表
const garageList = ref<GarageOption[]>([]);

const queryParams = reactive<CarInfoPageQuery>({
  current: 1,
  size: 10,
});

// 车辆信息表格数据
const pageData = ref<CarInfoPageVO[]>([]);

// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

// 车辆信息表单数据
const formData = reactive<CarInfoForm>({});

// 车辆信息表单校验规则
const rules = reactive({
  plateNum: [{ required: true, message: "请输入车牌号码", trigger: "blur" }],
  plateColor: [{ required: true, message: "请输入车牌颜色", trigger: "blur" }],
  cardNo: [{ required: true, message: "请输入注册号/卡号", trigger: "blur" }],
  parkNum: [{ required: true, message: "请输入车位号", trigger: "blur" }],
  cardTypeId: [
    { required: true, message: "请输入计费类型ID", trigger: "blur" },
  ],
  carTypeId: [{ required: true, message: "请输入车类型ID", trigger: "blur" }],
  gname: [{ required: true, message: "请输入车辆分组", trigger: "blur" }],
  pname: [{ required: true, message: "请输入车主", trigger: "blur" }],
  mobile: [{ required: true, message: "请输入手机号", trigger: "blur" }],
  addr: [{ required: true, message: "请输入地址", trigger: "blur" }],
  beginDate: [
    {
      required: true,
      message: "请输入有效期开始时间（yyyy-MM-dd）",
      trigger: "blur",
    },
  ],
  endDate: [
    {
      required: true,
      message: "请输入有效期结束时间（yyyy-MM-dd）",
      trigger: "blur",
    },
  ],
  balance: [
    { required: true, message: "请输入余额，只限于储值票车", trigger: "blur" },
  ],
  authPgName: [
    {
      required: true,
      message: "请输入授权车库，多个以逗号隔开，例如：A车库,B车库",
      trigger: "blur",
    },
  ],
  lastInDate: [
    {
      required: true,
      message: "请输入最后入场时间(yyyy-MM-dd HH:mm:ss)",
      trigger: "blur",
    },
  ],
  lastOutDate: [
    {
      required: true,
      message: "请输入最后出场时间(yyyy-MM-dd HH:mm:ss)",
      trigger: "blur",
    },
  ],
  realName: [{ required: true, message: "请输入最后修改人", trigger: "blur" }],
  remark: [{ required: true, message: "请输入备注", trigger: "blur" }],
  ioState: [
    { required: true, message: "请输入状态(0:离场, 1:在场)", trigger: "blur" },
  ],
  createDate: [
    {
      required: true,
      message: "请输入创建时间（yyyy-MM-dd HH:mm:ss）",
      trigger: "blur",
    },
  ],
  lastUpdateDate: [
    {
      required: true,
      message: "请输入最后修改时间（yyyy-MM-dd HH:mm:ss）",
      trigger: "blur",
    },
  ],
});

/** 获取车库列表（仅加载列表，不查询数据） */
function loadGarageList() {
  ParkingLotAPI.getGarageList()
    .then((data) => {
      garageList.value = data || [];
      // 默认选择第一个车库
      if (garageList.value.length > 0 && !queryParams.garageId) {
        queryParams.garageId = garageList.value[0].id;
      }
    })
    .catch((error) => {
      console.error('获取车库列表失败:', error);
      ElMessage.error('获取车库列表失败');
    });
}

/** 查询车辆信息 */
function handleQuery() {
  // 检查是否选择了车库
  if (!queryParams.garageId) {
    ElMessage.warning('请先选择车库');
    return;
  }

  loading.value = true;
  CarInfoAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.records;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置车辆信息查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.current = 1;

  // 重置后默认选择第一个车库
  if (garageList.value.length > 0) {
    queryParams.garageId = garageList.value[0].id;
  }

  handleQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  removeIds.value = selection.map((item: any) => item.id);
}

/** 打开车辆信息弹窗 */
function handleOpenDialog(id?: number) {
  dialog.visible = true;
  if (id) {
    dialog.title = "修改车辆信息";
    CarInfoAPI.getFormData(id).then((data) => {
      Object.assign(formData, data);
    });
  } else {
    dialog.title = "新增车辆信息";
  }
}

/** 提交车辆信息表单 */
function handleSubmit() {
  dataFormRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true;
      const id = formData.id;
      if (id) {
        CarInfoAPI.update(formData)
          .then(() => {
            ElMessage.success("修改成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      } else {
        CarInfoAPI.add(formData)
          .then(() => {
            ElMessage.success("新增成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}

/** 关闭车辆信息弹窗 */
function handleCloseDialog() {
  dialog.visible = false;
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  formData.id = undefined;
}

/** 删除车辆信息 */
function handleDelete(id?: number) {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      CarInfoAPI.deleteByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

function syncCarInfo(){
  // 检查是否选择了车库
  if (!queryParams.garageId) {
    ElMessage.warning("请先选择车库");
    return;
  }

  syncLoading.value = true;
  CarInfoAPI.syncCarInfo(queryParams.garageId)
  .then(() => {
    ElMessage.success("同步成功");
    handleResetQuery();
  })
  .catch((error) => {
    ElMessage.error(error.message || "同步失败");
  })
  .finally(() => {
    syncLoading.value = false;
  });
}

/**
 * 根据车牌号同步车辆信息
 * @param row  车辆信息
 */
function handleSyncByPlateNum(row: CarInfoForm) {
  if (!row.plateNum) {
    ElMessage.warning("车牌号不能为空");
    return;
  }

  syncPlateNumLoading.value = true;
  currentPlateNum.value = row.plateNum;

  CarInfoAPI.syncByPlateNum(row)
    .then((result) => {
      if (result) {
        ElMessage.success(`车牌号 ${row.plateNum} 同步成功`);
        handleResetQuery();
      } else {
        ElMessage.warning(`车牌号 ${row.plateNum} 同步失败`);
      }
    })
    .catch((error) => {
      ElMessage.error(error.message || `车牌号 ${row.plateNum} 同步失败`);
    })
    .finally(() => {
      syncPlateNumLoading.value = false;
      currentPlateNum.value = "";
    });
}

onMounted(() => {
  // 先加载车库列表，然后在回调中进行查询
  loadGarageListAndQuery();
});

/** 加载车库列表并查询 */
function loadGarageListAndQuery() {
  ParkingLotAPI.getGarageList()
    .then((data) => {
      garageList.value = data || [];
      // 默认选择第一个车库
      if (garageList.value.length > 0) {
        queryParams.garageId = garageList.value[0].id;
        // 选择车库后进行查询
        handleQuery();
      } else {
        ElMessage.warning('暂无可用车库');
      }
    })
    .catch((error) => {
      console.error('获取车库列表失败:', error);
      ElMessage.error('获取车库列表失败');
    });
}
</script>
