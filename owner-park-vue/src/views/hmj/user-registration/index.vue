<template>
  <div class="app-container">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="车库" prop="garageId" required>
          <el-select
            v-model="queryParams.garageId"
            placeholder="请选择车库"
            style="width: 200px"
          >
            <el-option
              v-for="garage in garageList"
              :key="garage.id"
              :label="garage.pname"
              :value="garage.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <template #icon><Search /></template>
            搜索
          </el-button>
          <el-button @click="handleResetQuery">
            <template #icon><Refresh /></template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">

      <el-table
          ref="dataTableRef"
          v-loading="loading"
          :data="pageData"
          highlight-current-row
          border
          @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />

                <el-table-column
                    key="pname"
                    label="车主"
                    prop="pname"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="mobile"
                    label="手机号"
                    prop="mobile"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="addr"
                    label="地址"
                    prop="addr"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="ownerCertificate"
                    label="业主凭证"
                    prop="ownerCertificate"
                    min-width="200"
                    align="center"
                >
                  <template #default="{ row }">
                    <div v-if="row.ownerCertificate" class="image-preview-container">
                      <el-image
                        v-for="(imageUrl, index) in parseImageUrls(row.ownerCertificate)"
                        :key="index"
                        :src="imageUrl"
                        :preview-src-list="parseImageUrls(row.ownerCertificate)"
                        :initial-index="index"
                        fit="cover"
                        class="preview-image"
                        :preview-teleported="true"
                      />
                    </div>
                    <span v-else class="text-gray-400">暂无图片</span>
                  </template>
                </el-table-column>
                <el-table-column
                    key="plateNum"
                    label="车牌号码"
                    prop="plateNum"
                    min-width="150"
                    align="center"
                />

                <el-table-column
                    key="parkNum"
                    label="车位号"
                    prop="parkNum"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="billingType"
                    label="计费类型"
                    prop="billingType"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="beginDate"
                    label="开始时间"
                    prop="beginDate"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="endDate"
                    label="结束时间"
                    prop="endDate"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="shouldPay"
                    label="应缴费用"
                    prop="shouldPay"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="userId"
                    label="用户ID"
                    prop="userId"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="openid"
                    label="openid"
                    prop="openid"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="status"
                    label="状态"
                    prop="status"
                    min-width="150"
                    align="center"
                >
                  <template #default="scope">
                    <DictLabel
                      code="pay_status"
                      v-model="scope.row.status"
                      size="small"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  key="isRenewal"
                  label="是否续费"
                  prop="isRenewal"
                  min-width="150"
                  align="center"
                >
                  <template #default="scope">
                    <el-tag :type="scope.row.isRenewal === 1 ? 'success' : 'warning'">
                      {{ scope.row.isRenewal === 1 ? "续费" : "登记" }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                    key="createTime"
                    label="创建时间"
                    prop="createTime"
                    min-width="150"
                    align="center"
                />
      </el-table>

      <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.current"
          v-model:limit="queryParams.size"
          @pagination="handleQuery()"
      />
    </el-card>
 </div>
</template>

<script setup lang="ts">
  import ParkingLotAPI, { GarageOption } from "@/api/hmj/parking-lot";

  defineOptions({
    name: "UserRegistration",
    inheritAttrs: false,
  });

  import UserRegistrationAPI, { UserRegistrationPageVO,  UserRegistrationPageQuery } from "@/api/hmj/user-registration";

  const queryFormRef = ref(ElForm);

  const loading = ref(false);
  const removeIds = ref<number[]>([]);
  const total = ref(0);

  const queryParams = reactive<UserRegistrationPageQuery>({
    current: 1,
    size: 10,
  });
  // 车库列表
  const garageList = ref<GarageOption[]>([]);
  // 用户信息登记表格数据
  const pageData = ref<UserRegistrationPageVO[]>([]);

  /** 查询用户信息登记 */
  function handleQuery() {
    loading.value = true;
          UserRegistrationAPI.getPage(queryParams)
        .then((data) => {
          pageData.value = data.records;
          total.value = data.total;
        })
        .finally(() => {
          loading.value = false;
        });
  }

  /** 重置用户信息登记查询 */
  function handleResetQuery() {
    queryFormRef.value!.resetFields();
    queryParams.current = 1;

    // 重置后默认选择第一个车库
    if (garageList.value.length > 0) {
      queryParams.garageId = garageList.value[0].id;
    }

    handleQuery();
  }

  /** 行复选框选中记录选中ID集合 */
  function handleSelectionChange(selection: any) {
    removeIds.value = selection.map((item: any) => item.id);
  }


  onMounted(() => {
    // 先加载车库列表，然后在回调中进行查询
    loadGarageListAndQuery();
  });

  /** 加载车库列表并查询 */
  function loadGarageListAndQuery() {
    ParkingLotAPI.getGarageList()
      .then((data) => {
        garageList.value = data || [];
        // 默认选择第一个车库
        if (garageList.value.length > 0) {
          queryParams.garageId = garageList.value[0].id;
          // 选择车库后进行查询
          handleQuery();
        } else {
          ElMessage.warning('暂无可用车库');
        }
      })
      .catch((error) => {
        console.error('获取车库列表失败:', error);
        ElMessage.error('获取车库列表失败');
      });
  }

  /** 解析图片URL字符串 */
  function parseImageUrls(ownerCertificate: string): string[] {
    if (!ownerCertificate || typeof ownerCertificate !== 'string') {
      return [];
    }

    // 按逗号分割并过滤空字符串
    return ownerCertificate
      .split(',')
      .map(url => url.trim())
      .filter(url => url.length > 0);
  }
</script>

<style scoped>
.image-preview-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

.preview-image {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
}

.preview-image:hover {
  border-color: #409eff;
  transform: scale(1.1);
}

.text-gray-400 {
  color: #9ca3af;
  font-size: 14px;
}
</style>
