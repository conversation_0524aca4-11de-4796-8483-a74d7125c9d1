<template>
  <div class="app-container">
    <div class="search-container">
      <div class="search-bar">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="queryParams.username"
              placeholder="用户名"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="手机号" prop="phone">
            <el-input
              v-model="queryParams.phone"
              placeholder="手机号"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>

          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="全部"
              clearable
              class="!w-[100px]"
            >
              <el-option label="正常" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <template #icon>
                <Search />
              </template>
              搜索
            </el-button>
            <el-button @click="handleResetQuery">
              <template #icon>
                <Refresh />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-card shadow="never" class="table-wrapper">
        <el-table
          v-loading="loading"
          :data="pageData"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column
            key="id"
            label="编号"
            align="center"
            prop="id"
            width="80"
          />
          <el-table-column
            key="username"
            label="用户名"
            align="center"
            prop="username"
          />
          <el-table-column label="用户昵称" align="center" prop="nickName" />

          <el-table-column label="性别" width="100" align="center" prop="sex">
            <template #default="scope">
              <DictLabel
                v-model="scope.row.sex"
                code="sys_user_sex"
                size="small"
              />
            </template>
          </el-table-column>

          <el-table-column label="手机号码" align="center" prop="phone" />

          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <el-tag :type="scope.row.status === '1' ? 'success' : 'info'">
                {{ scope.row.status === "1" ? "正常" : "禁用" }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="关联车位" align="center" width="200">
            <template #default="scope">
              <div v-if="scope.row.parkingList && scope.row.parkingList.length > 0">
                <el-tag
                  v-for="(parking, index) in scope.row.parkingList"
                  :key="index"
                  size="small"
                  class="mr-1 mb-1"
                  :type="getParkingTagType(parking)"
                  :effect="getParkingTagEffect(parking)"
                >
                  {{ parking.parkName }}
                  <el-tooltip
                    :content="getParkingTooltipContent(parking)"
                    placement="top"
                  >
                    <el-icon class="ml-1">
                      <InfoFilled />
                    </el-icon>
                  </el-tooltip>
                </el-tag>
              </div>
              <span v-else class="text-gray-400">未关联</span>
            </template>
          </el-table-column>

          <el-table-column label="关联车牌" align="center">
            <template #default="scope">
              <div
                v-if="
                  scope.row.plateNumbers && scope.row.plateNumbers.length > 0
                "
              >
                <el-tag
                  v-for="(plate, index) in scope.row.plateNumbers"
                  :key="index"
                  size="small"
                  class="mr-1 mb-1"
                >
                  {{ plate }}
                </el-tag>
              </div>
              <span v-else class="text-gray-400">未关联</span>
            </template>
          </el-table-column>

          <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            width="180"
          />
          <el-table-column label="操作" fixed="right" width="300">
            <template #default="scope">
              <el-button
                v-hasPerm="['sys_user_edit']"
                type="primary"
                link
                size="small"
                @click="handleOpenDialog(scope.row.id)"
              >
                <template #icon>
                  <Edit />
                </template>
                编辑
              </el-button>
              <el-button
                type="success"
                link
                size="small"
                @click="handleOpenParkingDialog(scope.row)"
              >
                <template #icon>
                  <Location />
                </template>
                关联车位
              </el-button>
              <el-button
                v-if="scope.row.parkingList && scope.row.parkingList.length > 0"
                type="warning"
                link
                size="small"
                @click="handleOpenUnbindDialog(scope.row)"
              >
                <template #icon>
                  <Remove />
                </template>
                解绑车位
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.current"
          v-model:limit="queryParams.size"
          @pagination="handleQuery"
        />
      </el-card>
    </div>

    <!-- 用户表单弹窗 -->
    <el-drawer
      v-model="dialog.visible"
      :title="dialog.title"
      append-to-body
      @close="handleCloseDialog"
    >
      <el-form
        ref="userFormRef"
        :model="formData"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="formData.username"
            :readonly="!!formData.id"
            placeholder="请输入用户名"
          />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="formData.password"
            type="password"
            show-password
            placeholder="请输入密码"
          />
        </el-form-item>

        <el-form-item label="用户昵称" prop="nickName">
          <el-input v-model="formData.nickName" placeholder="请输入用户昵称" />
        </el-form-item>

        <el-form-item label="性别" prop="sex">
          <Dict v-model="formData.sex" code="sys_user_sex" />
        </el-form-item>

        <el-form-item label="手机号码" prop="phone">
          <el-input
            v-model="formData.phone"
            placeholder="请输入手机号码"
            maxlength="11"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="formData.status"
            inline-prompt
            active-text="正常"
            inactive-text="禁用"
            active-value="1"
            inactive-value="0"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
          <el-button @click="handleCloseDialog">取 消</el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 车位关联弹窗 -->
    <el-dialog
      v-model="parkingDialog.visible"
      :title="parkingDialog.title"
      width="1000px"
      append-to-body
      @close="handleCloseParkingDialog"
    >
      <div class="parking-search-container">
        <!-- 搜索区域 -->
        <el-form :inline="true" class="search-form">
          <el-form-item label="搜索条件">
            <el-input
              v-model="parkingSearchKeyword"
              placeholder="请输入车位号或车牌号"
              clearable
              style="width: 300px"
              @keyup.enter="handleSearchParking"
            >
              <template #append>
                <el-button @click="handleSearchParking">
                  <template #icon>
                    <Search />
                  </template>
                </el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-form>

        <!-- 车位列表 -->
        <el-table
          v-loading="parkingLoading"
          :data="parkingList"
          @selection-change="handleParkingSelectionChange"
          @row-click="handleRowClick"
          max-height="400px"
          ref="parkingTableRef"
        >
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="车位池" prop="poolName" align="center" />
          <el-table-column label="车位号" prop="parkName" align="center" />
<!--          <el-table-column label="车库名称" prop="pgname" align="center" />-->
          <el-table-column label="车位类型" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.parkType === 0 ? 'success' : 'warning'">
                {{ scope.row.parkType === 0 ? "公用" : "私家" }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="有效期" align="center" min-width="120">
            <template #default="scope">
              <el-text v-if="scope.row.expireStatus === 1" type="warning">
                {{ scope.row.beginDateStr }} ~ {{ scope.row.endDateStr }}
              </el-text>
              <el-text v-else-if="scope.row.expireStatus === 2" type="danger">
                {{ scope.row.beginDateStr }} ~ {{ scope.row.endDateStr }}
              </el-text>
              <el-text v-else>
                {{ scope.row.beginDateStr }} ~ {{ scope.row.endDateStr }}
              </el-text>
            </template>
          </el-table-column>
          <el-table-column label="关联车牌" prop="plateNums" align="center">
            <template #default="scope">
              <span v-if="scope.row.plateNums">{{ scope.row.plateNums }}</span>
              <span v-else class="text-gray-400">未关联</span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 空状态 -->
        <el-empty
          v-if="!parkingLoading && parkingList.length === 0"
          description="暂无车位数据"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseParkingDialog">取 消</el-button>
          <el-button
            type="primary"
            :disabled="selectedParkings.length === 0"
            @click="handleConfirmParkingAssociation"
          >
            确认关联 ({{ selectedParkings.length }})
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 车位解绑弹窗 -->
    <el-dialog
      v-model="unbindDialog.visible"
      :title="unbindDialog.title"
      width="800px"
      append-to-body
      @close="handleCloseUnbindDialog"
    >
      <div class="unbind-container">
        <el-alert
          title="解绑提醒"
          type="warning"
          description="解绑后，车位将变为未关联状态，可以重新分配给其他用户。"
          show-icon
          :closable="false"
          class="mb-4"
        />

        <!-- 已关联车位列表 -->
        <el-table
          v-loading="unbindLoading"
          :data="currentUserParkings"
          @selection-change="handleUnbindSelectionChange"
          @row-click="handleUnbindRowClick"
          ref="unbindTableRef"
          max-height="400px"
        >
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="车位池" prop="poolName" align="center" />
          <el-table-column label="车位号" prop="parkName" align="center" />
<!--          <el-table-column label="车库名称" prop="pgname" align="center" />-->
          <el-table-column label="车位类型" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.parkType === 0 ? 'success' : 'warning'">
                {{ scope.row.parkType === 0 ? '公用' : '私家' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="关联车牌" prop="plateNums" align="center">
            <template #default="scope">
              <span v-if="scope.row.plateNums">{{ scope.row.plateNums }}</span>
              <span v-else class="text-gray-400">未关联</span>
            </template>
          </el-table-column>
          <el-table-column label="到期状态" align="center">
            <template #default="scope">
              <el-tag
                :type="getParkingTagType(scope.row)"
                :effect="getParkingTagEffect(scope.row)"
              >
                {{ getExpireStatusText(scope.row.expireStatus) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 空状态 -->
        <el-empty
          v-if="!unbindLoading && currentUserParkings.length === 0"
          description="该用户暂无关联车位"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseUnbindDialog">取 消</el-button>
          <el-button
            type="danger"
            :disabled="selectedUnbindParkings.length === 0"
            @click="handleConfirmUnbind"
          >
            确认解绑 ({{ selectedUnbindParkings.length }})
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { InfoFilled, Remove } from '@element-plus/icons-vue';

defineOptions({
  name: "User",
  inheritAttrs: false,
});

import OwnerAPI, {
  UserForm,
  UserPageQuery,
  UserPageVO,
  ParkingInfo,
  UnassociatedParkingQuery
} from "@/api/hmj/owner";
import PoolParkAPI, {
  PoolParkPageQuery,
  PoolParkPageVO,
} from "@/api/hmj/pool-park";

const queryFormRef = ref(ElForm);
const userFormRef = ref(ElForm);

const loading = ref(false);
const removeIds = ref([]);
const total = ref(0);
const pageData = ref<UserPageVO[]>();

/** 用户查询参数  */
const queryParams = reactive<UserPageQuery>({
  current: 1,
  size: 10,
});

/**  用户弹窗对象  */
const dialog = reactive({
  visible: false,
  title: "",
});

/** 车位关联弹窗对象 */
const parkingDialog = reactive({
  visible: false,
  title: "",
  currentUser: null as UserPageVO | null,
});

/** 车位解绑弹窗对象 */
const unbindDialog = reactive({
  visible: false,
  title: "",
  currentUser: null as UserPageVO | null,
});

// 车位相关数据
const parkingLoading = ref(false);
const parkingList = ref<ParkingInfo[]>([]);
const selectedParkings = ref<ParkingInfo[]>([]);
const parkingSearchKeyword = ref("");
const parkingTableRef = ref();

// 解绑相关数据
const unbindLoading = ref(false);
const currentUserParkings = ref<ParkingInfo[]>([]);
const selectedUnbindParkings = ref<ParkingInfo[]>([]);
const unbindTableRef = ref();

// 用户表单数据
const formData = reactive<UserForm>({
  status: "1",
});

/** 用户表单校验规则  */
const rules = reactive({
  username: [{ required: true, message: "用户名不能为空", trigger: "blur" }],
  nickname: [{ required: true, message: "用户昵称不能为空", trigger: "blur" }],
  phone: [
    {
      pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
});

/** 查询 */
function handleQuery() {
  loading.value = true;
  OwnerAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.records;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.current = 1;
  handleQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  removeIds.value = selection.map((item: any) => item.id);
}

/**
 * 打开弹窗
 *
 * @param id 用户ID
 */
async function handleOpenDialog(id?: number) {
  dialog.visible = true;

  if (id) {
    dialog.title = "修改用户";
    OwnerAPI.getFormData(id).then((data) => {
      Object.assign(formData, { ...data });
    });
  } else {
    dialog.title = "新增用户";
  }
}

/** 关闭弹窗 */
function handleCloseDialog() {
  dialog.visible = false;
  userFormRef.value.resetFields();
  userFormRef.value.clearValidate();

  formData.id = undefined;
  formData.status = "1";
}

/** 表单提交 */
const handleSubmit = useThrottleFn(() => {
  userFormRef.value.validate((valid: any) => {
    if (valid) {
      const userId = formData.id;
      loading.value = true;
      if (userId) {
        OwnerAPI.updateUserInfo(formData)
          .then(() => {
            ElMessage.success("修改用户成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      } else {
        ElMessage.warning("缺少用户id,无法修改用户信息");
      }
    }
  });
}, 3000);

/** 打开车位关联弹窗 */
function handleOpenParkingDialog(user: UserPageVO) {
  parkingDialog.visible = true;
  parkingDialog.title = `关联车位 - ${user.username}(${user.nickName})`;
  parkingDialog.currentUser = user;

  // 重置搜索条件和数据
  parkingSearchKeyword.value = "";
  selectedParkings.value = [];

  // 加载车位数据
  handleSearchParking();
}

/** 关闭车位关联弹窗 */
function handleCloseParkingDialog() {
  parkingDialog.visible = false;
  parkingDialog.currentUser = null;
  parkingList.value = [];
  selectedParkings.value = [];
  parkingSearchKeyword.value = "";
}

/** 搜索车位 */
function handleSearchParking() {
  parkingLoading.value = true;

  const queryParams: UnassociatedParkingQuery = {
    current: 1,
    size: 100, // 一次加载更多数据
  };

  // 如果有搜索关键词，添加搜索条件
  if (parkingSearchKeyword.value.trim()) {
    queryParams.keyword = parkingSearchKeyword.value.trim();
  }

  OwnerAPI.getUnassociatedParking(queryParams)
    .then((data) => {
      parkingList.value = data.records || [];
      if (parkingList.value.length === 0 && parkingSearchKeyword.value.trim()) {
        ElMessage.info("未找到匹配的未关联车位");
      }
    })
    .catch((error) => {
      console.error("获取未关联车位列表失败：", error);
      ElMessage.error("获取未关联车位列表失败");
    })
    .finally(() => {
      parkingLoading.value = false;
    });
}

/** 车位选择变化 */
function handleParkingSelectionChange(selection: ParkingInfo[]) {
  selectedParkings.value = selection;
}

/** 处理行点击事件 */
function handleRowClick(row: ParkingInfo) {
  // 检查当前行是否已选中
  const isSelected = selectedParkings.value.some(item => item.id === row.id);

  if (isSelected) {
    // 如果已选中，则取消选中
    parkingTableRef.value?.toggleRowSelection(row, false);
  } else {
    // 如果未选中，则选中
    parkingTableRef.value?.toggleRowSelection(row, true);
  }
}

/** 确认车位关联 */
function handleConfirmParkingAssociation() {
  if (!parkingDialog.currentUser || selectedParkings.value.length === 0) {
    ElMessage.warning("请选择要关联的车位");
    return;
  }

  const user = parkingDialog.currentUser;
  const parkingNames = selectedParkings.value.map((p) => p.parkName).join(", ");

  ElMessageBox.confirm(
    `确定要将车位 "${parkingNames}" 关联给用户 "${user.username}" 吗？`,
    "确认关联",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      // TODO: 调用关联API
      handleAssociateParkingToUser(user.id!, selectedParkings.value);
    })
    .catch(() => {
      ElMessage.info("已取消关联");
    });
}

/** 执行车位关联 */
function handleAssociateParkingToUser(
  userId: number,
  parkings: ParkingInfo[]
) {
  const parkingIds = parkings.map((p) => p.id!);

  OwnerAPI.associateParking(userId, parkingIds)
    .then(() => {
      ElMessage.success(`成功关联 ${parkings.length} 个车位`);
      handleCloseParkingDialog();
      // 刷新用户列表
      handleQuery();
    })
    .catch((error) => {
      console.error("关联车位失败：", error);
      ElMessage.error("关联车位失败，请稍后重试");
    });
}

/** 获取车位标签类型（根据后端返回的到期状态） */
function getParkingTagType(parking: any) {
  switch (parking.expireStatus) {
    case 0:
      return 'success'; // 正常 - 绿色
    case 1:
      return 'warning'; // 即将到期 - 橙色
    case 2:
      return 'danger'; // 已到期 - 红色
    case 3:
    default:
      return 'info'; // 未知状态 - 灰色
  }
}

/** 获取车位标签效果 */
function getParkingTagEffect(parking: any) {
  return parking.expireStatus === 2 ? 'dark' : 'light'; // 已到期使用深色效果
}

/** 获取车位提示内容 */
function getParkingTooltipContent(parking: any) {
  const endDate = parking.endDateStr || '未知';

  switch (parking.expireStatus) {
    case 0:
      return `到期时间: ${endDate}`;
    case 1:
      return `即将到期 - ${endDate}`;
    case 2:
      return `已到期 - ${endDate}`;
    case 3:
    default:
      return `到期时间: ${endDate}`;
  }
}

/** 获取到期状态文本 */
function getExpireStatusText(expireStatus: number) {
  switch (expireStatus) {
    case 0:
      return '正常';
    case 1:
      return '即将到期';
    case 2:
      return '已到期';
    case 3:
    default:
      return '未知';
  }
}

/** 打开解绑车位弹窗 */
function handleOpenUnbindDialog(user: UserPageVO) {
  unbindDialog.visible = true;
  unbindDialog.title = `解绑车位 - ${user.username}(${user.nickName})`;
  unbindDialog.currentUser = user;

  // 重置数据
  selectedUnbindParkings.value = [];

  // 设置当前用户的车位列表
  currentUserParkings.value = user.parkingList || [];
}

/** 关闭解绑车位弹窗 */
function handleCloseUnbindDialog() {
  unbindDialog.visible = false;
  unbindDialog.currentUser = null;
  currentUserParkings.value = [];
  selectedUnbindParkings.value = [];
}

/** 解绑车位选择变化 */
function handleUnbindSelectionChange(selection: ParkingInfo[]) {
  selectedUnbindParkings.value = selection;
}

/** 处理解绑表格行点击事件 */
function handleUnbindRowClick(row: ParkingInfo) {
  // 检查当前行是否已选中
  const isSelected = selectedUnbindParkings.value.some(item => item.id === row.id);

  if (isSelected) {
    // 如果已选中，则取消选中
    unbindTableRef.value?.toggleRowSelection(row, false);
  } else {
    // 如果未选中，则选中
    unbindTableRef.value?.toggleRowSelection(row, true);
  }
}

/** 确认解绑车位 */
function handleConfirmUnbind() {
  if (!unbindDialog.currentUser || selectedUnbindParkings.value.length === 0) {
    ElMessage.warning("请选择要解绑的车位");
    return;
  }

  const user = unbindDialog.currentUser;
  const parkingNames = selectedUnbindParkings.value.map(p => p.parkName).join(", ");

  ElMessageBox.confirm(
    `确定要解绑车位 "${parkingNames}" 吗？解绑后车位将变为未关联状态。`,
    "确认解绑",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  ).then(() => {
    handleExecuteUnbind(user.id!, selectedUnbindParkings.value);
  }).catch(() => {
    ElMessage.info("已取消解绑");
  });
}

/** 执行车位解绑 */
function handleExecuteUnbind(userId: number, parkings: ParkingInfo[]) {
  const parkingIds = parkings.map(p => p.id!);

  OwnerAPI.unbindParking(userId, parkingIds)
    .then(() => {
      ElMessage.success(`成功解绑 ${parkings.length} 个车位`);
      handleCloseUnbindDialog();
      // 刷新用户列表
      handleQuery();
    })
    .catch((error) => {
      console.error("解绑车位失败：", error);
      ElMessage.error("解绑车位失败，请稍后重试");
    });
}

onMounted(() => {
  handleQuery();
});
</script>

<style scoped lang="scss">
.parking-search-container {
  .search-form {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
  }

  .el-table {
    border-radius: 8px;
    overflow: hidden;
  }

  .text-gray-400 {
    color: #9ca3af;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 车位标签样式 */
.el-tag {
  .el-icon {
    cursor: help;
    opacity: 0.7;
    transition: opacity 0.3s;

    &:hover {
      opacity: 1;
    }
  }
}

/* 工具类 */
.mr-1 {
  margin-right: 4px;
}

.mb-1 {
  margin-bottom: 4px;
}

.ml-1 {
  margin-left: 4px;
}

.text-gray-400 {
  color: #9ca3af;
}

/* 车位表格行样式 */
.el-table__row {
  cursor: pointer;
  transition: background-color 0.3s;
}

.el-table__row:hover {
  background-color: #f5f7fa !important;
}

/* 解绑容器样式 */
.unbind-container {
  .mb-4 {
    margin-bottom: 16px;
  }
}
</style>
