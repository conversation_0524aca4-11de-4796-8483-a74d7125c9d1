<template>
  <div class="app-container">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="车主" prop="pname">
          <el-input
            v-model="queryParams.pname"
            placeholder="车主"
            clearable
            @keyup.enter="handleQuery()"
          />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="queryParams.phone"
            placeholder="手机号"
            clearable
            @keyup.enter="handleQuery()"
          />
        </el-form-item>
        <el-form-item label="充值流水号" prop="rechargeSn">
          <el-input
            v-model="queryParams.rechargeSn"
            placeholder="充值流水号"
            clearable
            @keyup.enter="handleQuery()"
          />
        </el-form-item>
        <el-form-item label="支付状态" prop="payStatus">
          <dict v-model="queryParams.payStatus" code="pay_status" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <template #icon>
              <Search />
            </template>
            搜索
          </el-button>
          <el-button @click="handleResetQuery">
            <template #icon>
              <Refresh />
            </template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
          :index="
            (index) => (queryParams.current - 1) * queryParams.size + index + 1
          "
        />
        <el-table-column
          key="userId"
          label="用户ID"
          prop="userId"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="pname"
          label="车主"
          prop="pname"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="openid"
          label="openid"
          prop="openid"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="phone"
          label="手机号"
          prop="phone"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="parkNum"
          label="车位号"
          prop="parkNum"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="subMchid"
          label="二级商户号"
          prop="subMchid"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="rechargeSn"
          label="充值流水号"
          prop="rechargeSn"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="rechargeAmount"
          label="充值金额"
          prop="rechargeAmount"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="rechargeType"
          label="充值类型"
          prop="rechargeType"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="isRenewal"
          label="是否续费"
          prop="isRenewal"
          min-width="150"
          align="center"
        >
          <template #default="scope">
            <el-tag :type="scope.row.isRenewal === 1 ? 'success' : 'warning'">
              {{ scope.row.isRenewal === 1 ? "续费" : "登记" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          key="tradeNo"
          label="交易流水号"
          prop="tradeNo"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="payStatus"
          label="支付状态"
          prop="payStatus"
          min-width="150"
          align="center"
        >
          <template #default="scope">
            <DictLabel
              code="pay_status"
              v-model="scope.row.payStatus"
              size="small"
            />
          </template>
        </el-table-column>
        <el-table-column
          key="timeEnd"
          label="支付完成时间"
          prop="timeEnd"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="createTime"
          label="创建时间"
          prop="createTime"
          min-width="160"
          align="center"
        />
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.current"
        v-model:limit="queryParams.size"
        @pagination="handleQuery()"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "RechargeRecord",
  inheritAttrs: false,
});

import RechargeRecordAPI, {
  RechargeRecordPageVO,
  RechargeRecordForm,
  RechargeRecordPageQuery,
} from "@/api/hmj/recharge-record";

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);

const loading = ref(false);
const removeIds = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<RechargeRecordPageQuery>({
  current: 1,
  size: 10,
});

// 充值记录表格数据
const pageData = ref<RechargeRecordPageVO[]>([]);

// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

// 充值记录表单数据
const formData = reactive<RechargeRecordForm>({});

// 充值记录表单校验规则
const rules = reactive({
  userId: [{ required: true, message: "请输入用户ID", trigger: "blur" }],
  pname: [{ required: true, message: "请输入车主", trigger: "blur" }],
  openid: [{ required: true, message: "请输入openid", trigger: "blur" }],
  phone: [{ required: true, message: "请输入手机号", trigger: "blur" }],
  parkNum: [{ required: true, message: "请输入车位号", trigger: "blur" }],
  garageName: [{ required: true, message: "请输入车库名称", trigger: "blur" }],
  subMchid: [{ required: true, message: "请输入二级商户号", trigger: "blur" }],
  rechargeSn: [
    { required: true, message: "请输入充值流水号", trigger: "blur" },
  ],
  rechargeAmount: [
    { required: true, message: "请输入充值金额", trigger: "blur" },
  ],
  rechargeType: [
    { required: true, message: "请输入充值类型", trigger: "blur" },
  ],
  tradeNo: [
    {
      required: true,
      message: "请输入微信系统中的交易流水号",
      trigger: "blur",
    },
  ],
  payStatus: [
    {
      required: true,
      message: "请输入支付状态 1.待支付 2.已支付 3.已取消",
      trigger: "blur",
    },
  ],
  timeEnd: [
    {
      required: true,
      message: "请输入支付完成时间 yyyyMMddHHmmss",
      trigger: "blur",
    },
  ],
  remark: [{ required: true, message: "请输入备注", trigger: "blur" }],
  modifyTime: [{ required: true, message: "请输入修改时间", trigger: "blur" }],
  valid: [
    {
      required: true,
      message: "请输入有效状态：0->无效；1->有效",
      trigger: "blur",
    },
  ],
});

/** 查询充值记录 */
function handleQuery() {
  loading.value = true;
  RechargeRecordAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.records;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置充值记录查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.current = 1;
  handleQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  removeIds.value = selection.map((item: any) => item.id);
}

/** 打开充值记录弹窗 */
function handleOpenDialog(id?: number) {
  dialog.visible = true;
  if (id) {
    dialog.title = "修改充值记录";
    RechargeRecordAPI.getFormData(id).then((data) => {
      Object.assign(formData, data);
    });
  } else {
    dialog.title = "新增充值记录";
  }
}

/** 提交充值记录表单 */
function handleSubmit() {
  dataFormRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true;
      const id = formData.id;
      if (id) {
        RechargeRecordAPI.update(formData)
          .then(() => {
            ElMessage.success("修改成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      } else {
        RechargeRecordAPI.add(formData)
          .then(() => {
            ElMessage.success("新增成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}

/** 关闭充值记录弹窗 */
function handleCloseDialog() {
  dialog.visible = false;
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  formData.id = undefined;
}

/** 删除充值记录 */
function handleDelete(id?: number) {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      RechargeRecordAPI.deleteByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

onMounted(() => {
  handleQuery();
});
</script>
