{"compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "strict": true, "noLib": false, "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "baseUrl": ".", "allowJs": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "jsx": "preserve", "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "paths": {"@/*": ["src/*"]}, "types": ["vite/client", "unplugin-icons/types/vue", "element-plus/global"]}, "include": ["mock/**/*.ts", "src/**/*.ts", "src/**/*.vue", "vite.config.ts"], "exclude": ["node_modules", "dist", "**/*.js"]}