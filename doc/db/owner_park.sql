/*
 Navicat Premium Dump SQL

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80041 (8.0.41)
 Source Host           : localhost:3306
 Source Schema         : owner_park

 Target Server Type    : MySQL
 Target Server Version : 80041 (8.0.41)
 File Encoding         : 65001

 Date: 25/07/2025 14:53:53
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for gen_config
-- ----------------------------
DROP TABLE IF EXISTS `gen_config`;
CREATE TABLE `gen_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `table_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '表名',
  `module_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模块名',
  `package_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '包名',
  `business_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务名',
  `entity_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '实体类名',
  `author` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '作者',
  `parent_menu_id` bigint NULL DEFAULT NULL COMMENT '上级菜单ID，对应sys_menu的id ',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `valid` int NULL DEFAULT 1 COMMENT '有效状态，0:无效 1:有效',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_tablename`(`table_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成基础配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gen_config
-- ----------------------------

-- ----------------------------
-- Table structure for gen_field_config
-- ----------------------------
DROP TABLE IF EXISTS `gen_field_config`;
CREATE TABLE `gen_field_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `config_id` bigint NOT NULL COMMENT '关联的配置ID',
  `column_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `column_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `column_length` int NULL DEFAULT NULL,
  `field_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字段名称',
  `field_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字段类型',
  `field_sort` int NULL DEFAULT NULL COMMENT '字段排序',
  `field_comment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字段描述',
  `max_length` int NULL DEFAULT NULL,
  `is_required` tinyint(1) NULL DEFAULT NULL COMMENT '是否必填',
  `is_show_in_list` tinyint(1) NULL DEFAULT 0 COMMENT '是否在列表显示',
  `is_show_in_form` tinyint(1) NULL DEFAULT 0 COMMENT '是否在表单显示',
  `is_show_in_query` tinyint(1) NULL DEFAULT 0 COMMENT '是否在查询条件显示',
  `query_type` tinyint NULL DEFAULT NULL COMMENT '查询方式',
  `form_type` tinyint NULL DEFAULT NULL COMMENT '表单类型',
  `dict_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典类型',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `config_id`(`config_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 212 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成字段配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gen_field_config
-- ----------------------------

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '系统内置: Y N ',
  `config_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '键',
  `config_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '值',
  `config_name` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态， 0：禁用 1：启用',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `valid` int NULL DEFAULT 1 COMMENT '有效状态，0:无效 1:有效',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `param_key_idx`(`config_key` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, 'Y', 'SYS_CAPTCHA_IMG', 'false', '系统验证码开关', '1', '系统验证码开关', '2024-12-20 16:00:45', '2024-12-20 16:00:45', 1);

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `pid` bigint NOT NULL COMMENT '上级部门',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '祖级列表',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '启用状态，0:禁用 1:启用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `valid` int NULL DEFAULT 1 COMMENT '有效状态，0:无效 1:有效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '部门表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES (1, 0, '0', 'Harry技术', 0, '1', '2024-12-02 15:35:36', '2024-12-02 15:36:08', 1);
INSERT INTO `sys_dept` VALUES (2, 1, '0,1', '研发部', 0, '1', '2024-12-02 15:35:36', '2024-12-02 15:35:59', 1);
INSERT INTO `sys_dept` VALUES (3, 1, '0,1', '营销部', 1, '1', '2024-12-02 15:35:36', '2024-12-02 15:35:57', 1);
INSERT INTO `sys_dept` VALUES (4, 1, '0,1', '产品部', 1, '1', '2024-12-02 15:35:36', '2024-12-02 15:35:36', 1);
INSERT INTO `sys_dept` VALUES (5, 4, '0,1,4', '产品1部', 1, '1', '2024-12-27 10:41:25', '2024-12-27 10:41:25', 1);
INSERT INTO `sys_dept` VALUES (6, 4, '0,1,4', '产品2部', 1, '1', '2024-12-27 10:41:25', '2024-12-27 10:41:25', 1);

-- ----------------------------
-- Table structure for sys_dict
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict`;
CREATE TABLE `sys_dict`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '字典名称',
  `type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '启用状态，0:禁用 1:启用',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `valid` int NULL DEFAULT 1 COMMENT '有效状态，0:无效 1:有效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '数据字典' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict
-- ----------------------------
INSERT INTO `sys_dict` VALUES (1, '用户性别', 'sys_user_sex', '1', '用户性别', '2024-12-02 14:51:06', '2024-12-02 14:51:12', 1);
INSERT INTO `sys_dict` VALUES (2, '菜单状态', 'sys_show_hide', '1', '菜单状态', '2024-12-02 14:51:06', '2024-12-02 14:51:14', 1);
INSERT INTO `sys_dict` VALUES (3, '系统开关', 'sys_normal_disable', '1', '系统开关', '2024-12-02 14:51:06', '2024-12-02 14:51:15', 1);
INSERT INTO `sys_dict` VALUES (4, '任务状态', 'sys_job_status', '1', '任务状态', '2024-12-02 14:51:06', '2024-12-02 14:51:16', 1);
INSERT INTO `sys_dict` VALUES (5, '任务分组', 'sys_job_group', '1', '任务分组', '2024-12-02 14:51:06', '2024-12-02 14:51:17', 1);
INSERT INTO `sys_dict` VALUES (6, '系统是否', 'sys_yes_no', '1', '系统是否', '2024-12-02 14:51:06', '2024-12-02 14:51:18', 1);
INSERT INTO `sys_dict` VALUES (7, '通知类型', 'sys_notice_type', '1', '通知类型', '2024-12-02 14:51:06', '2024-12-02 14:51:19', 1);
INSERT INTO `sys_dict` VALUES (8, '通知状态', 'sys_notice_status', '1', '通知状态', '2024-12-02 14:51:06', '2024-12-02 14:51:21', 1);
INSERT INTO `sys_dict` VALUES (9, '操作类型', 'sys_oper_type', '1', '操作类型', '2024-12-02 14:51:06', '2024-12-02 14:51:22', 1);
INSERT INTO `sys_dict` VALUES (10, '系统状态', 'sys_common_status', '1', '系统状态', '2024-12-02 14:51:06', '2024-12-02 14:51:24', 1);
INSERT INTO `sys_dict` VALUES (11, '用户岗位', 'sys_user_post', '1', '用户岗位', '2024-12-02 14:51:06', '2024-12-02 14:51:25', 1);
INSERT INTO `sys_dict` VALUES (12, '菜单类型', 'sys_menu_type', '1', '权限类型：0->目录；1->菜单；2->按钮', '2024-12-02 14:51:06', '2024-12-02 14:51:27', 1);
INSERT INTO `sys_dict` VALUES (14, '桶类型', 'sys_bucket_type', '1', '桶类型 aliyun,minio ', '2025-01-10 11:36:26', '2025-01-10 11:36:26', 1);
INSERT INTO `sys_dict` VALUES (15, '计费类型', 'card_type', '1', '1 贵宾车 2 月票车 3 储值票车 4 临时车 5 免费车 6 时租车 7 车位池车 8 公务车 9 月结车', '2025-06-30 12:47:05', '2025-06-30 12:47:05', 1);
INSERT INTO `sys_dict` VALUES (16, '计费类型', 'billing_type', '1', '计费类型 1.日租 2.月租 3.季租', '2025-07-01 18:34:43', '2025-07-01 18:34:43', 1);
INSERT INTO `sys_dict` VALUES (17, '支付状态', 'pay_status', '1', '支付状态 1.待支付 2.已支付 3.已取消', '2025-07-02 20:14:25', '2025-07-02 20:14:25', 1);
INSERT INTO `sys_dict` VALUES (18, '车辆类型', 'vehicle_type', '1', '1 小型车\n2 摩托车\n3 中型车\n4 大型车\n5 运输车\n6 备用车', '2025-07-03 15:31:44', '2025-07-03 15:31:44', 1);
INSERT INTO `sys_dict` VALUES (19, '车位状态', 'pool_park_status', '1', '状态：0 空闲 1 占用 2 待支付', '2025-07-25 09:11:45', '2025-07-25 09:11:45', 1);

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `dict_sort` int NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '是否默认，Y:是 N:否',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '启用状态，0:禁用 1:启用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `valid` int NULL DEFAULT 1 COMMENT '有效状态，0:无效 1:有效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 66 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (1, 1, '男', '0', 'sys_user_sex', '', 'success', 'Y', '1', '2024-12-02 14:52:18', '2024-12-02 14:52:51', 1);
INSERT INTO `sys_dict_data` VALUES (2, 2, '女', '1', 'sys_user_sex', '', 'warning', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:52:52', 1);
INSERT INTO `sys_dict_data` VALUES (3, 3, '未知', '2', 'sys_user_sex', '', 'info', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:52:52', 1);
INSERT INTO `sys_dict_data` VALUES (4, 1, '显示', '1', 'sys_show_hide', '', 'primary', 'Y', '1', '2024-12-02 14:52:18', '2024-12-02 14:52:53', 1);
INSERT INTO `sys_dict_data` VALUES (5, 2, '隐藏', '0', 'sys_show_hide', '', 'danger', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:52:54', 1);
INSERT INTO `sys_dict_data` VALUES (6, 1, '正常', '1', 'sys_normal_disable', '', 'primary', 'Y', '1', '2024-12-02 14:52:18', '2024-12-02 14:52:55', 1);
INSERT INTO `sys_dict_data` VALUES (7, 2, '停用', '0', 'sys_normal_disable', '', 'danger', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:52:55', 1);
INSERT INTO `sys_dict_data` VALUES (8, 1, '正常', '1', 'sys_job_status', '', 'primary', 'Y', '1', '2024-12-02 14:52:18', '2024-12-02 14:52:56', 1);
INSERT INTO `sys_dict_data` VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:52:58', 1);
INSERT INTO `sys_dict_data` VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', 'primary', 'Y', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:00', 1);
INSERT INTO `sys_dict_data` VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', 'primary', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:02', 1);
INSERT INTO `sys_dict_data` VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:05', 1);
INSERT INTO `sys_dict_data` VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:07', 1);
INSERT INTO `sys_dict_data` VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:08', 1);
INSERT INTO `sys_dict_data` VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:10', 1);
INSERT INTO `sys_dict_data` VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:12', 1);
INSERT INTO `sys_dict_data` VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:13', 1);
INSERT INTO `sys_dict_data` VALUES (18, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:16', 1);
INSERT INTO `sys_dict_data` VALUES (19, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:18', 1);
INSERT INTO `sys_dict_data` VALUES (20, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:21', 1);
INSERT INTO `sys_dict_data` VALUES (21, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:22', 1);
INSERT INTO `sys_dict_data` VALUES (22, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:23', 1);
INSERT INTO `sys_dict_data` VALUES (23, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:24', 1);
INSERT INTO `sys_dict_data` VALUES (24, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:26', 1);
INSERT INTO `sys_dict_data` VALUES (25, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:27', 1);
INSERT INTO `sys_dict_data` VALUES (26, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:28', 1);
INSERT INTO `sys_dict_data` VALUES (27, 1, '成功', '1', 'sys_common_status', '', 'primary', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:30', 1);
INSERT INTO `sys_dict_data` VALUES (28, 2, '失败', '0', 'sys_common_status', '', 'danger', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:32', 1);
INSERT INTO `sys_dict_data` VALUES (29, 1, '董事长', '1', 'sys_user_post', NULL, NULL, 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:33', 1);
INSERT INTO `sys_dict_data` VALUES (30, 2, '项目经理', '2', 'sys_user_post', NULL, NULL, 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:35', 1);
INSERT INTO `sys_dict_data` VALUES (31, 3, '人力资源', '3', 'sys_user_post', NULL, NULL, 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:36', 1);
INSERT INTO `sys_dict_data` VALUES (32, 4, '产品经理', '4', 'sys_user_post', NULL, NULL, 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:38', 1);
INSERT INTO `sys_dict_data` VALUES (33, 5, '普通员工', '5', 'sys_user_post', NULL, NULL, 'Y', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:39', 1);
INSERT INTO `sys_dict_data` VALUES (34, 1, '目录', '0', 'sys_menu_type', NULL, 'primary', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:53:41', 1);
INSERT INTO `sys_dict_data` VALUES (35, 2, '菜单', '1', 'sys_menu_type', NULL, 'success', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:54:55', 1);
INSERT INTO `sys_dict_data` VALUES (36, 3, '按钮', '2', 'sys_menu_type', NULL, 'warning', 'N', '1', '2024-12-02 14:52:18', '2024-12-02 14:54:57', 1);
INSERT INTO `sys_dict_data` VALUES (38, 4, '外链', '4', 'sys_menu_type', NULL, 'primary', 'N', '1', '2024-12-20 12:04:02', '2024-12-20 12:04:02', 1);
INSERT INTO `sys_dict_data` VALUES (39, 1, 'ALIYUN', 'ALIYUN', 'sys_bucket_type', NULL, 'success', 'N', '1', '2025-01-10 11:37:40', '2025-01-10 11:37:40', 1);
INSERT INTO `sys_dict_data` VALUES (40, 1, 'MINIO', 'MINIO', 'sys_bucket_type', NULL, 'warning', 'N', '1', '2025-01-10 11:37:54', '2025-01-10 11:37:54', 1);
INSERT INTO `sys_dict_data` VALUES (41, 1, '贵宾车', '1', 'card_type', NULL, '', 'N', '1', '2025-06-30 12:47:35', '2025-06-30 12:47:35', 1);
INSERT INTO `sys_dict_data` VALUES (42, 2, '月票车', '2', 'card_type', NULL, 'warning', 'N', '1', '2025-06-30 12:47:43', '2025-06-30 12:47:43', 1);
INSERT INTO `sys_dict_data` VALUES (43, 3, '储值票车', '3', 'card_type', NULL, '', 'N', '1', '2025-06-30 12:47:50', '2025-06-30 12:47:50', 1);
INSERT INTO `sys_dict_data` VALUES (44, 4, '临时车', '4', 'card_type', NULL, '', 'N', '1', '2025-06-30 12:47:56', '2025-06-30 12:47:56', 1);
INSERT INTO `sys_dict_data` VALUES (45, 5, '免费车', '5', 'card_type', NULL, '', 'N', '1', '2025-06-30 12:48:03', '2025-06-30 12:48:03', 1);
INSERT INTO `sys_dict_data` VALUES (46, 6, '时租车', '6', 'card_type', NULL, '', 'N', '1', '2025-06-30 12:48:11', '2025-06-30 12:48:11', 1);
INSERT INTO `sys_dict_data` VALUES (47, 7, '车位池车', '7', 'card_type', NULL, 'primary', 'N', '1', '2025-06-30 12:48:18', '2025-06-30 12:48:18', 1);
INSERT INTO `sys_dict_data` VALUES (48, 8, '公务车', '8', 'card_type', NULL, '', 'N', '1', '2025-06-30 12:48:25', '2025-06-30 12:48:25', 1);
INSERT INTO `sys_dict_data` VALUES (49, 9, '月结车', '9', 'card_type', NULL, '', 'N', '1', '2025-06-30 12:48:33', '2025-06-30 12:48:33', 1);
INSERT INTO `sys_dict_data` VALUES (50, 1, '日租', '0.01', 'billing_type', NULL, NULL, 'N', '1', '2025-07-01 18:35:26', '2025-07-01 18:35:26', 1);
INSERT INTO `sys_dict_data` VALUES (51, 1, '月租', '0.02', 'billing_type', NULL, NULL, 'N', '1', '2025-07-01 18:35:41', '2025-07-01 18:35:41', 1);
INSERT INTO `sys_dict_data` VALUES (52, 1, '季租', '0.03', 'billing_type', NULL, NULL, 'N', '1', '2025-07-01 18:35:59', '2025-07-01 18:35:59', 1);
INSERT INTO `sys_dict_data` VALUES (53, 1, '年租', '7800', 'billing_type', NULL, NULL, 'N', '0', '2025-07-01 18:35:59', '2025-07-01 18:35:59', 1);
INSERT INTO `sys_dict_data` VALUES (54, 1, '待支付', '1', 'pay_status', NULL, 'warning', 'N', '1', '2025-07-02 20:14:38', '2025-07-02 20:14:38', 1);
INSERT INTO `sys_dict_data` VALUES (55, 2, '已支付', '2', 'pay_status', NULL, 'success', 'N', '1', '2025-07-02 20:14:47', '2025-07-02 20:14:47', 1);
INSERT INTO `sys_dict_data` VALUES (56, 3, '已取消', '3', 'pay_status', NULL, 'info', 'N', '1', '2025-07-02 20:14:56', '2025-07-02 20:14:56', 1);
INSERT INTO `sys_dict_data` VALUES (57, 1, '小型车', '1', 'vehicle_type', NULL, NULL, 'N', '1', '2025-07-03 15:31:56', '2025-07-03 15:31:56', 1);
INSERT INTO `sys_dict_data` VALUES (58, 2, '摩托车', '2', 'vehicle_type', NULL, NULL, 'N', '1', '2025-07-03 15:32:04', '2025-07-03 15:32:04', 1);
INSERT INTO `sys_dict_data` VALUES (59, 3, '中型车', '3', 'vehicle_type', NULL, NULL, 'N', '1', '2025-07-03 15:32:11', '2025-07-03 15:32:11', 1);
INSERT INTO `sys_dict_data` VALUES (60, 4, '大型车', '4', 'vehicle_type', NULL, NULL, 'N', '1', '2025-07-03 15:32:18', '2025-07-03 15:32:18', 1);
INSERT INTO `sys_dict_data` VALUES (61, 5, '运输车', '5', 'vehicle_type', NULL, NULL, 'N', '1', '2025-07-03 15:32:26', '2025-07-03 15:32:26', 1);
INSERT INTO `sys_dict_data` VALUES (62, 6, '备用车', '6', 'vehicle_type', NULL, NULL, 'N', '1', '2025-07-03 15:32:33', '2025-07-03 15:32:33', 1);
INSERT INTO `sys_dict_data` VALUES (63, 1, '空闲', '0', 'pool_park_status', NULL, 'success', 'N', '1', '2025-07-25 09:12:20', '2025-07-25 09:12:20', 1);
INSERT INTO `sys_dict_data` VALUES (64, 2, '占用', '1', 'pool_park_status', NULL, 'primary', 'N', '1', '2025-07-25 09:12:36', '2025-07-25 09:12:36', 1);
INSERT INTO `sys_dict_data` VALUES (65, 2, '待支付', '2', 'pool_park_status', NULL, 'warning', 'N', '1', '2025-07-25 09:12:46', '2025-07-25 09:12:46', 1);

-- ----------------------------
-- Table structure for sys_file
-- ----------------------------
DROP TABLE IF EXISTS `sys_file`;
CREATE TABLE `sys_file`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件名',
  `original` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原始文件名',
  `bucket_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件存储桶名称',
  `bucket_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '桶类型 aliyun,minio',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件类型',
  `file_size` bigint NULL DEFAULT NULL COMMENT '文件大小',
  `domain` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '访问地址',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `modify_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `valid` int NULL DEFAULT 1 COMMENT '有效状态，0:无效 1:有效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文件管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_file
-- ----------------------------
INSERT INTO `sys_file` VALUES (1, '20250701/edb405a687c6462f9a750c1c88f02412.jpg', 'p2qMz3i4OK6Se59b22deee42c32b126f81a9fe918e6d.jpg', 'harry', 'MINIO', 'image/jpeg', 68577, 'https://minio.tech-harry.cn/harry/20250701/edb405a687c6462f9a750c1c88f02412.jpg', '2025-07-01 21:55:54', 'u0701174822', '2025-07-01 21:55:54', 'u0701174822', 1);
INSERT INTO `sys_file` VALUES (2, '20250701/7fcb9e8e080c43488fc4919c8d37d250.png', 'fzUL3R9SQl6Y996c8b5ffa1933b553b784260ef685b4.png', 'harry', 'MINIO', 'image/png', 178567, 'https://minio.tech-harry.cn/harry/20250701/7fcb9e8e080c43488fc4919c8d37d250.png', '2025-07-01 21:56:29', 'u0701174822', '2025-07-01 21:56:29', 'u0701174822', 1);
INSERT INTO `sys_file` VALUES (3, '20250701/9ca9ac8c3edf4099b3f1a989249cb6f5.jpg', 'pF9glZq1EJCte59b22deee42c32b126f81a9fe918e6d.jpg', 'harry', 'MINIO', 'image/jpeg', 68577, 'https://minio.tech-harry.cn/harry/20250701/9ca9ac8c3edf4099b3f1a989249cb6f5.jpg', '2025-07-01 22:05:29', 'u0701174822', '2025-07-01 22:05:29', 'u0701174822', 1);
INSERT INTO `sys_file` VALUES (4, '20250701/ca8b0498aec74d8a841552db583308e4.png', 'Amy04EInhZfDefa645a067073430993073066b68c4ee.png', 'harry', 'MINIO', 'image/png', 332098, 'https://minio.tech-harry.cn/harry/20250701/ca8b0498aec74d8a841552db583308e4.png', '2025-07-01 22:05:34', 'u0701174822', '2025-07-01 22:05:34', 'u0701174822', 1);
INSERT INTO `sys_file` VALUES (5, '20250701/6e7edb43a48643c4b500ece1fff5485b.jpg', 'tmp_3ca8fc668afd3cddfa97ad8249e2e109de5fb4802907084e.jpg', 'harry', 'MINIO', 'image/jpeg', 61916, 'https://minio.tech-harry.cn/harry/20250701/6e7edb43a48643c4b500ece1fff5485b.jpg', '2025-07-01 22:06:14', 'u0701174822', '2025-07-01 22:06:14', 'u0701174822', 1);
INSERT INTO `sys_file` VALUES (6, '20250701/f88001a60b8946e898a52dbaa17b7ecf.jpg', 'tmp_4f6a832afcaab45d7710894e254bdadd075d8cfd86455914.jpg', 'harry', 'MINIO', 'image/jpeg', 68815, 'https://minio.tech-harry.cn/harry/20250701/f88001a60b8946e898a52dbaa17b7ecf.jpg', '2025-07-01 22:06:25', 'u0701174822', '2025-07-01 22:06:25', 'u0701174822', 1);
INSERT INTO `sys_file` VALUES (7, '20250701/ac920f5d6d474504897c7db2376b71d4.jpg', 'tmp_e37d9bef2386f31906dabaa5084bdc51bf44c4a7b87c5a55.jpg', 'harry', 'MINIO', 'image/jpeg', 63547, 'https://minio.tech-harry.cn/harry/20250701/ac920f5d6d474504897c7db2376b71d4.jpg', '2025-07-01 22:32:35', 'u0701174822', '2025-07-01 22:32:35', 'u0701174822', 1);
INSERT INTO `sys_file` VALUES (8, '20250702/73ea259090a64ee98a62fc6bfd81aac3.jpg', 'eSRAuy3dSHVhe59b22deee42c32b126f81a9fe918e6d.jpg', 'harry', 'MINIO', 'image/jpeg', 68577, 'https://minio.tech-harry.cn/harry/20250702/73ea259090a64ee98a62fc6bfd81aac3.jpg', '2025-07-02 09:21:28', 'u0701174822', '2025-07-02 09:21:28', 'u0701174822', 1);
INSERT INTO `sys_file` VALUES (9, '20250702/f7d6f06a6a4346f6b62383183a7f5051.jpg', 'Ln0og0FvVXgve59b22deee42c32b126f81a9fe918e6d.jpg', 'harry', 'MINIO', 'image/jpeg', 68577, 'https://minio.tech-harry.cn/harry/20250702/f7d6f06a6a4346f6b62383183a7f5051.jpg', '2025-07-02 09:23:03', 'u0701174822', '2025-07-02 09:23:03', 'u0701174822', 1);
INSERT INTO `sys_file` VALUES (10, '20250702/82b874ba3f104d038153521dc91055c0.png', 'kRzc1Zhb9a4f996c8b5ffa1933b553b784260ef685b4.png', 'harry', 'MINIO', 'image/png', 178567, 'https://minio.tech-harry.cn/harry/20250702/82b874ba3f104d038153521dc91055c0.png', '2025-07-02 10:39:04', 'u0702103806', '2025-07-02 10:39:04', 'u0702103806', 1);
INSERT INTO `sys_file` VALUES (11, '20250707/bec98db87fad44f1993869eedcab9962.png', 'fVZbKZWM6z6t7f18c2a935aad7cfaf6eea2d34fff2ac.png', 'harry', 'MINIO', 'image/png', 473602, 'https://minio.tech-harry.cn/harry/20250707/bec98db87fad44f1993869eedcab9962.png', '2025-07-07 16:04:57', 'u0702103806', '2025-07-07 16:04:57', 'u0702103806', 1);
INSERT INTO `sys_file` VALUES (12, '20250707/372dbdaf55694afe9934f48d41fe0279.png', 'hwblehrvrGWEe5d9045e9bff4bd2b6189b7ea8d8df61.png', 'harry', 'MINIO', 'image/png', 44823, 'https://minio.tech-harry.cn/harry/20250707/372dbdaf55694afe9934f48d41fe0279.png', '2025-07-07 16:12:42', 'u0702103806', '2025-07-07 16:12:42', 'u0702103806', 1);
INSERT INTO `sys_file` VALUES (13, '20250707/f6ba83b166eb4ba1b5824134339a9261.png', 'OB3b4ZRvr0Nz04aeb5772702b9a6a23ab0b140ad2025.png', 'harry', 'MINIO', 'image/png', 524359, 'https://minio.tech-harry.cn/harry/20250707/f6ba83b166eb4ba1b5824134339a9261.png', '2025-07-07 16:14:28', 'u0702103806', '2025-07-07 16:14:28', 'u0702103806', 1);
INSERT INTO `sys_file` VALUES (14, '20250707/2380b6f9274b41da95b82efd78f0efd5.png', '707EQ6IQZtg1ecb7f6a72d606f70e8f214a8ace3faf9.png', 'harry', 'MINIO', 'image/png', 132958, 'https://minio.tech-harry.cn/harry/20250707/2380b6f9274b41da95b82efd78f0efd5.png', '2025-07-07 16:41:22', 'u0702103806', '2025-07-07 16:41:22', 'u0702103806', 1);
INSERT INTO `sys_file` VALUES (15, '20250714/ac701b3cd7a2425f87b64a0768c934fc.png', 'ahiFA4QMedLm27d746e60cba2838470bf047b3cee851.png', 'harry', 'MINIO', 'image/png', 361331, 'https://minio.tech-harry.cn/harry/20250714/ac701b3cd7a2425f87b64a0768c934fc.png', '2025-07-14 13:37:44', 'u0702103806', '2025-07-14 13:37:44', 'u0702103806', 1);
INSERT INTO `sys_file` VALUES (16, '20250714/1572ba33b3a44c49bd8e40df05abbab1.png', 'v0uAsReli3a22721cb1cb02ea951a85da30a51b8ac54.png', 'harry', 'MINIO', 'image/png', 456056, 'https://minio.tech-harry.cn/harry/20250714/1572ba33b3a44c49bd8e40df05abbab1.png', '2025-07-14 13:38:05', 'u0702103806', '2025-07-14 13:38:05', 'u0702103806', 1);
INSERT INTO `sys_file` VALUES (17, '20250715/166d330768594a89be1c404a4c24e6e8.png', 'MPvE5HcPOrt6a17eb222b953290570838131cf294b53.png', 'harry', 'MINIO', 'image/png', 490406, 'https://minio.tech-harry.cn/harry/20250715/166d330768594a89be1c404a4c24e6e8.png', '2025-07-15 12:15:47', 'u0702103806', '2025-07-15 12:15:47', 'u0702103806', 1);
INSERT INTO `sys_file` VALUES (18, '20250716/98c3cdc1f95b431ba86cbf70413923fa.png', 'vNpyc3iAqFqD2721cb1cb02ea951a85da30a51b8ac54.png', 'harry', 'MINIO', 'image/png', 456056, 'https://minio.tech-harry.cn/harry/20250716/98c3cdc1f95b431ba86cbf70413923fa.png', '2025-07-16 19:24:17', 'u0702103806', '2025-07-16 19:24:17', 'u0702103806', 1);
INSERT INTO `sys_file` VALUES (19, '20250718/d303635bf178422d8ef94b45b70d6a2a.png', 'YicAoLIIf3I277947fb15170b563154306b99aaae77c.png', 'harry', 'MINIO', 'image/png', 498023, 'https://minio.tech-harry.cn/harry/20250718/d303635bf178422d8ef94b45b70d6a2a.png', '2025-07-18 20:48:43', 'u0702103806', '2025-07-18 20:48:43', 'u0702103806', 1);
INSERT INTO `sys_file` VALUES (20, '20250718/e69e888b21294f9f91b54d9f0ebc3e83.png', 'FgYJsVeRzX8H77947fb15170b563154306b99aaae77c.png', 'harry', 'MINIO', 'image/png', 498023, 'https://minio.tech-harry.cn/harry/20250718/e69e888b21294f9f91b54d9f0ebc3e83.png', '2025-07-18 21:00:03', 'u0702103806', '2025-07-18 21:00:03', 'u0702103806', 1);
INSERT INTO `sys_file` VALUES (21, '20250718/97b9604389c34091b869420a2179ea4a.png', 'SwRh3d5mdEKB27d746e60cba2838470bf047b3cee851.png', 'harry', 'MINIO', 'image/png', 361331, 'https://minio.tech-harry.cn/harry/20250718/97b9604389c34091b869420a2179ea4a.png', '2025-07-18 21:01:46', 'u0702103806', '2025-07-18 21:01:46', 'u0702103806', 1);

-- ----------------------------
-- Table structure for sys_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_log`;
CREATE TABLE `sys_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT '' COMMENT '模块标题',
  `method` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT '' COMMENT '请求方式',
  `operator_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '操作类别（0 其它 1 后台用户 2 移动端用户）',
  `username` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT '' COMMENT '操作人员',
  `url` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT '' COMMENT '请求URL',
  `ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT '' COMMENT '主机地址',
  `location` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT '' COMMENT '操作地点',
  `param` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT '' COMMENT '返回参数',
  `status` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT '' COMMENT '错误消息',
  `execution_time` bigint NULL DEFAULT NULL COMMENT '执行时间(ms)',
  `browser` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '浏览器',
  `browser_version` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '浏览器版本',
  `os` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '终端系统',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `valid` int NULL DEFAULT 1 COMMENT '有效状态，0:无效 1:有效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 146 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `pid` bigint NULL DEFAULT NULL COMMENT '父级菜单id',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  `permission` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图标',
  `type` int NULL DEFAULT NULL COMMENT '权限类型，0:目录 1:菜单 2:按钮（接口绑定权限）3:外链',
  `uri` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件路径',
  `sort` int NULL DEFAULT NULL COMMENT '排序',
  `keep_alive` tinyint NULL DEFAULT NULL COMMENT '【菜单】是否开启页面缓存（1-是 0-否）',
  `always_show` tinyint NULL DEFAULT NULL COMMENT '【目录】只有一个子路由是否始终显示（1-是 0-否）',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '路由地址',
  `params` json NULL COMMENT '【菜单】路由参数',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '显示状态，0:隐藏 1:显示',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `valid` int NULL DEFAULT 1 COMMENT '有效状态，0:无效 1:有效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1079 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, 0, '系统管理', '', 'system', 0, '', 1, NULL, NULL, 'system', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (2, 0, '系统监控', NULL, 'monitor', 0, NULL, 1, 1, 1, 'monitor', '[]', '1', '2025-01-06 16:31:10', 1);
INSERT INTO `sys_menu` VALUES (3, 0, '代码生成', NULL, 'code', 0, '', 1, 1, 1, 'code', '[]', '1', '2025-01-03 14:15:49', 1);
INSERT INTO `sys_menu` VALUES (5, 0, '停车管理', NULL, NULL, 0, NULL, 1, 1, 1, 'owner-park', '[]', '1', '2025-06-30 11:50:53', 1);
INSERT INTO `sys_menu` VALUES (100, 1, '用户管理', 'sys_user_page', 'user', 1, 'system/user/index', 1, 1, NULL, 'user', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (101, 1, '角色管理', 'sys_role_page', 'role', 1, 'system/role/index', 2, 1, NULL, 'role', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (102, 1, '菜单管理', 'sys_menu_page', 'menu', 1, 'system/menu/index', 3, 1, NULL, 'menu', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (103, 1, '部门管理', 'sys_dept_page', 'tree', 1, 'system/dept/index', 4, 1, NULL, 'dept', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (105, 1, '字典管理', 'sys_dict_page', 'dict', 1, 'system/dict/index', 6, 1, NULL, 'dict', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (106, 1, '字典数据', 'sys_dict_data_page', 'dict-data', 1, 'system/dict/data', 8, 1, NULL, 'dict-data', NULL, '0', '2024-12-20 08:33:50', 1);
INSERT INTO `sys_menu` VALUES (107, 1, '系统配置', 'sys_config_page', 'config', 1, 'system/config/index', 7, 1, NULL, 'config', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (108, 1, '文件管理', NULL, 'download', 1, 'system/file/index', 1, 1, 0, 'file', '[]', '1', '2025-01-10 11:23:19', 1);
INSERT INTO `sys_menu` VALUES (200, 2, '操作日志', 'sys_log_page', 'collapse', 1, 'monitor/syslog/index', 1, 1, NULL, 'syslog', '[]', '1', '2025-01-06 16:33:41', 1);
INSERT INTO `sys_menu` VALUES (300, 3, '代码生成', NULL, NULL, 1, 'codegen/index', 1, 1, 0, 'gen', NULL, '1', '2025-01-03 14:17:37', 1);
INSERT INTO `sys_menu` VALUES (999, 1, '外链', NULL, 'close_other', 3, NULL, 1, 1, 0, 'http://127.0.0.1:9000/doc.html', '[]', '1', '2024-12-20 13:25:46', 1);
INSERT INTO `sys_menu` VALUES (1001, 100, '用户查询', 'sys_user_get', '', 2, '', 1, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1002, 100, '用户新增', 'sys_user_add', '', 2, '', 2, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1003, 100, '用户修改', 'sys_user_edit', '', 2, '', 3, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1004, 100, '用户删除', 'sys_user_del', '', 2, '', 4, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1005, 100, '用户导出', 'sys_user_export', '', 2, '', 5, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1006, 100, '用户导入', 'sys_user_import', '', 2, '', 6, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1007, 100, '重置密码', 'sys_user_reset', '', 2, '', 7, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1008, 101, '角色查询', 'sys_role_get', '', 2, '', 1, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1009, 101, '角色新增', 'sys_role_add', '', 2, '', 2, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1010, 101, '角色修改', 'sys_role_edit', '', 2, '', 3, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1011, 101, '角色删除', 'sys_role_del', '', 2, '', 4, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1012, 101, '角色导出', 'sys_role_export', '', 2, '', 5, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1013, 102, '菜单查询', 'sys_menu_get', '', 2, '', 1, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1014, 102, '菜单新增', 'sys_menu_add', '', 2, '', 2, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1015, 102, '菜单修改', 'sys_menu_edit', '', 2, '', 3, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1016, 102, '菜单删除', 'sys_menu_del', '', 2, '', 4, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1017, 103, '部门查询', 'sys_dept_get', '', 2, '', 1, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1018, 103, '部门新增', 'sys_dept_add', '', 2, '', 2, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1019, 103, '部门修改', 'sys_dept_edit', '', 2, '', 3, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1020, 103, '部门删除', 'sys_dept_del', '', 2, '', 4, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1026, 105, '字典查询', 'sys_dict_get', '', 2, '', 1, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1027, 105, '字典新增', 'sys_dict_add', '', 2, '', 2, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1028, 105, '字典修改', 'sys_dict_edit', '', 2, '', 3, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1029, 105, '字典删除', 'sys_dict_del', '', 2, '', 4, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1030, 105, '字典导出', 'sys_dict_export', '', 2, '', 5, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1031, 107, '系统配置查询', 'sys_config_get', '', 2, '', 1, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1032, 107, '系统配置新增', 'sys_config_add', '', 2, '', 2, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1033, 107, '系统配置修改', 'sys_config_edit', '', 2, '', 3, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1034, 107, '系统配置删除', 'sys_config_del', '', 2, '', 4, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1035, 107, '系统配置导出', 'sys_config_export', '', 2, '', 5, NULL, NULL, '', NULL, '1', '2024-12-02 14:56:50', 1);
INSERT INTO `sys_menu` VALUES (1056, 108, '文件管理查询', 'sys_config_get', '', 2, '', 1, NULL, NULL, NULL, NULL, '1', '2025-01-10 11:27:03', 1);
INSERT INTO `sys_menu` VALUES (1057, 108, '文件管理新增', 'sys_config_add', '', 2, '', 2, NULL, NULL, NULL, NULL, '1', '2025-01-10 11:27:03', 1);
INSERT INTO `sys_menu` VALUES (1058, 108, '文件管理修改', 'sys_config_edit', '', 2, '', 3, NULL, NULL, NULL, NULL, '1', '2025-01-10 11:27:03', 1);
INSERT INTO `sys_menu` VALUES (1059, 108, '文件管理删除', 'sys_config_del', '', 2, '', 4, NULL, NULL, NULL, NULL, '1', '2025-01-10 11:27:03', 1);
INSERT INTO `sys_menu` VALUES (1061, 5, '车辆管理', NULL, NULL, 1, 'hmj/car-info/index', 2, 1, 0, 'carinfo', '[]', '1', '2025-06-30 11:52:04', 1);
INSERT INTO `sys_menu` VALUES (1062, 1061, '车辆管理查询', 't_car_info_get', '', 2, '', 1, NULL, NULL, NULL, NULL, '1', '2025-06-30 11:57:00', 1);
INSERT INTO `sys_menu` VALUES (1063, 1061, '车辆管理新增', 't_car_info_add', '', 2, '', 2, NULL, NULL, NULL, NULL, '1', '2025-06-30 11:57:00', 1);
INSERT INTO `sys_menu` VALUES (1064, 1061, '车辆管理修改', 't_car_info_edit', '', 2, '', 3, NULL, NULL, NULL, NULL, '1', '2025-06-30 11:57:00', 1);
INSERT INTO `sys_menu` VALUES (1065, 1061, '车辆管理删除', 't_car_info_del', '', 2, '', 4, NULL, NULL, NULL, NULL, '1', '2025-06-30 11:57:00', 1);
INSERT INTO `sys_menu` VALUES (1066, 5, '车位管理', NULL, NULL, 1, 'hmj/pool-park/index', 3, 1, 0, 'poolpark', '[]', '1', '2025-06-30 11:58:01', 1);
INSERT INTO `sys_menu` VALUES (1067, 1066, '车位管理查询', 't_pool_park_get', '', 2, '', 1, NULL, NULL, NULL, NULL, '1', '2025-06-30 11:58:52', 1);
INSERT INTO `sys_menu` VALUES (1068, 1066, '车位管理新增', 't_pool_park_add', '', 2, '', 2, NULL, NULL, NULL, NULL, '1', '2025-06-30 11:58:52', 1);
INSERT INTO `sys_menu` VALUES (1069, 1066, '车位管理修改', 't_pool_park_edit', '', 2, '', 3, NULL, NULL, NULL, NULL, '1', '2025-06-30 11:58:52', 1);
INSERT INTO `sys_menu` VALUES (1070, 1066, '车位管理删除', 't_pool_park_del', '', 2, '', 4, NULL, NULL, NULL, NULL, '1', '2025-06-30 11:58:52', 1);
INSERT INTO `sys_menu` VALUES (1071, 5, '充值记录', NULL, NULL, 1, 'hmj/recharge-record/index', 4, 1, 0, 'recharge', '[]', '1', '2025-06-30 12:00:25', 1);
INSERT INTO `sys_menu` VALUES (1072, 1071, '充值记录查询', 't_recharge_record_get', '', 2, '', 1, NULL, NULL, NULL, NULL, '1', '2025-06-30 12:01:14', 1);
INSERT INTO `sys_menu` VALUES (1073, 1071, '充值记录新增', 't_recharge_record_add', '', 2, '', 2, NULL, NULL, NULL, NULL, '1', '2025-06-30 12:01:14', 1);
INSERT INTO `sys_menu` VALUES (1074, 1071, '充值记录修改', 't_recharge_record_edit', '', 2, '', 3, NULL, NULL, NULL, NULL, '1', '2025-06-30 12:01:14', 1);
INSERT INTO `sys_menu` VALUES (1075, 1071, '充值记录删除', 't_recharge_record_del', '', 2, '', 4, NULL, NULL, NULL, NULL, '1', '2025-06-30 12:01:14', 1);
INSERT INTO `sys_menu` VALUES (1076, 5, '车主管理', NULL, NULL, 1, 'hmj/owner/index', 1, 1, 0, 'owner', '[]', '1', '2025-06-30 12:24:25', 1);
INSERT INTO `sys_menu` VALUES (1077, 5, '车库管理', NULL, NULL, 1, 'hmj/parking-lot/index', 1, 1, 0, 'parkinglot', '[]', '1', '2025-07-16 14:38:08', 1);
INSERT INTO `sys_menu` VALUES (1078, 5, '用户登记', NULL, NULL, 1, 'hmj/user-registration/index', 1, 1, 0, 'user_registration', '[]', '1', '2025-07-24 16:36:58', 1);

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色权限字符',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 ）',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `user_count` int NULL DEFAULT NULL COMMENT '后台用户数量',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '启用状态，0:禁用 1:启用',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `valid` int NULL DEFAULT 1 COMMENT '有效状态，0:无效 1:有效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '管理员', 'ROOT', '0', '超级管理员', 1, '2024-12-02 10:57:02', '1', 0, 1);
INSERT INTO `sys_role` VALUES (2, '测试', 'TEST', '1', '测试角色', 1, '2024-12-02 15:21:49', '1', 0, 1);
INSERT INTO `sys_role` VALUES (3, 'c角色', 'ADD', '3', NULL, NULL, '2024-12-23 17:41:40', '1', 1, 1);

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `role_id` bigint NULL DEFAULT NULL COMMENT '角色ID',
  `menu_id` bigint NULL DEFAULT NULL COMMENT '菜单ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 204 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色和菜单关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (91, 2, 1);
INSERT INTO `sys_role_menu` VALUES (92, 2, 100);
INSERT INTO `sys_role_menu` VALUES (93, 2, 1001);
INSERT INTO `sys_role_menu` VALUES (94, 2, 1002);
INSERT INTO `sys_role_menu` VALUES (95, 2, 1003);
INSERT INTO `sys_role_menu` VALUES (96, 2, 1004);
INSERT INTO `sys_role_menu` VALUES (97, 2, 1005);
INSERT INTO `sys_role_menu` VALUES (98, 2, 1006);
INSERT INTO `sys_role_menu` VALUES (99, 2, 1007);
INSERT INTO `sys_role_menu` VALUES (100, 2, 101);
INSERT INTO `sys_role_menu` VALUES (101, 2, 1008);
INSERT INTO `sys_role_menu` VALUES (102, 2, 1009);
INSERT INTO `sys_role_menu` VALUES (103, 2, 1010);
INSERT INTO `sys_role_menu` VALUES (104, 2, 1011);
INSERT INTO `sys_role_menu` VALUES (105, 2, 1012);
INSERT INTO `sys_role_menu` VALUES (106, 2, 102);
INSERT INTO `sys_role_menu` VALUES (107, 2, 1013);
INSERT INTO `sys_role_menu` VALUES (108, 2, 1014);
INSERT INTO `sys_role_menu` VALUES (109, 2, 1015);
INSERT INTO `sys_role_menu` VALUES (110, 2, 1016);
INSERT INTO `sys_role_menu` VALUES (111, 2, 103);
INSERT INTO `sys_role_menu` VALUES (112, 2, 1017);
INSERT INTO `sys_role_menu` VALUES (113, 2, 1018);
INSERT INTO `sys_role_menu` VALUES (114, 2, 1019);
INSERT INTO `sys_role_menu` VALUES (115, 2, 1020);
INSERT INTO `sys_role_menu` VALUES (116, 2, 105);
INSERT INTO `sys_role_menu` VALUES (117, 2, 1026);
INSERT INTO `sys_role_menu` VALUES (118, 2, 1027);
INSERT INTO `sys_role_menu` VALUES (119, 2, 1028);
INSERT INTO `sys_role_menu` VALUES (120, 2, 1029);
INSERT INTO `sys_role_menu` VALUES (121, 2, 1030);
INSERT INTO `sys_role_menu` VALUES (122, 2, 107);
INSERT INTO `sys_role_menu` VALUES (123, 2, 1031);
INSERT INTO `sys_role_menu` VALUES (124, 2, 1032);
INSERT INTO `sys_role_menu` VALUES (125, 2, 1033);
INSERT INTO `sys_role_menu` VALUES (126, 2, 1034);
INSERT INTO `sys_role_menu` VALUES (127, 2, 1035);
INSERT INTO `sys_role_menu` VALUES (128, 2, 106);
INSERT INTO `sys_role_menu` VALUES (167, 3, 1);
INSERT INTO `sys_role_menu` VALUES (168, 3, 100);
INSERT INTO `sys_role_menu` VALUES (169, 3, 1001);
INSERT INTO `sys_role_menu` VALUES (170, 3, 1002);
INSERT INTO `sys_role_menu` VALUES (171, 3, 1003);
INSERT INTO `sys_role_menu` VALUES (172, 3, 1004);
INSERT INTO `sys_role_menu` VALUES (173, 3, 1005);
INSERT INTO `sys_role_menu` VALUES (174, 3, 1006);
INSERT INTO `sys_role_menu` VALUES (175, 3, 1007);
INSERT INTO `sys_role_menu` VALUES (176, 3, 101);
INSERT INTO `sys_role_menu` VALUES (177, 3, 1008);
INSERT INTO `sys_role_menu` VALUES (178, 3, 1009);
INSERT INTO `sys_role_menu` VALUES (179, 3, 1010);
INSERT INTO `sys_role_menu` VALUES (180, 3, 1011);
INSERT INTO `sys_role_menu` VALUES (181, 3, 1012);
INSERT INTO `sys_role_menu` VALUES (182, 3, 102);
INSERT INTO `sys_role_menu` VALUES (183, 3, 1013);
INSERT INTO `sys_role_menu` VALUES (184, 3, 1014);
INSERT INTO `sys_role_menu` VALUES (185, 3, 1015);
INSERT INTO `sys_role_menu` VALUES (186, 3, 1016);
INSERT INTO `sys_role_menu` VALUES (187, 3, 103);
INSERT INTO `sys_role_menu` VALUES (188, 3, 1017);
INSERT INTO `sys_role_menu` VALUES (189, 3, 1018);
INSERT INTO `sys_role_menu` VALUES (190, 3, 1019);
INSERT INTO `sys_role_menu` VALUES (191, 3, 1020);
INSERT INTO `sys_role_menu` VALUES (192, 3, 105);
INSERT INTO `sys_role_menu` VALUES (193, 3, 1026);
INSERT INTO `sys_role_menu` VALUES (194, 3, 1027);
INSERT INTO `sys_role_menu` VALUES (195, 3, 1028);
INSERT INTO `sys_role_menu` VALUES (196, 3, 1029);
INSERT INTO `sys_role_menu` VALUES (197, 3, 1030);
INSERT INTO `sys_role_menu` VALUES (198, 3, 107);
INSERT INTO `sys_role_menu` VALUES (199, 3, 1031);
INSERT INTO `sys_role_menu` VALUES (200, 3, 1032);
INSERT INTO `sys_role_menu` VALUES (201, 3, 1033);
INSERT INTO `sys_role_menu` VALUES (202, 3, 1034);
INSERT INTO `sys_role_menu` VALUES (203, 3, 1035);

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户名',
  `password` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '密码',
  `dept_id` bigint NULL DEFAULT NULL COMMENT '所属部门',
  `dept_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所属部门名称',
  `post_ids` json NULL COMMENT '岗位组',
  `icon` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号',
  `nick_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '昵称',
  `sex` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '性别，0:男 1:女 2:未知',
  `note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注信息',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '启用状态，0:禁用 1:启用',
  `login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `login_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后登陆IP',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `modify_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `valid` int NULL DEFAULT 1 COMMENT '有效状态，0:无效 1:有效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 106 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 'admin', '$2a$10$lbpXb6YzzqdnIw0naHGZkOS0qMMBMUjm7IXG6UYQUmlW65DQS.vTy', 1, 'Harry科技', '[]', 'http://localhost:9090/harry/20250110/2baf828d871e490a9ea1ecf19fc9a01b.png', '<EMAIL>', '16666666666', 'Harry', '1', NULL, '1', '2024-11-20 17:25:53', '************', '2019-09-29 13:55:30', NULL, '2025-01-10 23:02:20', 'admin', 1);
INSERT INTO `sys_user` VALUES (101, 'harry', '$2a$10$Wr0wX8Ueiox9ndNXsrx8JOfe3sc6QIsTW4JW/rPR6.7iAzrdKLYSe', 2, '研发部', '[\"3\"]', '', '<EMAIL>', '17777777777', 'Harry测试', '1', NULL, '1', '2024-11-20 17:32:24', '************', '2019-09-29 13:55:30', NULL, '2024-12-02 10:59:49', 'harry', 1);
INSERT INTO `sys_user` VALUES (102, 'chanpin_1', '$2a$10$06Yz1xjErlIpXFtgC8CLC.6zpqUp2ctC9ysW3qSl/Vof2qSZbbyGa', 6, '产品2部', NULL, NULL, '<EMAIL>', '17777777777', '产品1部', '0', NULL, '1', NULL, NULL, '2024-12-27 15:11:05', NULL, '2024-12-31 09:07:02', NULL, 1);
INSERT INTO `sys_user` VALUES (104, 'u0701174822', '$2a$10$R.QBoqfzxCGqWMendUJoPejgAoV.wLHbHtjcwgTOZFcyau4qLG5VG', 3, '营销部', NULL, NULL, NULL, '16666666666', '张三', '0', NULL, '1', NULL, NULL, '2025-07-01 17:48:22', NULL, '2025-07-01 17:48:22', NULL, 1);
INSERT INTO `sys_user` VALUES (105, 'u0702103806', '$2a$10$XRcEWsG8fWD5Z2UeoTX4suies1MAk7D5YQRHK9NPr5Dq0bDZFdwX6', 3, '营销部', NULL, 'https://minio.tech-harry.cn/harry/20250714/1572ba33b3a44c49bd8e40df05abbab1.png', NULL, '13333333333', 'Harry', '1', NULL, '1', NULL, NULL, '2025-07-02 10:38:06', NULL, '2025-07-14 13:38:04', NULL, 1);

-- ----------------------------
-- Table structure for sys_user_oauth
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_oauth`;
CREATE TABLE `sys_user_oauth`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `oauth_type` int NULL DEFAULT 1 COMMENT '授权来源 1 微信小程序 2 微信 3 QQ',
  `appid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'appid',
  `openid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'openId',
  `union_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'unionId',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `modify_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `valid` int NULL DEFAULT 1 COMMENT '有效状态，0:无效 1:有效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '第三方登陆授权信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_oauth
-- ----------------------------
INSERT INTO `sys_user_oauth` VALUES (2, 104, 1, 'wx5fc6947b76779518', 'oJyGu7eE1Rq5fth5KmyFlBpQoPbs', NULL, '2025-07-01 17:48:23', NULL, '2025-07-02 09:57:39', NULL, 1);
INSERT INTO `sys_user_oauth` VALUES (3, 105, 1, 'wxead3e1f683bc1865', 'oIDNDvk44Hiw62D6pC0Bk3Mrr7CM', NULL, '2025-07-02 10:38:06', NULL, '2025-07-18 20:48:11', NULL, 1);

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `role_id` bigint NULL DEFAULT NULL COMMENT '角色ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户和角色关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 101, 2);
INSERT INTO `sys_user_role` VALUES (2, 1, 1);
INSERT INTO `sys_user_role` VALUES (27, 102, 3);
INSERT INTO `sys_user_role` VALUES (28, 103, 3);
INSERT INTO `sys_user_role` VALUES (29, 104, 3);
INSERT INTO `sys_user_role` VALUES (30, 105, 3);

-- ----------------------------
-- Table structure for t_car_info
-- ----------------------------
DROP TABLE IF EXISTS `t_car_info`;
CREATE TABLE `t_car_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `garage_id` bigint NULL DEFAULT NULL COMMENT '车库ID',
  `plate_num` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车牌号码',
  `plate_color` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '车牌颜色(1 蓝色，2 黄色，3 白色，4 黑色, 5:绿色, 6:\r\n黄绿色)',
  `card_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '注册号/卡号',
  `park_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '车位号',
  `card_type_id` bigint NULL DEFAULT NULL COMMENT '计费类型ID',
  `car_type_id` bigint NULL DEFAULT NULL COMMENT '车类型ID',
  `gname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '车辆分组',
  `pname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车主',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '手机号',
  `addr` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '地址',
  `begin_date` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '有效期开始时间（yyyy-MM-dd）',
  `end_date` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '有效期结束时间（yyyy-MM-dd）',
  `balance` double NULL DEFAULT 0 COMMENT '余额，只限于储值票车',
  `auth_pg_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '授权车库，多个以逗号隔开，例如：A车库,B车库',
  `last_in_date` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后入场时间(yyyy-MM-dd HH:mm:ss)',
  `last_out_date` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后出场时间(yyyy-MM-dd HH:mm:ss)',
  `real_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后修改人',
  `remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注',
  `io_state` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '状态(0:离场, 1:在场)',
  `create_date` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建时间（yyyy-MM-dd HH:mm:ss）',
  `last_update_date` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后修改时间（yyyy-MM-dd HH:mm:ss）',
  `sync_status` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '同步标识 0 未同步 1 已同步',
  `sync_last_date` datetime NULL DEFAULT NULL COMMENT '最后一次同步时间',
  `is_renewal` int NULL DEFAULT NULL COMMENT '是否续费 0:登记 1:续费',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_plate_num`(`plate_num` ASC) USING BTREE,
  INDEX `idx_card_no`(`card_no` ASC) USING BTREE,
  INDEX `idx_pname`(`pname` ASC) USING BTREE,
  INDEX `idx_card_type_id`(`card_type_id` ASC) USING BTREE,
  INDEX `idx_car_type_id`(`car_type_id` ASC) USING BTREE,
  INDEX `idx_io_state`(`io_state` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5294 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '车辆信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_car_info
-- ----------------------------

-- ----------------------------
-- Table structure for t_parking_lot
-- ----------------------------
DROP TABLE IF EXISTS `t_parking_lot`;
CREATE TABLE `t_parking_lot`  (
  `id` int NOT NULL COMMENT '停车场ID',
  `pname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '停车场名称',
  `parea` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所在区（道里区等）',
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '城市（哈尔滨市等）',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '详细地址',
  `lng` decimal(10, 6) NULL DEFAULT NULL COMMENT '经度',
  `lat` decimal(10, 6) NULL DEFAULT NULL COMMENT '纬度',
  `park_total_count` int NULL DEFAULT NULL COMMENT '总车位数量',
  `park_empty_count` int NULL DEFAULT NULL COMMENT '空车位数量',
  `park_empty_update_date` bigint NULL DEFAULT NULL COMMENT '空车位信息更新时间戳',
  `is_online` tinyint NULL DEFAULT NULL COMMENT '是否在线（1：在线，0：离线）',
  `out_delay` int NULL DEFAULT NULL COMMENT '出场延迟时间（分钟）',
  `comm_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '项目唯一编码',
  `status` int NULL DEFAULT NULL COMMENT '状态：1 启用 0 禁用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '停车场信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_parking_lot
-- ----------------------------
INSERT INTO `t_parking_lot` VALUES (16653, '康泰大库', '道里区', '哈尔滨市', '哈尔滨市香坊区通乡街174号', 126.543848, 45.720025, 3000, 2525, 1752576964, 1, 15, 'hKcsKOBYoYvAYThO', NULL);
INSERT INTO `t_parking_lot` VALUES (16654, '康泰小库', '道里区', '哈尔滨市', '哈尔滨市香坊区通乡街174号', 126.543848, 45.720025, 1000, 755, 1752576964, 1, 15, 'WK89kYSSqhCCvyCF', NULL);

-- ----------------------------
-- Table structure for t_pool_park
-- ----------------------------
DROP TABLE IF EXISTS `t_pool_park`;
CREATE TABLE `t_pool_park`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `garage_id` bigint NULL DEFAULT NULL COMMENT '车库ID',
  `pool_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '所属车位池',
  `pgname` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '所属车库名称',
  `park_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车位名称、车位号',
  `park_type` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '车位类型(0:公用车位, 1:私家车位)',
  `begin_date` bigint NULL DEFAULT NULL COMMENT '有效期开始时间（时间戳，单位:秒）',
  `end_date` bigint NULL DEFAULT NULL COMMENT '有效期结束时间（时间戳，单位:秒）',
  `create_date` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建时间（yyyy-MM-dd HH:mm:ss）',
  `last_update_date` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后修改时间（yyyy-MM-dd HH:mm:ss）',
  `charge_type` tinyint NULL DEFAULT NULL COMMENT '车位满缴费方式(1：先出收费，2：后进收费)',
  `full_mode` tinyint NULL DEFAULT NULL COMMENT '车位池满统计规则（1：按停车场统计 2：按车库统计）',
  `total_num` int NULL DEFAULT NULL COMMENT '总车位数',
  `valid_num` int NULL DEFAULT NULL COMMENT '有效车位数',
  `parked_num` int NULL DEFAULT NULL COMMENT '在场车辆数',
  `empty_num` int NULL DEFAULT NULL COMMENT '剩余车位数',
  `plate_nums` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '绑定的车牌号',
  `hourse_addr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所属楼栋单元房铺',
  `pname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '管理员姓名',
  `mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '管理员电话',
  `free_time` int NULL DEFAULT NULL COMMENT '免费换车时间(单位分钟)',
  `sync_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '同步标识 0 未同步 1 已同步',
  `sync_last_date` datetime NULL DEFAULT NULL COMMENT '最后一次同步时间',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '状态：0 空闲 1 占用',
  `user_id` int NULL DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_pool_name`(`pool_name` ASC) USING BTREE,
  INDEX `idx_pgname`(`pgname` ASC) USING BTREE,
  INDEX `idx_park_name`(`park_name` ASC) USING BTREE,
  INDEX `idx_park_type`(`park_type` ASC) USING BTREE,
  INDEX `idx_date_range`(`begin_date` ASC, `end_date` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5730 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '车位信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_pool_park
-- ----------------------------

-- ----------------------------
-- Table structure for t_pool_park_plate_nums
-- ----------------------------
DROP TABLE IF EXISTS `t_pool_park_plate_nums`;
CREATE TABLE `t_pool_park_plate_nums`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `garage_id` bigint NULL DEFAULT NULL COMMENT '车库ID',
  `pool_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '所属车位池',
  `charge_type` tinyint NULL DEFAULT NULL COMMENT '车位满缴费方式(1：先出收费，2：后进收费)',
  `full_mode` tinyint NULL DEFAULT NULL COMMENT '车位池满统计规则（1：按停车场统计 2：按车库统计）',
  `total_num` int NULL DEFAULT NULL COMMENT '总车位数',
  `valid_num` int NULL DEFAULT NULL COMMENT '有效车位数',
  `parked_num` int NULL DEFAULT NULL COMMENT '在场车辆数',
  `empty_num` int NULL DEFAULT NULL COMMENT '剩余车位数',
  `plate_nums` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '绑定的车牌号',
  `hourse_addr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所属楼栋单元房铺',
  `pname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '管理员姓名',
  `mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '管理员电话',
  `free_time` int NULL DEFAULT NULL COMMENT '免费换车时间(单位分钟)',
  `sync_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '同步标识 0 未同步 1 已同步',
  `sync_last_date` datetime NULL DEFAULT NULL COMMENT '最后一次同步时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3925 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '车位池关联车位车牌信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_pool_park_plate_nums
-- ----------------------------

-- ----------------------------
-- Table structure for t_recharge_record
-- ----------------------------
DROP TABLE IF EXISTS `t_recharge_record`;
CREATE TABLE `t_recharge_record`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `garage_id` bigint NULL DEFAULT NULL COMMENT '车库ID',
  `garage_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车库名称',
  `registration_id` int NULL DEFAULT NULL COMMENT '登记ID',
  `park_id` int NULL DEFAULT NULL COMMENT '车位ID',
  `is_renewal` int NULL DEFAULT NULL COMMENT '是否续费 0:登记 1:续费',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `pname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车主',
  `openid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'openid',
  `phone` char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `plate_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车牌号',
  `park_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车位号',
  `sub_mchid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '二级商户号',
  `recharge_sn` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '充值流水号',
  `recharge_amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '充值金额',
  `recharge_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '充值类型',
  `trade_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信系统中的交易流水号',
  `pay_status` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付状态 1.待支付 2.已支付 3.已取消',
  `time_end` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付完成时间 yyyyMMddHHmmss',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `modify_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `valid` int NULL DEFAULT 1 COMMENT '有效状态，0:无效 1:有效',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `recharge_sn_idx`(`recharge_sn` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '充值记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_recharge_record
-- ----------------------------

-- ----------------------------
-- Table structure for t_user_registration
-- ----------------------------
DROP TABLE IF EXISTS `t_user_registration`;
CREATE TABLE `t_user_registration`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `garage_id` bigint NULL DEFAULT NULL COMMENT '车库ID',
  `pname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车主',
  `mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号',
  `addr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '地址',
  `owner_certificate` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '业主凭证',
  `plate_num` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车牌号码',
  `park_id` bigint NULL DEFAULT NULL COMMENT '车位ID',
  `park_num` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车位号',
  `billing_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '计费类型 1.日租 2.月租 3.季租 4.年租',
  `begin_date` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '开始时间（yyyy-MM-dd）',
  `end_date` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '结束时间（yyyy-MM-dd）',
  `should_pay` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '应缴费用',
  `user_id` int NULL DEFAULT NULL COMMENT '用户ID',
  `openid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'openid',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态',
  `is_renewal` int NULL DEFAULT NULL COMMENT '是否续费 0:登记 1:续费',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `modify_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `valid` int NULL DEFAULT 1 COMMENT '有效状态，0:无效 1:有效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户信息登记表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_user_registration
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
