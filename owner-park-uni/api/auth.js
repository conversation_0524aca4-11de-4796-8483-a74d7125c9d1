import {http} from '@/utils/request.js'

const appid = 'wxead3e1f683bc1865'
const AuthAPI = {
    /**
     * 小程序登录换取token
     * @param {*} code
     * @returns
     */
    miniLogin: (code) => {
        return http.request({
            url: `/auth/miniapp/${appid}/login?code=${code}`,
            method: 'POST'
        })
    },
    /**
     * 用户详情
     */
    userInfo: () => {
        return http.request({
            url: "/auth/info",
            method: 'GET'
        })
    },

    /**
     * 修改头像
     * @param {*} data
     * @returns
     */
    updateAvatar: (data) => {
        return http.request({
            url: `/sys/user/profile/avatar`,
            method: 'POST',
            data
        })
    },
    /**
     *  修改用户信息
     * @param {*} data
     * @returns
     */
    updateProfile: (data) => {
        return http.request({
            url: `/sys/user/profile`,
            method: 'PUT',
            data
        })
    },
    /**
     *  修改密码
     * @param {*} data
     * @returns
     */
    updatePassword: (data) => {
        return http.request({
            url: `/sys/user/profile/updatePassword`,
            method: 'PUT',
            data
        })
    }
}
export default AuthAPI