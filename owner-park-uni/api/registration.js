import {
    http
} from '@/utils/request.js'

const RegistrationAPI = {
    /**
     * 提交用户登记信息
     * @param {Object} data 登记信息
     */
    submitRegistration: (data) => {
        return http.request({
            url: '/hmj/registration/add',
            method: 'POST',
            data: data
        })
    },
    /**
     * 获取用户登记信息详情
     */
    getRegistrationDetail: (id) => {
        return http.request({
            url: '/hmj/registration/detail/' + id,
            method: 'GET'
        })
    },

    /**
     * 计算费用
     * @param {Object} data 计费参数
     */
    calculateFee: (data) => {
        return http.request({
            url: '/hmj/registration/calculate-fee',
            method: 'POST',
            data: data
        })
    },

    /**
     * 获取可用车位列表
     */
    getAvailableParkingSpots: (data) => {
        return http.request({
            url: '/hmj/registration/select-parking-spot',
            method: 'POST',
            data: data
        })
    },
    /**
     * 创建缴费订单
     */
    createPaymentOrder: (data) => {
        return http.request({
            url: '/applet/wxpay/createPaymentOrder',
            method: 'POST',
            data: data
        })
    },
    /**
     * 获取车库
     */
    getGarageList: () => {
        return http.request({
            url: '/hmj/parkingLot/garage',
            method: 'GET'
        })
    }
}

export default RegistrationAPI