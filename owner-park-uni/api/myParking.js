import {
	http
} from '@/utils/request.js'

const MyParkingAPI = {
  /**
   * 获取我的车位列表
   */
  getMyParkingList() {
   	return http.request({
      url: '/hmj/my-parking/list',
      method: 'GET'
    })
  },

  /**
   * 添加车辆到车位
   * @param {Object} data - 车辆信息
   * @param {string} data.parkingId - 车位ID
   * @param {string} data.plateNumber - 车牌号
   * @param {string} data.carModel - 车型
   * @param {string} data.ownerName - 车主姓名
   * @param {string} data.ownerPhone - 车主电话
   */
  addCarToParking(data) {
   	return http.request({
      url: '/hmj/my-parking/add-car',
      method: 'POST',
      data
    })
  },

  /**
   * 从车位移除车辆
   * @param {string} data - 车辆ID
   */
  removeCarFromParking(data) {
   	return http.request({
      url: `/hmj/my-parking/delete-car`,
      method: 'DELETE',
        data
    })
  },

}

export default MyParkingAPI
