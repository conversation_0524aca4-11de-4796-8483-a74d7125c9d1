import baseUrl from "@/utils/global.js"
const UploadAPI = {

    uploadFile: (formData) => {
		return  new Promise((resolve, reject) => {
			uni.uploadFile({
				url: baseUrl+'/oss',
				filePath: formData,
				name: 'file',
				header: { //（根据自身接口要求选择）
					'Authorization': uni.getStorageSync('token'),
                    "Content-Type": "multipart/form-data",
				},
				success: res => {
                    console.log(JSON.stringify(res))
					const imgRes = JSON.parse(res.data)
					if (imgRes.code == 200) {
						resolve(imgRes)
					} else {
						reject('上传失败');
					}
				},
				fail: () => {
					reject('网络错误');
				}
			});
		})
	},
}
export default UploadAPI