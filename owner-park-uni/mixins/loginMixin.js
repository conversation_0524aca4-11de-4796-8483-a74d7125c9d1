import loginManager from '@/utils/loginManager.js'

/**
 * 登录混入
 * 为页面提供登录相关的方法
 */
export default {
  data() {
    return {
      isPageLoading: false
    }
  },
  
  methods: {
    /**
     * 确保登录后执行回调
     * @param {Function} callback 登录成功后的回调函数
     */
    async ensureLoginAndExecute(callback) {
      if (this.isPageLoading) {
        return
      }
      
      this.isPageLoading = true
      
      try {
        await loginManager.ensureLogin()
        if (typeof callback === 'function') {
          await callback()
        }
      } catch (error) {
        console.error('登录失败:', error)
        uni.showToast({
          title: '登录失败，请重试',
          icon: 'error'
        })
      } finally {
        this.isPageLoading = false
      }
    },
    
    /**
     * 检查登录状态
     */
    checkLogin() {
      return loginManager.checkLoginStatus()
    },
    
    /**
     * 重新登录
     */
    async reLogin() {
      try {
        await loginManager.reLogin()
        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })
        return true
      } catch (error) {
        console.error('重新登录失败:', error)
        uni.showToast({
          title: '登录失败',
          icon: 'error'
        })
        return false
      }
    }
  }
}
