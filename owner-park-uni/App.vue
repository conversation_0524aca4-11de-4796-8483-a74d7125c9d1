<script setup>
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app';
import loginManager from '@/utils/loginManager.js';

onLaunch(() => {
	console.log('App Launch');
	// 应用启动时检查登录状态
	initApp();
});

onShow(() => {
	console.log('App Show');
	// 应用从后台进入前台时，检查登录状态
	checkLoginStatus();
});

onHide(() => {
	console.log('App Hide');
});

const initApp = async () => {
	try {
		// 检查是否已有token
		const hasToken = loginManager.checkLoginStatus();
		console.log('应用启动，当前登录状态:', hasToken);

		if (!hasToken) {
			console.log('未登录，将在首次API调用时自动登录');
		}
	} catch (error) {
		console.error('应用初始化失败:', error);
	}
};

const checkLoginStatus = () => {
	const hasToken = loginManager.checkLoginStatus();
	console.log('应用激活，当前登录状态:', hasToken);
};
</script>


<style lang="scss">
	/*每个页面公共css */
	@import '@/uni_modules/uni-scss/index.scss';
</style>