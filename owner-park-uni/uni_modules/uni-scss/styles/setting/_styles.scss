/* #ifndef APP-NVUE */

$-color-white:#fff;
$-color-black:#000;
@mixin base-style($color) {
	color: #fff;
	background-color: $color;
	border-color: mix($-color-black, $color, 8%);
	&:not([hover-class]):active {
		background: mix($-color-black, $color, 10%);
		border-color: mix($-color-black, $color, 20%);
		color: $-color-white;
		outline: none;
	}
}
@mixin is-color($color) {
	@include base-style($color);
	&[loading] {
		@include base-style($color);
		&::before {
			margin-right:5px;
		}
	}
	&[disabled] {
	  &,
		&[loading],
	  &:not([hover-class]):active {
	    color: $-color-white;
			border-color: mix(darken($color,10%), $-color-white);
	    background-color: mix($color, $-color-white);
	  }
	}

}
@mixin base-plain-style($color) {
	color:$color;
	background-color: mix($-color-white, $color, 90%);
	border-color: mix($-color-white, $color, 70%);
	&:not([hover-class]):active {
	  background: mix($-color-white, $color, 80%);
	  color: $color;
	  outline: none;
		border-color: mix($-color-white, $color, 50%);
	}
}
@mixin is-plain($color){
	&[plain] {
		@include base-plain-style($color);
		&[loading] {
			@include base-plain-style($color);
			&::before {
				margin-right:5px;
			}
		}
		&[disabled] {
		  &,
		  &:active {
		    color: mix($-color-white, $color, 40%);
		    background-color: mix($-color-white, $color, 90%);
				border-color: mix($-color-white, $color, 80%);
		  }
		}
	}
}


.uni-btn {
	margin: 5px;
	color: #393939;
	border:1px solid #ccc;
	font-size: 16px;
	font-weight: 200;
	background-color: #F9F9F9;
	// TODO 暂时处理边框隐藏一边的问题
	overflow: visible;
	&::after{
		border: none;
	}

	&:not([type]),&[type=default] {
		color: #999;
		&[loading] {
			background: none;
			&::before {
				margin-right:5px;
			}
		}



		&[disabled]{
			color: mix($-color-white, #999, 60%);
		  &,
			&[loading],
		  &:active {
				color: mix($-color-white, #999, 60%);
		    background-color: mix($-color-white,$-color-black , 98%);
				border-color: mix($-color-white,  #999, 85%);
		  }
		}

		&[plain] {
			color: #999;
			background: none;
			border-color: $uni-border-1;
			&:not([hover-class]):active {
				background: none;
			  color: mix($-color-white, $-color-black, 80%);
				border-color: mix($-color-white, $-color-black, 90%);
			  outline: none;
			}
			&[disabled]{
			  &,
				&[loading],
			  &:active {
			    background: none;
					color: mix($-color-white, #999, 60%);
					border-color: mix($-color-white,  #999, 85%);
			  }
			}
		}
	}

	&:not([hover-class]):active {
	  color: mix($-color-white, $-color-black, 50%);
	}

	&[size=mini] {
		font-size: 16px;
		font-weight: 200;
		border-radius: 8px;
	}



	&.uni-btn-small {
		font-size: 14px;
	}
	&.uni-btn-mini {
		font-size: 12px;
	}

	&.uni-btn-radius {
		border-radius: 999px;
	}
	&[type=primary] {
		@include is-color($uni-primary);
		@include is-plain($uni-primary)
	}
	&[type=success] {
		@include is-color($uni-success);
		@include is-plain($uni-success)
	}
	&[type=error] {
		@include is-color($uni-error);
		@include is-plain($uni-error)
	}
	&[type=warning] {
		@include is-color($uni-warning);
		@include is-plain($uni-warning)
	}
	&[type=info] {
		@include is-color($uni-info);
		@include is-plain($uni-info)
	}
}
/* #endif */
