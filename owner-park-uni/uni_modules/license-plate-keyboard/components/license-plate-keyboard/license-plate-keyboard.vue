<template>
  <view class="license-plate-container">
    <!-- 车牌号展示区域 -->
    <view class="plate-display">
      <view 
        v-for="(item, index) in plateChars" 
        :key="index" 
        class="plate-char"
        :class="{ active: currentIndex === index, 'new-energy': index === 7 }"
        @tap="selectChar(index)"
      >
        <text>{{ item || ' ' }}</text>
      </view>
    </view>
    
    <!-- 键盘区域 -->
    <view class="keyboard" v-if="showKeyboard">
      <view class="keyboard-header">
        <text class="keyboard-title">{{ getKeyboardTitle() }}</text>
        <text class="keyboard-close" @tap="hideKeyboard">完成</text>
      </view>
      
      <view class="keyboard-body">
        <!-- 省份选择键盘 -->
        <view class="keyboard-province" v-if="currentIndex === 0">
          <view 
            v-for="(province, index) in provinces" 
            :key="index" 
            class="keyboard-key province-key"
            @tap="inputChar(province)"
          >
            <text>{{ province }}</text>
          </view>
        </view>
        
        <!-- 字母键盘 -->
        <view class="keyboard-letters" v-else-if="currentIndex === 1">
          <view 
            v-for="(letter, index) in letters" 
            :key="index" 
            class="keyboard-key letter-key"
            @tap="inputChar(letter)"
          >
            <text>{{ letter }}</text>
          </view>
        </view>
        
        <!-- 字母和数字键盘 -->
        <view class="keyboard-alphanumeric" v-else>
          <view 
            v-for="(char, index) in alphanumeric" 
            :key="index" 
            class="keyboard-key alphanumeric-key"
            :class="{ disabled: isCharDisabled(char) }"
            @tap="isCharDisabled(char) ? null : inputChar(char)"
          >
            <text>{{ char }}</text>
          </view>
        </view>
        
        <!-- 底部操作按钮 -->
        <view class="keyboard-actions">
          <view class="keyboard-key action-key" @tap="prevChar">
            <text>上一项</text>
          </view>
          <view class="keyboard-key action-key" @tap="deleteChar">
            <text>删除</text>
          </view>
          <view class="keyboard-key action-key" @tap="nextChar">
            <text>下一项</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 遮罩层 -->
    <view class="keyboard-mask" v-if="showKeyboard" @tap="hideKeyboard"></view>
  </view>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

// 定义组件的 props
const props = defineProps({
  value: {
    type: String,
    default: ''
  },
  modelValue: {
    type: String,
    default: ''
  }
});

// 定义组件的 emits
const emit = defineEmits(['update:modelValue', 'change']);

// 各种键盘字符集
const provinces = ['京', '津', '冀', '晋', '蒙', '辽', '吉', '黑', '沪', '苏', '浙', '皖', '闽', '赣', '鲁', '豫', '鄂', '湘', '粤', '桂', '琼', '渝', '川', '贵', '云', '藏', '陕', '甘', '青', '宁', '新', '港', '澳', '台'];
const letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
const numbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

// 字母数字组合键盘
const alphanumeric = computed(() => {
  return [...letters, ...numbers];
});

// 是否为新能源车牌 - 自动检测 - 第8位有值则为新能源车牌
const isNewEnergy = computed(() => {
  return plateChars.value[7] !== '';
});

// 车牌号字符数组
const plateChars = ref(Array(8).fill(''));

// 当前选中的位置
const currentIndex = ref(0);

// 是否显示键盘
const showKeyboard = ref(false);

// 从外部传入的值初始化
const initFromValue = () => {
  // 优先使用 modelValue，兼容 v-model
  const initialValue = props.modelValue || props.value || '';
  const chars = initialValue.split('');
  
  // 填充车牌字符
  plateChars.value = Array(8).fill('');
  chars.forEach((char, index) => {
    if (index < plateChars.value.length) {
      plateChars.value[index] = char;
    }
  });
};

// 监听外部 value 变化
watch(() => props.modelValue || props.value, () => {
  initFromValue();
}, { immediate: true });

// 更新 modelValue
const updateValue = () => {
  // 过滤掉末尾的空字符
  let filteredChars = [...plateChars.value];
  while (filteredChars.length > 0 && filteredChars[filteredChars.length - 1] === '') {
    filteredChars.pop();
  }
  const plateValue = filteredChars.join('');
  emit('update:modelValue', plateValue);
  emit('change', plateValue);
};

// 获取键盘标题
const getKeyboardTitle = () => {
  if (currentIndex.value === 0) {
    return '请选择省份';
  } else if (currentIndex.value === 1) {
    return '请选择字母';
  } else {
    return '请选择字母或数字';
  }
};

// 判断字符是否禁用
const isCharDisabled = (char) => {
  // 第二位禁用字母 I 和 O
  if (currentIndex.value === 1 && (char === 'I' || char === 'O')) {
    return true;
  }
  
  // 最后一位新能源车牌只能是数字 和 字母
  if (currentIndex.value === 7 && !numbers.includes(char) && !letters.includes(char)) {
    return true;
  }
  
  return false;
};

// 选择车牌字符位置
const selectChar = (index) => {
  currentIndex.value = index;
  showKeyboard.value = true;
};

// 输入字符
const inputChar = (char) => {
  plateChars.value[currentIndex.value] = char;
  
  // 自动跳转到下一个位置
  nextChar();
  
  // 更新车牌值
  updateValue();
};

// 删除字符
const deleteChar = () => {
  plateChars.value[currentIndex.value] = '';
  
  // 更新车牌值
  updateValue();
};

// 前一个字符
const prevChar = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--;
  }
};

// 下一个字符
const nextChar = () => {
  if (currentIndex.value < 7) {
    currentIndex.value++;
  } else {
    hideKeyboard();
  }
};

// 隐藏键盘
const hideKeyboard = () => {
  showKeyboard.value = false;
};

// 暴露方法给外部
defineExpose({
  focus: () => {
    showKeyboard.value = true;
  },
  blur: () => {
    showKeyboard.value = false;
  }
});
</script>

<style>
.license-plate-container {
  width: 100%;
  position: relative;
}

.plate-display {
  display: flex;
  justify-content: center;
  padding: 20rpx 0;
}

.plate-char {
  width: 12%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  margin: 0 2rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.plate-char.active {
  background-color: #ddeeff;
  border-color: #007AFF;
}

.plate-char.new-energy {
  background-color: #eaffea;
  border-color: #16C60C;
}

.keyboard {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  background-color: #d1d5db;
  z-index: 999;
  padding-bottom: env(safe-area-inset-bottom);
}

.keyboard-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 998;
}

.keyboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.keyboard-title {
  font-size: 28rpx;
  color: #333;
}

.keyboard-close {
  font-size: 28rpx;
  color: #007AFF;
}

.keyboard-body {
  padding: 10rpx;
}

.keyboard-province,
.keyboard-letters,
.keyboard-alphanumeric {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.keyboard-key {
  width: 64rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #fff;
  margin: 5rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.province-key {
  width: 70rpx;
}

.keyboard-key.disabled {
  color: #ccc;
  background-color: #f5f5f5;
}

.keyboard-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 10rpx;
}

.action-key {
  width: 30%;
  font-size: 28rpx;
  color: #007AFF;
  background-color: #f5f5f5;
}
</style> 