# 车牌号输入键盘组件

## 介绍

一个专门为中国车牌号设计的输入键盘组件，支持普通车牌（7位）和新能源车牌（8位）的输入。

## 功能特点

- 支持普通车牌和新能源车牌的切换
- 根据输入位置自动切换键盘布局（省份、字母、字母数字混合）
- 符合车牌号输入规则，自动禁用不合法字符
- 支持删除、前一项、后一项等操作
- 美观的UI设计，符合中国车牌样式

## 使用方法

### 基本用法

```html
<template>
  <view>
    <license-plate-keyboard v-model="plateNumber" @change="onPlateChange" />
  </view>
</template>

<script setup>
import { ref } from 'vue';

const plateNumber = ref('');

const onPlateChange = (value) => {
  console.log('车牌号变更为：', value);
};
</script>
```

### 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| modelValue | String | '' | 绑定的车牌号值（推荐使用v-model） |
| value | String | '' | 绑定的车牌号值（兼容写法） |

### 事件说明

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| update:modelValue | 车牌号更新时触发 | 更新后的车牌号 |
| change | 车牌号变更时触发 | 更新后的车牌号 |

### 方法说明

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| focus | 主动弹出键盘 | 无 |
| blur | 主动隐藏键盘 | 无 |

## 注意事项

1. 车牌规则依据中国大陆现行车牌规则设计
2. 组件会根据输入内容长度自动判断是否为新能源车牌
3. 普通车牌为7位，新能源车牌为8位 