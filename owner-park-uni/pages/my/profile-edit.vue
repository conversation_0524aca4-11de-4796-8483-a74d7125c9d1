<template>
  <view class="container">
    <!-- 导航栏 -->
    <view class="navbar">
      <view class="nav-left" @tap="goBack">
        <uni-icons type="arrowleft" size="20" color="#333"></uni-icons>
      </view>
      <view class="nav-title">编辑资料</view>
      <view class="nav-right" @tap="saveProfile">
        <text class="save-btn">保存</text>
      </view>
    </view>

    <!-- 头像编辑区域 -->
    <view class="avatar-section">
      <view class="avatar-container" @tap="chooseAvatar">
        <image class="avatar" 
               :src="formData.icon || 'https://img.bhlinka.com/cw/images/20250516/1747357873085_wechat_2025-05-16_091059_475.png'" 
               mode="aspectFill"></image>
        <view class="avatar-overlay">
          <uni-icons type="camera" size="24" color="#fff"></uni-icons>
          <text class="overlay-text">更换头像</text>
        </view>
      </view>
    </view>

    <!-- 表单区域 -->
    <view class="form-section">
      <uni-forms ref="profileForm" :model="formData" :rules="formRules">
        <!-- 昵称 -->
        <view class="form-item">
          <view class="item-label">昵称</view>
          <view class="item-content">
            <uni-easyinput 
              v-model="formData.nickName" 
              placeholder="请输入昵称"
              :clearable="true"
              maxlength="20"
            />
          </view>
        </view>

        <!-- 手机号 -->
        <view class="form-item">
          <view class="item-label">手机号</view>
          <view class="item-content">
            <uni-easyinput 
              v-model="formData.phone" 
              placeholder="请输入手机号"
              type="number"
              maxlength="11"
              :clearable="true"
            />
          </view>
        </view>

        <!-- 性别 -->
        <view class="form-item" @tap="showGenderPicker">
          <view class="item-label">性别</view>
          <view class="item-content">
            <view class="picker-input">
              <text class="picker-text" :class="{ 'placeholder': !genderText }">
                {{ genderText || '请选择性别' }}
              </text>
              <uni-icons type="arrowdown" size="16" color="#999"></uni-icons>
            </view>
          </view>
        </view>
      </uni-forms>
    </view>

    <!-- 性别选择器 -->
    <uni-popup ref="genderPopup" type="bottom">
      <view class="picker-popup">
        <view class="picker-header">
          <text class="picker-cancel" @tap="cancelGenderPicker">取消</text>
          <text class="picker-title">选择性别</text>
          <text class="picker-confirm" @tap="confirmGenderPicker">确定</text>
        </view>
        <picker-view class="picker-view" :value="[genderIndex]" @change="onGenderChange">
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in genderOptions" :key="index">
              {{ item.label }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>

    <!-- 加载提示 -->
    <uni-load-more v-if="loading" status="loading" content-text="保存中..."></uni-load-more>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import AuthAPI from '@/api/auth.js';
import UploadAPI from '@/api/upload.js';

// 表单数据
const formData = reactive({
  icon: '',
  nickName: '',
  phone: '',
  sex: ''
});

// 表单验证规则
const formRules = reactive({
  nickName: {
    rules: [
      { required: true, errorMessage: '请输入昵称' },
      { minLength: 1, maxLength: 20, errorMessage: '昵称长度为1-20个字符' }
    ]
  },
  phone: {
    rules: [
      { pattern: /^1[3-9]\d{9}$/, errorMessage: '请输入正确的手机号' }
    ]
  }
});

// 性别选项
const genderOptions = [
  { label: '男', value: '1' },
  { label: '女', value: '2' },
  { label: '未知', value: '0' }
];

const genderIndex = ref(0);
const loading = ref(false);
const profileForm = ref(null);
const genderPopup = ref(null);

// 计算性别显示文本
const genderText = computed(() => {
  const option = genderOptions.find(item => item.value === formData.sex);
  return option ? option.label : '';
});

// 获取用户信息
const getUserInfo = async () => {
  try {
    const res = await AuthAPI.userInfo();
    if (res && res.data) {
      const { userInfo } = res.data;
      Object.assign(formData, {
        icon: userInfo.icon || '',
        nickName: userInfo.nickName || '',
        phone: userInfo.phone || '',
        sex: userInfo.sex || '0'
      });
      
      // 设置性别选择器的初始值
      const index = genderOptions.findIndex(item => item.value === formData.sex);
      genderIndex.value = index >= 0 ? index : 0;
    }
  } catch (e) {
    console.error('获取用户信息失败：', e);
    uni.showToast({
      title: '获取用户信息失败',
      icon: 'none'
    });
  }
};

// 选择头像
const chooseAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: async (res) => {
      const tempFilePath = res.tempFilePaths[0];
      
      // 显示加载提示
      uni.showLoading({
        title: '上传中...'
      });
      
      try {
        // 上传图片
        const uploadRes = await UploadAPI.uploadFile(tempFilePath);
        if (uploadRes && uploadRes.data) {
          // 更新头像
          await AuthAPI.updateAvatar({ icon: uploadRes.data });
          formData.icon = uploadRes.data;
          
          uni.showToast({
            title: '头像更新成功',
            icon: 'success'
          });
        }
      } catch (error) {
        console.error('头像上传失败：', error);
        uni.showToast({
          title: '头像上传失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    }
  });
};

// 显示性别选择器
const showGenderPicker = () => {
  genderPopup.value.open();
};

// 性别选择变化
const onGenderChange = (e) => {
  genderIndex.value = e.detail.value[0];
};

// 取消性别选择
const cancelGenderPicker = () => {
  genderPopup.value.close();
};

// 确认性别选择
const confirmGenderPicker = () => {
  formData.sex = genderOptions[genderIndex.value].value;
  genderPopup.value.close();
};

// 保存资料
const saveProfile = async () => {
  try {
    // 表单验证
    const valid = await profileForm.value.validate();
    if (!valid) return;
    
    loading.value = true;
    
    // 调用更新接口
    await AuthAPI.updateProfile({
      nickName: formData.nickName,
      phone: formData.phone,
      sex: formData.sex
    });
    
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    });
    
    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
    
  } catch (error) {
    console.error('保存失败：', error);
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

onMounted(() => {
  getUserInfo();
});
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: #f5f5f5;
}

// 导航栏
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 30rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-left, .nav-right {
  width: 120rpx;
}

.nav-left {
  display: flex;
  align-items: center;
}

.nav-right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.save-btn {
  font-size: 28rpx;
  color: #007AFF;
  font-weight: 500;
}

// 头像区域
.avatar-section {
  display: flex;
  justify-content: center;
  padding: 60rpx 0;
  background: #fff;
  margin-bottom: 20rpx;
}

.avatar-container {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  overflow: hidden;
}

.avatar {
  width: 100%;
  height: 100%;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.avatar-container:active .avatar-overlay {
  opacity: 1;
}

.overlay-text {
  font-size: 20rpx;
  color: #fff;
  margin-top: 8rpx;
}

// 表单区域
.form-section {
  background: #fff;
  margin-bottom: 20rpx;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
}

.item-label {
  width: 140rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.item-content {
  flex: 1;
}

.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60rpx;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-text.placeholder {
  color: #999;
}

// 性别选择器
.picker-popup {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #eee;
}

.picker-cancel, .picker-confirm {
  font-size: 28rpx;
  color: #007AFF;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.picker-view {
  height: 400rpx;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
}
</style>
