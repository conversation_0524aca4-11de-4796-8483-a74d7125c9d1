<template>
  <view class="container">
    <!-- 头部用户信息区域 -->
    <view class="header-section">
      <view class="header-bg">
        <view class="bg-decoration"></view>
        <view class="bg-circles">
          <view class="circle circle-1"></view>
          <view class="circle circle-2"></view>
          <view class="circle circle-3"></view>
        </view>
      </view>

      <view class="user-card">
        <view class="user-info">
          <view class="avatar-container">
            <image class="avatar"
                   :src="userInfo.icon || 'https://img.bhlinka.com/cw/images/20250516/1747357873085_wechat_2025-05-16_091059_475.png'"
                   mode="aspectFill"></image>
          </view>
          <view class="user-details">
            <view class="user-name">{{ userInfo.nickName || '停车用户' }}</view>
            <view class="user-phone">{{ userInfo.phone || '未绑定手机号' }}</view>
            <view class="user-level">
              <uni-icons type="star-filled" size="14" color="#FFD700"></uni-icons>
              <text class="level-text">VIP会员</text>
            </view>
          </view>
          <view class="edit-btn" @tap="editProfile">
            <uni-icons type="compose" size="18" color="#FFFFFF"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 快捷功能区域 -->
    <view class="quick-actions">
      <view class="section-header">
        <text class="section-title">常用功能</text>
        <text class="section-subtitle">常用操作一键直达</text>
      </view>

      <view class="action-grid">
        <view class="action-item" @tap="navigateTo('/pages/my/parking')">
          <view class="action-icon action-icon-car">
            <uni-icons type="home" size="24" color="#fff"></uni-icons>
          </view>
          <text class="action-text">车辆管理</text>
        </view>

		 <view class="action-item" @tap="navigateTo('/pages/my/payment-record')">
		   <view class="action-icon action-icon-record">
			 <uni-icons type="list" size="24" color="#fff"></uni-icons>
		   </view>
		   <text class="action-text">缴费记录</text>
		 </view>
  
      </view>
    </view>

    <!-- 服务菜单 -->
    <view class="service-section">
      <view class="section-header">
        <text class="section-title">更多服务</text>
        <text class="section-subtitle">为您提供全方位服务</text>
      </view>

      <view class="service-list">
        <view class="service-item" @tap="navigateTo('/pages/help/help')">
          <view class="service-icon service-icon-help">
            <uni-icons type="help" size="20" color="#fff"></uni-icons>
          </view>
          <view class="service-content">
            <text class="service-title">帮助与反馈</text>
            <text class="service-desc">使用帮助、问题反馈</text>
          </view>
          <view class="service-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>

        <view class="service-item" @tap="showContact">
          <view class="service-icon service-icon-contact">
            <uni-icons type="phone" size="20" color="#fff"></uni-icons>
          </view>
          <view class="service-content">
            <text class="service-title">联系客服</text>
            <text class="service-desc">在线客服、电话咨询</text>
          </view>
          <view class="service-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>

        <view class="service-item" @tap="showAbout">
          <view class="service-icon service-icon-about">
            <uni-icons type="info" size="20" color="#fff"></uni-icons>
          </view>
          <view class="service-content">
            <text class="service-title">关于我们</text>
            <text class="service-desc">版本信息、使用条款</text>
          </view>
          <view class="service-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部信息 -->
    <view class="footer-section">
      <view class="app-info">
        <text class="version-text">黑ICP备2025041498号-1X v1.0.0</text>
        <text class="copyright-text">© 2025 好民居自助停车</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import {ref, onMounted} from 'vue';
import AuthAPI from '@/api/auth.js';
import {
  onShow,
  onHide,
  onLoad,
  onShareAppMessage,
  onShareTimeline
} from "@dcloudio/uni-app"
// 用户信息
const userInfo = ref({
  avatar: '',
  nickname: '',
  phone: ''
});

// 获取用户信息
const getUserInfo = async () => {
  try {
    const res = await AuthAPI.userInfo();
    if (res && res.data) {
      const {userInfo: userData, permissions, roles} = res.data;
      userInfo.value = userData;
    }
  } catch (e) {
    console.error('获取用户信息失败：', e);
  }
};


onShow(async () => {
  await getUserInfo();
});

// 方法
const navigateTo = (url) => {
  uni.navigateTo({url});
};

// 编辑个人资料
const editProfile = () => {
  uni.navigateTo({
    url: '/pages/my/profile-edit'
  });
};


// 显示联系方式
const showContact = () => {
  uni.showModal({
    title: '联系客服',
    content: '客服电话：0451-51753591\n工作时间：9:00-18:00',
    showCancel: false
  });
};

// 显示关于信息
const showAbout = () => {
  uni.showModal({
    title: '关于我们',
    content: '好民居自助停车管理系统\n 版本：v1.0.0\n 为您提供便捷的停车服务',
    showCancel: false
  });
};
</script>

<style lang="scss">
.container {
  background: linear-gradient(180deg, #f0f2f5 0%, #ffffff 100%);
  min-height: 100vh;
  padding-bottom: 120rpx; // 根据底部高度调整

}

// 头部区域
.header-section {
  position: relative;
  padding-bottom: 40rpx;
}

.header-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: 400rpx;
  position: relative;
  overflow: hidden;
}

.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.3;
}

.bg-circles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: -100rpx;
  right: -50rpx;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 200rpx;
  left: -75rpx;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 100rpx;
  right: 100rpx;
}

.user-card {
  position: absolute;
  bottom: -20rpx;
  left: 30rpx;
  right: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  padding: 30rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar-container {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
  border: 4rpx solid rgba(102, 126, 234, 0.2);
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
  position: relative;
}

.avatar {
  width: 100%;
  height: 100%;
}

.avatar-badge {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 32rpx;
  height: 32rpx;
  background: #52c41a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid #fff;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: #2c3e50;
}

.user-phone {
  font-size: 24rpx;
  color: #7f8c8d;
  margin-bottom: 8rpx;
}

.user-level {
  display: flex;
  align-items: center;
}

.level-text {
  font-size: 20rpx;
  color: #FFD700;
  margin-left: 6rpx;
  font-weight: 500;
}

.edit-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s;
}

.edit-btn:active {
  transform: scale(0.9);
}

// 快捷功能区域
.quick-actions {
  margin: 30rpx;
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.section-subtitle {
  font-size: 22rpx;
  color: #7f8c8d;
  margin-left: 10rpx;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s;
}

.action-item:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.12);
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.action-icon-car {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.action-icon-register {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.action-icon-record {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.action-icon-qr {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.action-text {
  font-size: 22rpx;
  color: #2c3e50;
  font-weight: 500;
  text-align: center;
}

// 服务菜单
.service-section {
  margin: 30rpx;
}

.service-list {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10rpx);
}

.service-item {
  display: flex;
  align-items: center;
  padding: 32rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s;
}

.service-item:last-child {
  border-bottom: none;
}

.service-item:active {
  background-color: #f8f9fa;
}

.service-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.service-icon-help {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.service-icon-contact {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.service-icon-about {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.service-content {
  flex: 1;
}

.service-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.service-desc {
  font-size: 22rpx;
  color: #7f8c8d;
  margin-left: 10rpx;
}

.service-arrow {
  margin-left: 16rpx;
}

// 底部信息
.footer-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0;
  padding: 20rpx 30rpx;
  backdrop-filter: blur(10rpx);
  z-index: 100;
 }

.app-info {
  text-align: center;
  padding: 20rpx;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
}

.version-text {
  font-size: 24rpx;
  color: #7f8c8d;
  margin-bottom: 8rpx;
}

.copyright-text {
  margin-left: 10rpx;
  font-size: 20rpx;
  color: #bdc3c7;
}

.card-container {
margin: 20rpx;
position: relative;
z-index: 2;
}

.section-title {
font-size: 32rpx;
font-weight: bold;
margin-bottom: 20rpx;
position: relative;
padding-left: 20rpx;
}

.section-title::before {
content: "";
position: absolute;
left: 0;
top: 50%;
transform: translateY(-50%);
width: 8rpx;
height: 32rpx;
background: linear-gradient(to bottom, #2196F3, #0D47A1);
border-radius: 4rpx;
}

.cars-container {
margin-bottom: 30rpx;
}

.car-list {
background-color: #fff;
border-radius: 12rpx;
padding: 10rpx;
box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}

.car-item {
display: flex;
align-items: center;
padding: 20rpx;
border-bottom: 1px solid #f0f0f0;
transition: background-color 0.3s;
}

.car-item:last-child {
border-bottom: none;
}

.car-item:active {
background-color: #f5f9ff;
}

.car-icon {
width: 90rpx;
height: 90rpx;
background: linear-gradient(145deg, #e6f5ff, #d5e8ff);
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 20rpx;
box-shadow: 0 4rpx 10rpx rgba(33, 150, 243, 0.15);

.car-icon-image {
width: 80%;
height: 80%;
}
}

.car-info {
flex: 1;
}

.car-park {
font-size: 30rpx;
font-weight: bold;
margin-bottom: 8rpx;
color: #2c3e50;
}

.car-plate {
font-size: 24rpx;
color: #546e7a;
margin-bottom: 8rpx;
}

.car-desc {
font-size: 24rpx;
color: #78909c;
}

.car-action {
width: 60rpx;
height: 60rpx;
display: flex;
align-items: center;
justify-content: center;
}

.menu-list {
background-color: #fff;
border-radius: 12rpx;
margin-bottom: 30rpx;
box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
overflow: hidden;
}

.menu-icon {
width: 36rpx;
height: 36rpx;
border-radius: 8rpx;
display: flex;
align-items: center;
justify-content: center;
margin-right: 12rpx;
}

.menu-icon-parking {
background: linear-gradient(135deg, #4CAF50, #1B5E20);
}

.menu-icon-payment {
background: linear-gradient(135deg, #F44336, #B71C1C);
}

.menu-icon-help {
background: linear-gradient(135deg, #FF9800, #E65100);
}

.app-info {
text-align: center;
padding: 40rpx 0;
}

.version-text {
font-size: 24rpx;
color: #999;
}
</style> 