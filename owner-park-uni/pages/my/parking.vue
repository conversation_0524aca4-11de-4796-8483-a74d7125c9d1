<template>
  <view class="page-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="header-content">
        <view class="header-left">
          <view class="header-title">我的车位</view>
          <view class="header-subtitle">管理您的车位和车辆信息</view>
        </view>
        <view class="header-right">
          <view class="refresh-btn" @tap="refreshParkingList">
            <uni-icons type="refreshempty" size="16" color="#667eea"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <uni-load-more status="loading" />
    </view>

    <!-- 车位列表 -->
    <view v-else-if="parkingList.length > 0" class="parking-list-container">
      <view
        class="parking-item"
        v-for="(parking, index) in parkingList"
        :key="parking.id"
      >
        <!-- 车位信息头部 -->
        <view class="parking-header">
          <view class="parking-info">
            <view class="parking-name">{{ parking.parkName }}</view>
            <view class="garage-name">{{ parking.garageName || '未知车库' }}</view>
            <view class="parking-details">
              <view class="detail-item">
                <uni-icons type="calendar" size="14" color="#666"></uni-icons>
                <text class="detail-text">到期：{{ parking.endDateStr || '未知' }}</text>
              </view>
            </view>
          </view>
          <view class="parking-status">
            <view class="expire-status" :class="getExpireClass(parking.expireStatus)">
              {{ getExpireStatusText(parking.expireStatus) }}
            </view>
            <!-- 续费按钮 -->
            <view
                v-if="needRenewal(parking.expireStatus)"
                class="renewal-btn"
                @tap="handleRenewal(parking)"
            >
              <uni-icons type="wallet" size="14" color="#fff"></uni-icons>
              <text class="renewal-text">续费</text>
            </view>
          </view>
        </view>

        <!-- 车辆列表 -->
        <view class="cars-section">
          <view class="cars-header">
            <text class="cars-title">关联车辆 ({{ parking.cars ? parking.cars.length : 0 }})</text>
            <view class="add-car-btn" @tap="showAddCarDialog(parking)">
              <uni-icons type="plus" size="16" color="#2196F3"></uni-icons>
              <text class="add-car-text">添加车辆</text>
            </view>
          </view>

          <!-- 车辆列表 -->
          <view v-if="parking.cars && parking.cars.length > 0" class="cars-list">
            <view
              class="car-item"
              v-for="(car, carIndex) in parking.cars"
              :key="car.id"
            >
              <view class="car-icon">
                <image class="car-icon-image" src="@/static/car.png" mode="aspectFill"></image>
              </view>
              <view class="car-info">
                <view class="car-plate">{{ car.plateNum }}</view>
                <view class="car-model">{{ getCarTypeName(car.carTypeId) }}</view>
                <view class="car-owner">{{ car.pname || '未知车主' }}</view>
              </view>
              <view class="car-actions">
                <view class="action-btn delete-btn" @tap.stop="deleteCar(car, parking)">
                  <uni-icons type="trash" size="14" color="#f56c6c"></uni-icons>
                </view>
              </view>
            </view>
          </view>

          <!-- 无车辆提示 -->
          <view v-else class="no-cars-tip">
            <uni-icons type="info" size="24" color="#ccc"></uni-icons>
            <text class="tip-text">暂无关联车辆</text>
            <text class="tip-desc">点击上方按钮添加车辆</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else class="empty-state">
      <uni-icons type="home" size="80" color="#ccc"></uni-icons>
      <text class="empty-title">暂无车位信息</text>
      <text class="empty-desc">请先在后台关联车位</text>
    </view>

    <!-- 添加车辆弹窗 -->
    <uni-popup ref="carPopup" type="center" background-color="#fff">
      <view class="car-form-container" :class="{ 'keyboard-active': keyboardActive }">
        <view class="form-header">
          <text class="form-title">添加车辆到车位</text>
          <view class="close-btn" @tap="closeCarDialog">
            <uni-icons type="closeempty" size="20" color="#fff"></uni-icons>
          </view>
        </view>

        <!-- 车位信息显示 -->
        <view v-if="currentParking" class="parking-info-display">
          <view class="info-item">
            <uni-icons type="location" size="16" color="#2196F3"></uni-icons>
            <text class="info-label">车位：</text>
            <text class="info-value">{{ currentParking.parkName }}</text>
          </view>
          <view class="info-item">
            <uni-icons type="home" size="16" color="#2196F3"></uni-icons>
            <text class="info-label">车库：</text>
            <text class="info-value">{{ currentParking.pgname }}</text>
          </view>
        </view>

        <view class="form-content">
          <uni-forms ref="carForm" :model="carFormData" :rules="carFormRules">
            <uni-forms-item label="车牌号" name="plateNum" required>
              <view class="plate-input-container" :class="{ 'keyboard-focus': keyboardActive }">
                <license-plate-keyboard
                  v-model="carFormData.plateNum"
                  ref="licensePlateKeyboard"
                  placeholder="请输入车牌号"
                />
              </view>
            </uni-forms-item>

            <uni-forms-item label="车型" name="carTypeId">
              <view class="car-type-selector" @tap="showCarTypeSelector">
                <uni-easyinput
                  v-model="selectedCarTypeName"
                  :disabled="true"
                  placeholder="请选择车型（可选）"
                  suffix-icon="arrowdown"
                />
              </view>
            </uni-forms-item>
          </uni-forms>
        </view>

        <view class="form-actions">
          <button class="cancel-btn" @tap="closeCarDialog">取消</button>
          <button class="submit-btn" @tap="submitCarForm">添加车辆</button>
        </view>
      </view>
    </uni-popup>

    <!-- 车型选择弹窗 -->
    <uni-popup ref="carTypePopup" type="center" background-color="#fff">
      <view class="car-type-select-container">
        <view class="select-header">
          <text class="select-title">选择车型</text>
          <view class="close-btn" @tap="closeCarTypeDialog">
            <uni-icons type="closeempty" size="20" color="#fff"></uni-icons>
          </view>
        </view>

        <view class="car-type-list">
          <view
            class="car-type-option"
            v-for="carType in carTypeOptions"
            :key="carType.value"
            @tap="selectCarType(carType)"
          >
            <view class="car-type-info">
              <text class="car-type-name">{{ carType.label }}</text>
            </view>
            <view class="car-type-check" v-if="carFormData.carTypeId === carType.value">
              <uni-icons type="checkmarkempty" size="20" color="#007AFF"></uni-icons>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import MyParkingAPI from '@/api/myParking';
import DictDataAPI from '@/api/dict';
import LicensePlateKeyboard
  from '@/uni_modules/license-plate-keyboard/components/license-plate-keyboard/license-plate-keyboard.vue';
import {
  onShow,
  onHide,
  onLoad,
  onShareAppMessage,
  onShareTimeline
} from "@dcloudio/uni-app"
// 响应式数据
const loading = ref(false);
const parkingList = ref([]);
const keyboardActive = ref(false);

// 弹窗引用
const carPopup = ref();
const carForm = ref();
const licensePlateKeyboard = ref();
const carTypePopup = ref();

const selectedCarTypeName = ref('');

// 表单数据
const currentParking = ref(null);
const carFormData = reactive({
  plateNum: '',
  carTypeId: '',
  ownerPhone: '',
  parkingId: null
});

// 表单验证规则
const carFormRules = {
  plateNum: {
    rules: [
      { required: true, errorMessage: '请输入车牌号' }
    ]
  }
};

// 页面加载
onMounted(() => {
  loadParkingList();

  // 监听键盘弹出事件
  uni.onKeyboardHeightChange((res) => {
    const isKeyboardShow = res.height > 0;
    keyboardActive.value = isKeyboardShow;

    if (isKeyboardShow) {
      // 键盘弹出时，延迟一点时间确保弹窗动画完成后再滚动
      setTimeout(() => {
        // 滚动到车牌号输入框
        scrollToPlateInput();
      }, 300);
    }

    console.log('键盘高度变化：', res.height, '键盘状态：', isKeyboardShow);
  });
});

// 加载车位列表
const loadParkingList = async () => {
  loading.value = true;
  try {
    const result = await MyParkingAPI.getMyParkingList();
    if (result.code === 200) {
      parkingList.value = result.data || [];
    } else {
      uni.showToast({
        title: result.message || '获取车位列表失败',
        icon: 'error'
      });
    }
  } catch (error) {
    console.error('获取车位列表失败：', error);
    uni.showToast({
      title: '网络错误，请稍后重试',
      icon: 'error'
    });
  } finally {
    loading.value = false;
  }
};

// 刷新车位列表
const refreshParkingList = () => {
  loadParkingList();
};

// 获取到期状态样式类
const getExpireClass = (expireStatus) => {
  switch (expireStatus) {
    case 0: return 'status-normal';
    case 1: return 'status-warning';
    case 2: return 'status-expired';
    default: return 'status-unknown';
  }
};

// 获取到期状态文本
const getExpireStatusText = (expireStatus) => {
  switch (expireStatus) {
    case 0: return '正常';
    case 1: return '即将到期';
    case 2: return '已到期';
    default: return '未知';
  }
};

// 根据车型ID获取车型名称
const getCarTypeName = (carTypeId) => {
  if (!carTypeId) return '未知车型';
  const carType = carTypeOptions.value.find(item => item.value === carTypeId.toString());
  return carType ? carType.label : '未知车型';
};

// 显示车型选择器
const showCarTypeSelector = () => {
  carTypePopup.value.open();
};

// 选择车型
const selectCarType = (carType) => {
  carFormData.carTypeId = carType.value;
  selectedCarTypeName.value = carType.label;
  carTypePopup.value.close();
};

// 关闭车型选择弹窗
const closeCarTypeDialog = () => {
  carTypePopup.value.close();
};

// 滚动到车牌号输入框
const scrollToPlateInput = () => {
  try {
    // 使用uni.createSelectorQuery来获取元素位置并滚动
    const query = uni.createSelectorQuery();
    query.select('.form-content').scrollOffset();
    query.exec((res) => {
      if (res && res[0]) {
        // 滚动到顶部，确保车牌号输入框可见
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 200
        });
      }
    });
  } catch (error) {
    console.log('滚动到输入框失败：', error);
  }
};

// 显示添加车辆弹窗
const showAddCarDialog = (parking) => {
  // 检查车位是否已到期
  if (parking.expireStatus === 2) {
    uni.showModal({
      title: '车位已到期',
      content: '车位已到期，不允许添加车辆信息，请先续费后添加',
      showCancel: false,
      confirmText: '知道了'
    });
    return;
  }

  currentParking.value = parking;
  resetCarForm();
  carFormData.parkingId = parking.id;
  carPopup.value.open();
};

// 删除车辆
const deleteCar = (car, parking) => {
  uni.showModal({
    title: '确认删除',
    content: `确定要从车位 ${parking.parkName} 删除车辆 ${car.plateNum} 吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {

          const  param = {
            carId: car.id,
            parkingId: parking.id
          }

          const result = await MyParkingAPI.removeCarFromParking(param);
          if (result.code === 200) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
            loadParkingList();
          } else {
            uni.showToast({
              title: result.message || '删除失败',
              icon: 'error'
            });
          }
        } catch (error) {
          console.error('删除车辆失败：', error);
          uni.showToast({
            title: '网络错误，请稍后重试',
            icon: 'error'
          });
        }
      }
    }
  });
};

// 关闭车辆弹窗
const closeCarDialog = () => {
  carPopup.value.close();
  resetCarForm();
};

// 重置表单
const resetCarForm = () => {
  carFormData.plateNum = '';
  carFormData.carTypeId = '';
  carFormData.ownerPhone = '';
  carFormData.parkingId = null;
  selectedCarTypeName.value = '';
  currentParking.value = null;
};

// 提交表单
const submitCarForm = () => {
  carForm.value.validate().then(async () => {
    try {
      // 添加车辆到车位
      const result = await MyParkingAPI.addCarToParking(carFormData);

      if (result.code === 200) {
        uni.showToast({
          title: '添加成功',
          icon: 'success'
        });
        closeCarDialog();
        loadParkingList();
      } else {
        uni.showToast({
          title: result.message || '添加失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('添加失败：', error);
      uni.showToast({
        title: '网络错误，请稍后重试',
        icon: 'error'
      });
    }
  }).catch(err => {
    console.log('表单验证失败：', err);
  });
};

// 获取车辆类型字典值
const carTypeOptions = ref([]);

// 获取车辆类型字典值
function getCarTypeOptions() {
  DictDataAPI.getOptions('vehicle_type')
      .then((res) => {
        if (res.code === 200 && res.data && res.data.length > 0) {
          carTypeOptions.value = res.data;
        }
      })
      .catch((error) => {
        console.error('获取车辆类型失败：', error);
        // 使用默认值
      })
}

// 判断是否需要续费
const needRenewal = (expireStatus) => {
  // 即将到期(1)或已到期(2)时显示续费按钮
  return expireStatus === 1 || expireStatus === 2;
};

// 处理续费
const handleRenewal = (parking) => {
  uni.showModal({
    title: '续费确认',
    content: `确定要为车位 ${parking.parkName} 续费吗？`,
    success: (res) => {
      if (res.confirm) {
        // 跳转到续费页面，传递车位信息和到期时间
        uni.navigateTo({
          url: `/pages/index/registration?parkingId=${parking.id}&parkName=${parking.parkName}&endDate=${parking.endDateStr}&renewal=true`
        });
      }
    }
  });
};

onShow(() => {
  getCarTypeOptions();
});
</script>

<style lang="scss">
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
}

// 页面头部
.page-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  flex: 1;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 24rpx;
  color: #7f8c8d;
}

.header-right {
  margin-left: 20rpx;
}

.refresh-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  background: rgba(102, 126, 234, 0.1);
  border: 2rpx solid rgba(102, 126, 234, 0.2);
  border-radius: 50%;
  transition: all 0.3s;
}

.refresh-btn:active {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(0.9);
}

// 加载状态
.loading-container {
  padding: 80rpx 0;
  text-align: center;
}

// 车位列表
.parking-list-container {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.parking-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.parking-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-bottom: 1rpx solid #dee2e6;
}

.parking-info {
  flex: 1;
}
.garage-name {
  font-size: 24rpx;
  color: #2c3e50;
  margin-bottom: 6rpx;
  font-weight: 500;
}
.parking-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 12rpx;
}

.parking-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-text {
  font-size: 22rpx;
  color: #7f8c8d;
  margin-left: 8rpx;
}

.parking-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.parking-type {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #fff;
  font-weight: 500;
}

.parking-type.public {
  background-color: #52c41a;
}

.parking-type.private {
  background-color: #faad14;
}

.expire-status {
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 18rpx;
  color: #fff;
  font-weight: 500;
}

.status-normal {
  background-color: #52c41a;
}

.status-warning {
  background-color: #faad14;
}

.status-expired {
  background-color: #ff4d4f;
}

.status-unknown {
  background-color: #8c8c8c;
}
.renewal-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s;
}

.renewal-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.4);
}

.renewal-text {
  font-size: 20rpx;
  color: #fff;
  margin-left: 6rpx;
  font-weight: bold;
}
// 车辆部分
.cars-section {
  padding: 0;
}

.cars-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #dee2e6;
}

.cars-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #495057;
}

.add-car-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: rgba(33, 150, 243, 0.1);
  border: 1rpx solid rgba(33, 150, 243, 0.3);
  border-radius: 16rpx;
  transition: all 0.3s;
}

.add-car-btn:active {
  background: rgba(33, 150, 243, 0.2);
  transform: scale(0.95);
}

.add-car-text {
  font-size: 22rpx;
  color: #2196F3;
  margin-left: 6rpx;
}

.cars-list {
  background: #fff;
}

.car-item {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s;
  position: relative;
}

.car-item:last-child {
  border-bottom: none;
}

.car-item:active {
  background-color: #f8f9fa;
  transform: scale(0.98);
}

.no-cars-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 24rpx;
  background: #fff;
}

.tip-text {
  font-size: 26rpx;
  color: #8c8c8c;
  margin-top: 16rpx;
}

.tip-desc {
  font-size: 22rpx;
  color: #bfbfbf;
  margin-top: 8rpx;
}

.car-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}

.car-icon-image {
  width: 60%;
  height: 60%;
  filter: brightness(0) invert(1);
}

.car-info {
  flex: 1;
}

.car-plate {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.car-model {
  font-size: 22rpx;
  color: #7f8c8d;
  margin-bottom: 4rpx;
}

.car-owner {
  font-size: 20rpx;
  color: #8c8c8c;
}

.car-actions {
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.delete-btn {
  background-color: rgba(245, 108, 108, 0.1);
  border: 2rpx solid rgba(245, 108, 108, 0.2);
}

.delete-btn:active {
  background-color: rgba(245, 108, 108, 0.2);
  transform: scale(0.9);
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  opacity: 0.6;
  margin-bottom: 24rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #7f8c8d;
  margin-bottom: 32rpx;
  text-align: center;
}

.empty-add-btn {
  display: flex;
  align-items: center;
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 25rpx;
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s;
}

.empty-add-btn:active {
  transform: translateY(2rpx);
}

.empty-add-text {
  color: #fff;
  font-size: 26rpx;
  margin-left: 8rpx;
}

// 车辆表单弹窗
.car-form-container {
  background: #fff;
  border-radius: 20rpx;
  padding: 0;
  width: 90vw;
  max-height: 70vh;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

// 键盘激活时的样式
.car-form-container.keyboard-active {
  transform: translateY(-120rpx);
  max-height: 55vh;
  width: 95vw;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.15);
}

// 针对不同屏幕尺寸的适配
@media screen and (max-height: 667px) {
  .car-form-container.keyboard-active {
    transform: translateY(-180rpx);
    max-height: 45vh;
  }

  .keyboard-active .form-content {
    max-height: 30vh;
  }
}

@media screen and (max-height: 568px) {
  .car-form-container.keyboard-active {
    transform: translateY(-200rpx);
    max-height: 40vh;
  }

  .keyboard-active .form-content {
    max-height: 25vh;
  }
}

.form-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.parking-info-display {
  padding: 24rpx 30rpx;
  background: linear-gradient(135deg, #f8f9ff, #e8f5ff);
  border-bottom: 1rpx solid #e9ecef;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 24rpx;
  color: #666;
  margin-left: 8rpx;
  margin-right: 8rpx;
}

.info-value {
  font-size: 24rpx;
  color: #2c3e50;
  font-weight: 500;
}

.form-content {
  padding: 0 30rpx;
  max-height: 60vh;
  overflow-y: auto;
  transition: max-height 0.3s ease;
}

// 键盘激活时调整表单内容高度
.keyboard-active .form-content {
  max-height: 35vh;
}

.form-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.3s;
}

.close-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.9);
}

.form-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  background: #f8f9fa;
}

.cancel-btn {
  flex: 1;
  height: 80rpx;
  background: #fff;
  border: 2rpx solid #e9ecef;
  border-radius: 40rpx;
  color: #6c757d;
  font-size: 28rpx;
  transition: all 0.3s;
}

.cancel-btn:active {
  background: #f8f9fa;
  transform: scale(0.98);
}

.submit-btn {
  flex: 1;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 40rpx;
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s;
}

.submit-btn:active {
  transform: scale(0.98);
}

.parking-selector {
  width: 100%;
}

// 车位选择弹窗
.parking-select-container {
  background: #fff;
  border-radius: 20rpx;
  width: 600rpx;
  max-height: 70vh;
  overflow: hidden;
}

.select-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.select-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

.parking-list {
  max-height: 500rpx;
  overflow-y: auto;
}

.parking-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s;
}

.parking-option:last-child {
  border-bottom: none;
}

.parking-option:active {
  background-color: #f8f9fa;
}

.parking-info {
  flex: 1;
}

.parking-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.parking-pool {
  font-size: 22rpx;
  color: #7f8c8d;
}

.parking-type {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #fff;
  font-weight: 500;
}

.parking-type.public {
  background-color: #52c41a;
}

.parking-type.private {
  background-color: #faad14;
}

// 车型选择器
.car-type-selector {
  width: 100%;
}

// 车型选择弹窗
.car-type-select-container {
  background: #fff;
  border-radius: 20rpx;
  width: 600rpx;
  max-height: 70vh;
  overflow: hidden;
}

.car-type-list {
  max-height: 500rpx;
  overflow-y: auto;
}

.car-type-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s;
}

.car-type-option:last-child {
  border-bottom: none;
}

.car-type-option:active {
  background-color: #f8f9fa;
}

.car-type-info {
  flex: 1;
}

.car-type-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #2c3e50;
}

.car-type-check {
  margin-left: 16rpx;
}

// 车牌号输入框容器
.plate-input-container {
  transition: all 0.3s ease;
  border-radius: 8rpx;
  padding: 4rpx;
}

.plate-input-container.keyboard-focus {
  background: linear-gradient(135deg, #f0f8ff, #e6f3ff);
  border: 2rpx solid #007AFF;
  box-shadow: 0 0 10rpx rgba(0, 122, 255, 0.3);
}
</style>
