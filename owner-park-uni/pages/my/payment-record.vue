<template>
  <view class="page-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="header-content">
        <view class="header-left">
          <view class="header-title">缴费记录</view>
          <view class="header-subtitle">查看您的所有缴费记录</view>
        </view>
        <view class="header-right">
          <view class="refresh-btn" @tap="refreshRecords">
            <uni-icons type="refreshempty" size="16" color="#667eea"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 统计卡片 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stat-item">
          <view class="stat-icon stat-icon-total">
            <uni-icons type="wallet" size="20" color="#fff"></uni-icons>
          </view>
          <view class="stat-info">
            <text class="stat-number">¥{{ statsData.totalAmount }}</text>
            <text class="stat-label">累计缴费</text>
          </view>
        </view>

        <view class="stat-divider"></view>

        <view class="stat-item">
          <view class="stat-icon stat-icon-count">
            <uni-icons type="list" size="20" color="#fff"></uni-icons>
          </view>
          <view class="stat-info">
            <text class="stat-number">{{ statsData.totalCount }}</text>
            <text class="stat-label">缴费次数</text>
          </view>
        </view>

        <view class="stat-divider"></view>

        <view class="stat-item">
          <view class="stat-icon stat-icon-recent">
            <uni-icons type="calendar" size="20" color="#fff"></uni-icons>
          </view>
          <view class="stat-info">
            <text class="stat-number">¥{{ statsData.recentAmount }}</text>
            <text class="stat-label">本月缴费</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <uni-load-more status="loading" />
    </view>

    <!-- 缴费记录列表 -->
    <view v-else-if="paymentRecords.length > 0" class="records-section">
      <view class="section-header">
        <text class="section-title">缴费明细</text>
        <text class="section-subtitle">按时间倒序排列</text>
      </view>

      <view class="records-list">
        <view
          class="record-item"
          v-for="(record, index) in paymentRecords"
          :key="record.id"
          @tap="viewRecordDetails(record)"
        >
          <!-- 记录图标 -->
          <view class="record-icon">
            <view class="icon-container" :class="getStatusIconClass(record.payStatus)">
              <uni-icons :type="getStatusIcon(record.payStatus)" size="20" color="#fff"></uni-icons>
            </view>
          </view>

          <!-- 记录信息 -->
          <view class="record-info">
            <view class="record-header">
              <view class="record-title">{{ record.parkNum || '车位缴费' }}</view>
              <view class="record-amount" :class="getAmountClass(record.payStatus)">
                {{ record.payStatus === '2' ? '' : '' }}¥{{ record.rechargeAmount }}
              </view>
            </view>

            <view class="record-details">
              <view class="detail-row">
                <text class="detail-label">订单号：</text>
                <text class="detail-value">{{ record.rechargeSn }}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">缴费类型：</text>
                <text class="detail-value">{{ record.rechargeType }}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">缴费时间：</text>
                <text class="detail-value">{{ record.createTime }}</text>
              </view>
            </view>
          </view>

          <!-- 状态标识 -->
          <view class="record-status">
            <view class="status-badge" :class="getStatusClass(record.payStatus)">
              {{ record.payStatusLabel }}
            </view>
            <!-- 待支付状态显示支付按钮 -->
            <view v-if="record.payStatus === '1'" class="pay-btn" @tap.stop="handlePayment(record)">
              <uni-icons type="wallet" size="16" color="#fff"></uni-icons>
              <text class="pay-btn-text">去支付</text>
            </view>
            <!-- 其他状态显示箭头 -->
            <view v-else class="arrow-icon">
              <uni-icons type="arrowright" size="14" color="#ccc"></uni-icons>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else class="empty-state">
      <view class="empty-icon">
        <uni-icons type="wallet" size="80" color="#ccc"></uni-icons>
      </view>
      <text class="empty-title">暂无缴费记录</text>
      <text class="empty-desc">您还没有任何缴费记录</text>
      <view class="empty-action" @tap="goToRegistration">
        <uni-icons type="plus" size="18" color="#667eea"></uni-icons>
        <text class="empty-action-text">立即缴费</text>
      </view>
    </view>

    <!-- 记录详情弹窗 -->
    <uni-popup ref="detailPopup" type="bottom" background-color="#fff">
      <view class="detail-container">
        <view class="detail-header">
          <text class="detail-title">缴费详情</text>
          <view class="close-btn" @tap="closeDetailDialog">
            <uni-icons type="closeempty" size="20" color="#666"></uni-icons>
          </view>
        </view>

        <view v-if="selectedRecord" class="detail-content">
          <view class="detail-item">
            <text class="item-label">订单号</text>
            <text class="item-value">{{ selectedRecord.rechargeSn }}</text>
          </view>
          <view class="detail-item">
            <text class="item-label">车位号</text>
            <text class="item-value">{{ selectedRecord.parkNum || '未知' }}</text>
          </view>
          <view class="detail-item">
            <text class="item-label">缴费金额</text>
            <text class="item-value amount">¥{{ selectedRecord.rechargeAmount }}</text>
          </view>
          <view class="detail-item">
            <text class="item-label">缴费类型</text>
            <text class="item-value">{{ selectedRecord.rechargeType }}</text>
          </view>
          <view class="detail-item">
            <text class="item-label">支付状态</text>
            <text class="item-value status-badge" :class="getStatusClass(selectedRecord.payStatus)">
              {{ selectedRecord.payStatusLabel }}
            </text>
          </view>
          <view class="detail-item">
            <text class="item-label">缴费时间</text>
            <text class="item-value">{{ selectedRecord.createTime }}</text>
          </view>
          <view class="detail-item" v-if="selectedRecord.remark">
            <text class="item-label">备注</text>
            <text class="item-value">{{ selectedRecord.remark }}</text>
          </view>
        </view>

        <!-- 待支付状态显示支付按钮 -->
        <view v-if="selectedRecord && selectedRecord.payStatus === '1'" class="detail-actions">
          <view class="pay-action-btn" @tap="handlePayment(selectedRecord)">
            <uni-icons type="wallet" size="20" color="#fff"></uni-icons>
            <text class="pay-action-text">立即支付</text>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import PaymentRecordAPI from '@/api/paymentRecord';
import RegistrationAPI from '@/api/registration';

// 响应式数据
const loading = ref(false);
const paymentRecords = ref([]);
const selectedRecord = ref(null);

// 弹窗引用
const detailPopup = ref();

// 统计数据
const statsData = reactive({
  totalAmount: '0.00',
  totalCount: 0,
  recentAmount: '0.00'
});

// 页面加载
onMounted(() => {
  loadPaymentRecords();
});

// 加载支付记录
const loadPaymentRecords = async () => {
  loading.value = true;
  try {
    const result = await PaymentRecordAPI.getMyPaymentRecords();
    if (result.code === 200) {
      paymentRecords.value = result.data || [];
      calculateStats();
    } else {
      uni.showToast({
        title: result.message || '获取缴费记录失败',
        icon: 'error'
      });
    }
  } catch (error) {
    console.error('获取缴费记录失败：', error);
    uni.showToast({
      title: '网络错误，请稍后重试',
      icon: 'error'
    });
  } finally {
    loading.value = false;
  }
};

// 计算统计数据
const calculateStats = () => {
  const records = paymentRecords.value;

  // 计算总金额和总次数
  let totalAmount = 0;
  let totalCount = 0;
  let recentAmount = 0;

  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();

  records.forEach(record => {
    if (record.payStatus === '2') { // 只统计已支付的记录
      const amount = parseFloat(record.rechargeAmount) || 0;
      totalAmount += amount;
      totalCount++;

      // 计算本月缴费
      if (record.payTime) {
        const payDate = new Date(record.payTime);
        if (payDate.getMonth() === currentMonth && payDate.getFullYear() === currentYear) {
          recentAmount += amount;
        }
      }
    }
  });

  statsData.totalAmount = totalAmount.toFixed(2);
  statsData.totalCount = totalCount;
  statsData.recentAmount = recentAmount.toFixed(2);
};

// 刷新记录
const refreshRecords = () => {
  loadPaymentRecords();
};

// 获取状态图标
const getStatusIcon = (payStatus) => {
  switch (payStatus) {
    case '1': return 'locked';           // 待支付
    case '2': return 'checkmarkempty';  // 已支付
    case '3': return 'closeempty';      // 支付失败
    case '4': return 'undo';            // 已退款
    default: return 'help';             // 未知
  }
};

// 获取状态图标样式类
const getStatusIconClass = (payStatus) => {
  switch (payStatus) {
    case '1': return 'icon-pending';    // 待支付 - 橙色
    case '2': return 'icon-success';    // 已支付 - 绿色
    case '3': return 'icon-failed';     // 支付失败 - 红色
    case '4': return 'icon-refund';     // 已退款 - 蓝色
    default: return 'icon-unknown';     // 未知 - 灰色
  }
};

// 获取状态样式类
const getStatusClass = (payStatus) => {
  switch (payStatus) {
    case '1': return 'status-pending';
    case '2': return 'status-success';
    case '3': return 'status-failed';
    case '4': return 'status-refund';
    default: return 'status-unknown';
  }
};

// 获取金额样式类
const getAmountClass = (payStatus) => {
  switch (payStatus) {
    case '1': return 'amount-pending';
    case '2': return 'amount-success';
    case '3': return 'amount-failed';
    case '4': return 'amount-refund';
    default: return 'amount-unknown';
  }
};

// 查看记录详情
const viewRecordDetails = (record) => {
  selectedRecord.value = record;
  detailPopup.value.open();
};

// 关闭详情弹窗
const closeDetailDialog = () => {
  detailPopup.value.close();
  selectedRecord.value = null;
};

// 跳转到缴费页面
const goToRegistration = () => {
  uni.navigateTo({
    url: '/pages/index/registration'
  });
};

// 处理支付
const handlePayment = async (record) => {
  if (!record || !record.registrationId) {
    uni.showToast({
      title: '支付信息不完整',
      icon: 'error'
    });
    return;
  }

  // 检查支付状态
  if (record.payStatus !== '1') {
    uni.showToast({
      title: '该订单不是待支付状态',
      icon: 'error'
    });
    return;
  }

  try {
    uni.showLoading({
      title: '正在创建支付订单...'
    });

    // 创建支付订单
    const param = {
      registrationId: record.registrationId,
      openid: uni.getStorageSync('openid'),
      rechargeId: record.id
    };

    const result = await RegistrationAPI.createPaymentOrder(param);

    if (result.code === 200 && result.data) {
      uni.hideLoading();

      // 调起支付
      uni.requestPayment({
        provider: 'wxpay',
        timeStamp: result.data.timeStamp,
        nonceStr: result.data.nonceStr,
        package: result.data.packageValue,
        signType: result.data.signType,
        paySign: result.data.paySign,
        success: function(res) {
          console.log('支付成功:', JSON.stringify(res));
          uni.showToast({
            title: '支付成功',
            icon: 'success'
          });

          // 关闭详情弹窗
          if (detailPopup.value) {
            detailPopup.value.close();
          }

          // 刷新支付记录
          setTimeout(() => {
            loadPaymentRecords();
          }, 1000);
        },
        fail: function(err) {
          console.log('支付失败:', JSON.stringify(err));
          if (err.errMsg && err.errMsg.indexOf('cancel') > -1) {
            uni.showToast({
              title: '支付已取消',
              icon: 'none'
            });
          } else {
            uni.showToast({
              title: '支付失败，请重试',
              icon: 'error'
            });
          }
        }
      });
    } else {
      uni.hideLoading();
      uni.showToast({
        title: result.message || '创建支付订单失败',
        icon: 'error'
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error('支付处理失败：', error);
    uni.showToast({
      title: '网络错误，请稍后重试',
      icon: 'error'
    });
  }
};
</script>

<style lang="scss">
.page-container {
  padding: 0;
  background: linear-gradient(180deg, #f0f2f5 0%, #ffffff 100%);
  min-height: 100vh;
}

// 页面头部
.page-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  flex: 1;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.header-subtitle {
  margin-left: 10rpx;
  font-size: 24rpx;
  color: #7f8c8d;
}

.header-right {
  margin-left: 20rpx;
}

.refresh-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  background: rgba(102, 126, 234, 0.1);
  border: 2rpx solid rgba(102, 126, 234, 0.2);
  border-radius: 50%;
  transition: all 0.3s;
}

.refresh-btn:active {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(0.9);
}

// 统计卡片
.stats-section {
  margin: 20rpx;
}

.stats-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s;
}

.stat-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.stat-icon-total {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-icon-count {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stat-icon-recent {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #7f8c8d;
}

.stat-divider {
  width: 2rpx;
  height: 60rpx;
  background: linear-gradient(180deg, transparent, #e9ecef, transparent);
  margin: 0 20rpx;
}

// 加载状态
.loading-container {
  padding: 80rpx 0;
  text-align: center;
}

// 记录列表
.records-section {
  margin: 20rpx;
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.section-subtitle {
  margin-left: 10rpx;
  font-size: 22rpx;
  color: #7f8c8d;
}

.records-list {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10rpx);
}

.record-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s;
}

.record-item:last-child {
  border-bottom: none;
}

.record-item:active {
  background-color: #f8f9fa;
}

.record-icon {
  margin-right: 20rpx;
}

.icon-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.icon-success {
  background: linear-gradient(135deg, #52c41a, #389e0d);
}

.icon-pending {
  background: linear-gradient(135deg, #faad14, #d48806);
}

.icon-failed {
  background: linear-gradient(135deg, #ff4d4f, #cf1322);
}

.icon-refund {
  background: linear-gradient(135deg, #1890ff, #096dd9);
}

.icon-unknown {
  background: linear-gradient(135deg, #8c8c8c, #595959);
}

.record-info {
  flex: 1;
  margin-right: 16rpx;
}

.record-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.record-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
}

.record-amount {
  font-size: 32rpx;
  font-weight: bold;
}

.amount-success {
  color: #52c41a;
}

.amount-pending {
  color: #faad14;
}

.amount-failed {
  color: #ff4d4f;
}

.amount-refund {
  color: #1890ff;
}

.amount-unknown {
  color: #8c8c8c;
}

.record-details {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.detail-row {
  display: flex;
  align-items: center;
}

.detail-label {
  font-size: 20rpx;
  color: #8c8c8c;
  width: 120rpx;
}

.detail-value {
  font-size: 20rpx;
  color: #595959;
  flex: 1;
}

.record-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.status-badge {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  color: #fff;
}

.status-success {
  background-color: #52c41a;
}

.status-pending {
  background-color: #faad14;
}

.status-failed {
  background-color: #ff4d4f;
}

.status-refund {
  background-color: #1890ff;
}

.status-unknown {
  background-color: #8c8c8c;
}

.arrow-icon {
  opacity: 0.5;
}

// 支付按钮
.pay-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #52c41a, #389e0d);
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);
  transition: all 0.3s;
}

.pay-btn:active {
  transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(82, 196, 26, 0.4);
}

.pay-btn-text {
  color: #fff;
  font-size: 20rpx;
  font-weight: 500;
  margin-left: 4rpx;
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #7f8c8d;
  margin-bottom: 40rpx;
  text-align: center;
}

.empty-action {
  display: flex;
  align-items: center;
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 25rpx;
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s;
}

.empty-action:active {
  transform: translateY(2rpx);
}

.empty-action-text {
  color: #fff;
  font-size: 26rpx;
  margin-left: 8rpx;
}

// 详情弹窗
.detail-container {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.detail-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.3s;
}

.close-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.9);
}

.detail-content {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.detail-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.item-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.item-value {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

.item-value.amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #52c41a;
}

// 详情弹窗支付操作区域
.detail-actions {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  background: #fafafa;
}

.pay-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #52c41a, #389e0d);
  border-radius: 25rpx;
  box-shadow: 0 4rpx 15rpx rgba(82, 196, 26, 0.4);
  transition: all 0.3s;
}

.pay-action-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.5);
}

.pay-action-text {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
  margin-left: 8rpx;
}
</style>