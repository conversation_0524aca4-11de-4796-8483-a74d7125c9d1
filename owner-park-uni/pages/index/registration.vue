<template>
  <view class="container">

    <view class="card-container">
      <uni-forms ref="form" :model="formData" :rules="rules" label-width="80px">
        <!-- 续费模式提示 -->
        <view v-if="isRenewalMode" class="renewal-notice">
          <uni-icons type="info" size="20" color="#ff6b6b"></uni-icons>
          <view class="renewal-notice-content">
            <text class="renewal-notice-text">车位续费模式</text>
            <text v-if="parkingEndDate" class="renewal-notice-desc">
              车位到期时间：{{ parkingEndDate }}
            </text>
            <text class="renewal-notice-desc">
              续费将从到期时间开始计算
            </text>
          </view>
        </view>

        <!-- 非续费模式显示的字段 -->
        <template v-if="!isRenewalMode">
          <uni-forms-item label="车主姓名" name="ownerName" required>
            <uni-easyinput v-model="formData.ownerName" placeholder="请输入车主姓名"/>
          </uni-forms-item>
          <uni-forms-item label="手机号码" name="phone" required>
            <uni-easyinput v-model="formData.phone" placeholder="请输入手机号码"/>
          </uni-forms-item>
          <uni-forms-item label="车牌号" name="carInfo" required>
            <license-plate-keyboard v-model="formData.carInfo" ref="licensePlateKeyboard"/>
          </uni-forms-item>
          <uni-forms-item label="住址信息" name="address">
            <uni-easyinput v-model="formData.address" placeholder="请输入住址信息" type="textarea"/>
          </uni-forms-item>

          <uni-forms-item label="业主凭证" name="ownerCertificate">
            <view class="image-upload-container">
              <view v-for="(item, index) in photoList" :key="index" class="image-item">
                <image
                    class="uploaded-image"
                    :src="item.url || item"
                    mode="aspectFill"
                    @tap="previewImage(index)"
                ></image>
                <view class="image-actions">
                  <view class="action-btn delete-btn" @tap="deleteImage(index)">
                    <uni-icons type="closeempty" size="16" color="#fff"></uni-icons>
                  </view>
                </view>
              </view>
              <view v-if="photoList.length < 9" class="upload-btn" @tap="chooseImage">
                <uni-icons type="camera" size="24" color="#999"></uni-icons>
                <text class="upload-text">上传图片</text>
              </view>
            </view>
            <!-- 标注非小区业主禁止使用停车软件。  -->
            <view class="notice">
              <uni-icons type="info" size="16" color="#999"></uni-icons>
              <text class="notice-text">非小区业主禁止使用停车软件。</text>
            </view>
          </uni-forms-item>
          <uni-forms-item label="选择车位" name="parkingSpot" required>
            <view class="parking-selector" @click="openParkingSelection" >
              <uni-easyinput
                  v-model="formData.parkingSpot"
                  :disabled="true"
                  placeholder="请选择车位"
                  @iconClick="openParkingSelection"
                  :suffixIcon="'search'"
              />
              <view class="parking-info" v-if="formData.parkingSpot">
                <view class="parking-icon">
                  <uni-icons type="location" size="16" color="#FFFFFF"></uni-icons>
                </view>
                <text class="parking-text">车位: {{ formData.parkingSpot }}</text>
              </view>
            </view>
          </uni-forms-item>
        </template>

        <!-- 续费模式：直接显示车位信息 -->
        <uni-forms-item v-if="isRenewalMode" label="续费车位" name="parkingSpot" required>
          <view class="renewal-parking-display">
            <view class="parking-card">
              <view class="parking-icon-large">
                <uni-icons type="home" size="24" color="#007AFF"></uni-icons>
              </view>
              <view class="parking-details">
                <view class="parking-name">{{ formData.parkingSpot }}</view>
                <view class="parking-label">当前续费车位</view>
              </view>
              <view class="renewal-badge">
                <text class="renewal-text">续费</text>
              </view>
            </view>
          </view>
        </uni-forms-item>
        <uni-forms-item label="计费类型" name="billingType" required>
          <view class="billing-options">
            <view
                v-for="(option, index) in billingTypeOptions"
                :key="index"
                class="billing-option"
                :class="{ active: formData.billingType === option.label }"
                @tap="selectBillingType(option)"
            >
              <text class="billing-text">{{ option.label }}</text>
              <text class="billing-price">{{ option.value }}元</text>
            </view>
          </view>
        </uni-forms-item>

        <uni-forms-item label="开始时间" name="startTime" required>
          <!-- 续费模式：显示固定的开始时间，不允许修改 -->
          <uni-easyinput
              v-if="isRenewalMode"
              v-model="formData.startTime"
              placeholder="续费开始时间"
              :disabled="true"
          />
          <!-- 非续费模式：允许选择开始时间 -->
          <uni-datetime-picker
              v-else
              v-model="formData.startTime"
              type="date"
              :start="minStartDate"
              @change="onStartTimeChange"
          />
        </uni-forms-item>

        <!--日租、 月租、季租、年租显示数量选择 -->
        <uni-forms-item
            :label="getPeriodLabel()"
            name="periodCount"
            required
        >
          <uni-number-box
              v-model="formData.periodCount"
              :min="1"
              :max="365"
              @change="onPeriodCountChange"
          />
        </uni-forms-item>

        <uni-forms-item label="结束时间" name="endTime" required>
          <uni-easyinput
              v-model="formData.endTime"
              placeholder="系统自动计算"
              :disabled="true"
          />
        </uni-forms-item>
      </uni-forms>

      <view class="fee-container">
        <view class="fee-card">
          <view class="fee-label">应缴费用</view>
          <view class="fee-amount">
            <text class="fee-symbol">¥</text>
            <text class="fee-value">{{ formData.shouldPay || '0.00' }}</text>
          </view>
        </view>
      </view>

      <button class="submit-btn" @tap="handleSubmit">
        {{ isRenewalMode ? '确认续费' : '提交缴费' }}
      </button>
    </view>

    <!-- 停车位选择组件 -->
    <parking-selector
        ref="parkingSelectorRef"
        @select="onParkingSelect"
        @close="onParkingSelectorClose"
    />
  </view>
</template>

<script setup>
import {ref, reactive, computed} from 'vue';
import LicensePlateKeyboard
  from '@/uni_modules/license-plate-keyboard/components/license-plate-keyboard/license-plate-keyboard.vue';
import ParkingSelector from '@/components/parking-selector/parking-selector.vue';
import DictDataAPI from '@/api/dict';
import RegistrationAPI from '@/api/registration';
import UploadAPI from "@/api/upload";

import {
  onShow,
  onHide,
  onLoad,
  onShareAppMessage,
  onShareTimeline
} from "@dcloudio/uni-app"
// 表单数据
const formData = reactive({
  carInfo: '',
  ownerName: '',
  phone: '',
  address: '',
  parkingSpot: '',
  garageId: '', // 车库ID
  garageName: '', // 车库名称
  billingType: '月租', // 默认月租
  startTime: new Date().toISOString().split('T')[0], // 格式化为yyyy-MM-dd
  endTime: '', // 由后端计算返回
  periodCount: 1, // 期数（月数、季度数、年数）
  shouldPay: '0.00' // 应缴费用
});

// 最小开始日期
const minStartDate = computed(() => {
  if (isRenewalMode.value) {
    // 续费模式下，最小日期为当天（允许用户调整续费开始时间）
    return new Date().toISOString().split('T')[0];
  } else {
    // 非续费模式下，最小日期为今天
    return new Date().toISOString().split('T')[0];
  }
});


// 表单规则
const rules = {
  carInfo: {
    rules: [
      {required: true, errorMessage: '请输入车牌号'},
      {
        validateFunction: (rule, value, data, callback) => {
          if (value.length < 7) {
            callback('车牌号格式不正确');
          }
          return true;
        }
      }
    ]
  },
  ownerName: {
    rules: [
      {required: true, errorMessage: '请输入车主姓名'}
    ]
  },
  phone: {
    rules: [
      {required: true, errorMessage: '请输入手机号码'},
      {pattern: /^1[3-9]\d{9}$/, errorMessage: '手机号码格式不正确'}
    ]
  },
  parkingSpot: {
    rules: [
      {required: true, errorMessage: '请选择车位'}
    ]
  },
  billingType: {
    rules: [
      {required: true, errorMessage: '请选择计费类型'}
    ]
  },
  startTime: {
    rules: [
      {required: true, errorMessage: '请选择开始时间'}
    ]
  },
  endTime: {
    rules: [
      {required: true, errorMessage: '请选择结束时间'}
    ]
  }
};

// 图片列表
const photoList = ref([]);

// 表单引用
const form = ref(null);
const licensePlateKeyboard = ref(null);
const parkingSelectorRef = ref(null);

// 选择计费类型
const selectBillingType = (item) => {
  formData.billingType = item.label;

  // 非续费模式下才重置开始时间为今天
  if (!isRenewalMode.value) {
    const today = new Date();
    formData.startTime = today.toISOString().split('T')[0];
  }

  // 调用后端接口计算费用和结束时间
  calculateFeeFromServer();
};

// 开始时间变化
const onStartTimeChange = (e) => {
  formData.startTime = e;
  // 调用后端接口计算费用和结束时间
  calculateFeeFromServer();
};

// 期数变化
const onPeriodCountChange = (value) => {
  formData.periodCount = value;
  // 调用后端接口计算费用和结束时间
  calculateFeeFromServer();
};

// 获取期数标签
const getPeriodLabel = () => {
  const labelMap = {
    '1': '天数',
    '日': '天数',
    '日租': '天数',
    '2': '月数',
    '月': '月数',
    '月租': '月数',
    '3': '季度数',
    '季': '季度数',
    '季租': '季度数',
    '4': '年数',
    '年': '年数',
    '年租': '年数'
  };
  return labelMap[formData.billingType] || '数量';
};

// 调用后端接口计算费用
const calculateFeeFromServer = async () => {
  if (!formData.billingType || !formData.startTime || !formData.periodCount) {
    return;
  }

  try {
    const param = {
      billingType: formData.billingType,
      startTime: formData.startTime,
      periodCount: formData.periodCount
    };

    const result = await RegistrationAPI.calculateFee(param);

    if (result.code === 200 && result.data) {
      formData.endTime = result.data.endTime;
      formData.shouldPay = result.data.shouldPay;
    }
  } catch (error) {
    console.error('计算费用失败：', error);
    uni.showToast({
      title: '计算费用失败',
      icon: 'error'
    });
  }
};

// 打开车位选择组件
const openParkingSelection = () => {
  parkingSelectorRef.value?.open();
};

// 处理车位选择
const onParkingSelect = (selectionData) => {
  if (selectionData && selectionData.parking && selectionData.parking.parkName) {
    // 保存车位信息
    formData.parkingSpot = selectionData.parking.parkName;
    formData.parkingId = selectionData.parking.id;

    // 保存车库信息
    if (selectionData.garage) {
      formData.garageId = selectionData.garage.id;
      formData.garageName = selectionData.garage.pname;
    }

    console.log('选中的车库:', selectionData.garage);
    console.log('选中的车位:', selectionData.parking);
  }
};

// 处理停车位选择器关闭
const onParkingSelectorClose = () => {
  // 可以在这里处理关闭事件，如果需要的话
};

// 选择图片
const chooseImage = () => {
  const remainingCount = 9 - photoList.value.length;
  if (remainingCount <= 0) {
    uni.showToast({
      title: '最多只能上传9张图片',
      icon: 'none'
    });
    return;
  }

  uni.chooseImage({
    count: Math.min(remainingCount, 3), // 一次最多选择3张
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: async (res) => {
      const tempFilePaths = res.tempFilePaths;

      // 显示上传进度
      uni.showLoading({
        title: '上传中...'
      });

      try {
        // 批量上传图片
        for (let i = 0; i < tempFilePaths.length; i++) {
          const filePath = tempFilePaths[i];

          try {
            // 上传图片到服务器
            const uploadResult = await UploadAPI.uploadFile(filePath);

            if (uploadResult.code === 200) {
              // 添加图片信息到列表
              photoList.value.push({
                url: uploadResult.data.url || uploadResult.data,
                localPath: filePath
              });
              console.log('图片上传成功：', uploadResult.data);
            } else {
              throw new Error(uploadResult.message || '上传失败');
            }
          } catch (error) {
            console.error('图片上传失败：', error);
            // 如果上传失败，仍然显示本地图片
            photoList.value.push({
              url: filePath,
              localPath: filePath,
              isLocal: true
            });
          }
        }

        uni.hideLoading();
        uni.showToast({
          title: '上传完成',
          icon: 'success'
        });
      } catch (error) {
        uni.hideLoading();
        console.error('图片处理失败：', error);
        uni.showToast({
          title: '处理失败',
          icon: 'error'
        });
      }
    }
  });
};

// 删除图片
const deleteImage = (index) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这张图片吗？',
    success: (res) => {
      if (res.confirm) {
        photoList.value.splice(index, 1);
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        });
      }
    }
  });
};

// 预览图片
const previewImage = (index) => {
  const urls = photoList.value.map(item => item.url || item);
  uni.previewImage({
    current: index,
    urls: urls
  });
};

// 提交表单
const handleSubmit = () => {
  form.value.validate().then(async () => {
    // 表单校验通过
    uni.showLoading({
      title: '提交中...'
    });

    try {
      // 准备提交数据
      const imageUrls = photoList.value.map(item => {
        if (typeof item === 'string') {
          return item;
        }
        return item.url || item.localPath;
      }).filter(url => url); // 过滤掉空值

      const submitData = {
        pname: formData.ownerName,
        mobile: formData.phone,
        addr: formData.address,
        ownerCertificate: imageUrls.join(','),
        plateNum: formData.carInfo,
        parkNum: formData.parkingSpot,
        parkId: formData.parkingId, // 车位ID
        garageId: formData.garageId, // 车库ID
        garageName: formData.garageName, // 车库名称
        billingType: formData.billingType,
        beginDate: formData.startTime.split('T')[0],
        endDate: formData.endTime.split('T')[0],
        shouldPay: formData.shouldPay,
        openid: uni.getStorageSync('openid'),
        isRenewal:isRenewalMode.value ? 1 : 0
      };

      console.log('提交数据：', submitData);

      // 提交登记信息
      const result = await RegistrationAPI.submitRegistration(submitData);

      uni.hideLoading();

      if (result.code === 200) {
        uni.showToast({
          title: '提交成功',
          success: () => {
            setTimeout(() => {
              uni.navigateTo({
                url: '/pages/payment/payment?registrationId=' + result.data
              });
            }, 1500);
          }
        });
      } else {
        uni.showToast({
          title: result.message || '提交失败',
          icon: 'error'
        });
      }
    } catch (error) {
      uni.hideLoading();
      console.error('提交失败：', error);
      uni.showToast({
        title: '提交失败',
        icon: 'error'
      });
    }
  }).catch(err => {
    // 表单校验失败
    console.log('表单错误信息：', err);
  });
};
const billingTypeOptions = ref([]);

// 获取计费类型字典值
function getBillingTypeOptions() {
  DictDataAPI.getOptions('billing_type')
      .then((res) => {
        if (res.code === 200 && res.data && res.data.length > 0) {
          billingTypeOptions.value = res.data;
        }
      })
      .catch((error) => {
        console.error('获取计费类型失败：', error);
        // 使用默认值
      })
}

// 续费模式相关数据
const isRenewalMode = ref(false);
const currentParkingId = ref('');
const parkingEndDate = ref('');

onLoad((options) => {
  console.log('页面参数：', options);

  // 检查是否为续费模式
  if (options.renewal === 'true') {
    isRenewalMode.value = true;
    formData.parkingSpot = options.parkName || '';
    currentParkingId.value = options.parkingId || '';
    parkingEndDate.value = options.endDate || '';
	  formData.garageId = options.garageId || 0;
    formData.parkingId = options.parkingId || '';

    // 续费模式下，开始时间固定为车位到期时间
    if (options.endDate) {
      // 直接使用车位到期时间作为续费开始时间
      formData.startTime = options.endDate;
    }

    // 续费模式下隐藏部分字段
    uni.setNavigationBarTitle({
      title: '车位续费'
    });
  } else {
    isRenewalMode.value = false;
    uni.setNavigationBarTitle({
      title: '车位登记'
    });
  }
});

onShow(() => {
  getBillingTypeOptions();
  calculateFeeFromServer();
});


onShareAppMessage(() => {
  return {
    title: '登记-好民居自助停车',
    path: '/pages/index/registration',
  }
})
onShareTimeline(() => {
  return {
    title: '登记-好民居自助停车',
    path: '/pages/index/registration',
  }
})
</script>

<style lang="scss">
.container {
  padding: 0;
  background-color: #f7f8fc;
  min-height: 100vh;
}


// 卡片容器
.card-container {
  margin: 0rpx 20rpx;
  position: relative;
  z-index: 3;

  uni-forms {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 30rpx 20rpx;
    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
    margin-bottom: 10rpx;
  }
}

// 图片上传区域
.image-upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.image-item {
  width: 120rpx;
  height: 120rpx;
  position: relative;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.uploaded-image {
  width: 100%;
  height: 100%;
}

.image-actions {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 8rpx;
  opacity: 0;
  transition: opacity 0.3s;
}

// 移动端始终显示操作按钮
.image-actions {
  opacity: 1;
}

// PC端悬停显示
@media (hover: hover) {
  .image-actions {
    opacity: 0;
  }

  .image-item:hover .image-actions {
    opacity: 1;
  }
}

.action-btn {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8rpx;
}

.delete-btn {
  background-color: rgba(255, 77, 79, 0.8);
}

.upload-btn {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  border: 2rpx dashed #ddd;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;

  &:active {
    transform: scale(0.95);
    background-color: #e8e8e8;
  }
}

.upload-text {
  font-size: 20rpx;
  color: #999;
  margin-top: 8rpx;
}

.notice {
  display: flex;
  margin-top: 10rpx;
}

.notice-text {
  font-size: 24rpx;
  color: #999;
}

// 车位选择器
.parking-selector {
  position: relative;
}

.parking-info {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
}

.parking-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background-color: #2196F3;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
}

.parking-text {
  font-size: 28rpx;
  color: #546e7a;
}

.renewal-tag {
  background-color: #ff6b6b;
  color: #fff;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  margin-left: 12rpx;
}

.renewal-notice {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background: linear-gradient(135deg, #fff5f5, #ffe8e8);
  border-radius: 12rpx;
  border-left: 4rpx solid #ff6b6b;
  margin-bottom: 20rpx;
}

.renewal-notice-content {
  margin-left: 12rpx;
  flex: 1;
}

.renewal-notice-text {
  font-size: 28rpx;
  color: #ff6b6b;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.renewal-notice-desc {
  font-size: 24rpx;
  color: #999;
  display: block;
}

// 续费车位显示
.renewal-parking-display {
  width: 100%;
}

.parking-card {
  background: linear-gradient(135deg, #f0f8ff, #e6f3ff);
  border: 2rpx solid #007AFF;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.15);
}

.parking-icon-large {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #007AFF, #0056CC);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

.parking-details {
  flex: 1;
}

.parking-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.parking-label {
  font-size: 22rpx;
  color: #7f8c8d;
}

.renewal-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  border-radius: 12rpx;
  padding: 6rpx 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}

.renewal-text {
  font-size: 20rpx;
  color: #fff;
  font-weight: bold;
}

// 计费类型选择
.billing-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.billing-option {
  flex: 1;
  min-width: 120rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s;
  border: 2rpx solid transparent;

  &.active {
    background-color: #e3f2fd;
    border-color: #2196F3;
  }
}

.billing-text {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.billing-price {
  font-size: 24rpx;
  color: #f44336;
}

// 费用展示
.fee-container {
  margin: 30rpx 0;
}

.fee-card {
  background: linear-gradient(to right, #f8f9ff, #e8f5ff);
  border-radius: 12rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
  border-left: 4rpx solid #2196F3;
}

.fee-label {
  font-size: 28rpx;
  color: #606266;
  margin-bottom: 10rpx;
}

.fee-amount {
  display: flex;
  align-items: flex-end;
  margin-bottom: 10rpx;
}

.fee-symbol {
  font-size: 32rpx;
  font-weight: bold;
  color: #f44336;
  margin-right: 4rpx;
}

.fee-value {
  font-size: 56rpx;
  font-weight: bold;
  color: #f44336;
  line-height: 1;
}

.fee-period {
  font-size: 24rpx;
  color: #78909c;
}

// 提交按钮
.submit-btn {
  width: 90%;
  height: 80rpx;
  line-height: 80rpx;
  background: linear-gradient(135deg, #2196F3, #0D47A1);
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 40rpx;
  margin: 20rpx auto;
  box-shadow: 0 8rpx 16rpx rgba(33, 150, 243, 0.3);
}
</style> 