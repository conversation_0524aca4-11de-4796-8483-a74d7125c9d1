<template>
  <view class="container">
    <view class="banner">
      <image class="banner-image" src="https://img.bhlinka.com/cw/images/20250515/banertihuan.jpg" mode="aspectFill"></image>
    </view>
    
    <view class="function-grid">
      <view class="function-row">
        <view class="function-item register-box" @tap="navigateTo('/pages/index/registration')">
          <view class="function-icon">
            <uni-icons type="plusempty" size="24" color="#FFFFFF"></uni-icons>
          </view>
          <text class="function-text">登记</text>
          <view class="tech-accent"></view>
        </view>
        
        <view class="function-item payment-box" @tap="navigateTo('/pages/my/payment-record')">
          <view class="function-icon">
            <uni-icons type="wallet" size="24" color="#FFFFFF"></uni-icons>
          </view>
          <text class="function-text">缴费</text>
          <view class="tech-accent"></view>
        </view>
      </view>
      
      <view class="function-row">
        <view class="function-item help-box" @tap="navigateTo('/pages/help/flow')">
          <view class="function-icon">
            <uni-icons type="help" size="24" color="#FFFFFF"></uni-icons>
          </view>
          <text class="function-text">帮助</text>
          <view class="tech-accent"></view>
        </view>
        
        <view class="function-item service-box" @tap="navigateTo('/pages/help/help')">
          <view class="function-icon">
            <uni-icons type="contact" size="24" color="#FFFFFF"></uni-icons>
          </view>
          <text class="function-text">客服</text>
          <view class="tech-accent"></view>
        </view>
      </view>
    </view>

    <view class="my-parking">
      <view class="section-title">
        我的车位
        <view class="refresh-btn" @tap="refreshParkingList">
          <uni-icons type="refreshempty" size="20" color="#2196F3"></uni-icons>
        </view>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <uni-load-more status="loading" />
      </view>

      <!-- 车位列表 -->
      <view v-else-if="myParkingList.length > 0" class="parking-list-container">
        <view
          class="parking-item"
          v-for="(parking, index) in myParkingList"
          :key="parking.id"
        >
          <!-- 车位信息头部 -->
          <view class="parking-header">
            <view class="parking-info">
              <view class="parking-name">{{ parking.parkName }}</view>
              <view class="garage-name">{{ parking.garageName || '未知车库' }}</view>
              <view class="parking-details">
                <view class="detail-item">
                  <uni-icons type="calendar" size="14" color="#666"></uni-icons>
                  <text class="detail-text">到期：{{ parking.endDateStr || '未知' }}</text>
                </view>
              </view>
            </view>
            <view class="parking-status">
              <view class="expire-status" :class="getExpireClass(parking.expireStatus)">
                {{ getExpireStatusText(parking.expireStatus) }}
              </view>
              <!-- 续费按钮 -->
              <view
                class="renewal-btn"
                @tap="handleRenewal(parking)"
              >
                <uni-icons type="wallet" size="14" color="#fff"></uni-icons>
                <text class="renewal-text">续费</text>
              </view>
            </view>
          </view>

          <!-- 车辆列表 -->
          <view class="cars-section">
            <view class="cars-header">
              <text class="cars-title">关联车辆 ({{ parking.cars ? parking.cars.length : 0 }})</text>
            </view>

            <!-- 车辆列表 -->
            <view v-if="parking.cars && parking.cars.length > 0" class="cars-list">
              <view
                class="car-item"
                v-for="(car, carIndex) in parking.cars"
                :key="car.id"
              >
                <view class="car-icon">
                  <image class="car-icon-image" src="@/static/car.png" mode="aspectFill"></image>
                </view>
                <view class="car-info">
                  <view class="car-plate">{{ car.plateNum }}</view>
                  <view class="car-model">{{ getCarTypeName(car.carTypeId) }}</view>
                  <view class="car-owner">{{ car.pname || '未知车主' }}</view>
                </view>
              </view>
            </view>

            <!-- 无车辆提示 -->
            <view v-else class="no-cars-tip">
              <uni-icons type="info" size="24" color="#ccc"></uni-icons>
              <text class="tip-text">暂无关联车辆</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else class="empty-state">
        <uni-icons type="info" size="48" color="#ccc"></uni-icons>
        <text class="empty-text">暂无车位信息</text>
        <text class="empty-desc">1.请先登记车辆信息</text>
        <text class="empty-desc">2.已购买车位请联系管理员关联车位信息</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import MyParkingAPI from '@/api/myParking';
import DictDataAPI from '@/api/dict';
import { onShow ,onLoad ,onShareAppMessage ,onShareTimeline} from "@dcloudio/uni-app"
// 响应式数据
const loading = ref(false);
const myParkingList = ref([]);

// 方法
const navigateTo = (url) => {
  uni.navigateTo({ url });
};

// 加载我的车位列表
const loadMyParkingList = async () => {
  loading.value = true;
  try {
    const result = await MyParkingAPI.getMyParkingList();
    if (result.code === 200) {
      myParkingList.value = result.data || [];
    } else {
      uni.showToast({
        title: result.message || '获取车位信息失败',
        icon: 'error'
      });
    }
  } catch (error) {
    console.error('获取车位列表失败：', error);
    uni.showToast({
      title: '网络错误，请稍后重试',
      icon: 'error'
    });
  } finally {
    loading.value = false;
  }
};

// 刷新车位列表
const refreshParkingList = () => {
  loadMyParkingList();
};

// 获取到期状态样式类
const getExpireClass = (expireStatus) => {
  switch (expireStatus) {
    case 0: return 'status-normal';     // 正常
    case 1: return 'status-warning';    // 即将到期
    case 2: return 'status-expired';    // 已到期
    default: return 'status-unknown';   // 未知
  }
};

// 获取到期状态文本
const getExpireStatusText = (expireStatus) => {
  switch (expireStatus) {
    case 0: return '正常';
    case 1: return '即将到期';
    case 2: return '已到期';
    default: return '未知';
  }
};

// 判断是否需要续费
const needRenewal = (expireStatus) => {
  // 即将到期(1)或已到期(2)时显示续费按钮
  return expireStatus === 1 || expireStatus === 2;
};

// 处理续费
const handleRenewal = (parking) => {
  uni.showModal({
    title: '续费确认',
    content: `确定要为车位 ${parking.parkName} 续费吗？`,
    success: (res) => {
      if (res.confirm) {
        // 跳转到续费页面，传递车位信息和到期时间
        uni.navigateTo({
          url: `/pages/index/registration?garageId=${parking.garageId}&parkingId=${parking.id}&parkName=${parking.parkName}&endDate=${parking.endDateStr}&renewal=true`
        });
      }
    }
  });
};

// 获取车辆类型字典值
const carTypeOptions = ref([]);
// 根据车型ID获取车型名称
const getCarTypeName = (carTypeId) => {
  if (!carTypeId) return '未知车型';
  const carType = carTypeOptions.value.find(item => item.value === carTypeId.toString());
  return carType ? carType.label : '未知车型';
};

// 获取车辆类型字典值
function getCarTypeOptions() {
  DictDataAPI.getOptions('vehicle_type')
      .then((res) => {
        if (res.code === 200 && res.data && res.data.length > 0) {
          carTypeOptions.value = res.data;
        }
      })
      .catch((error) => {
        console.error('获取车辆类型失败：', error);
        // 使用默认值
      })
}
onShow(() => {
  getCarTypeOptions();
  loadMyParkingList();
});
onLoad((options) => {
  console.log('options', options);
  if (options.scene) {
    let path = decodeURIComponent(options.scene)
    console.log('path', path)
  }
});
onShareAppMessage(() => {
  return {
    title: '好民居自助停车',
    path: '/pages/index/index',
  }
})
onShareTimeline(() => {
  return {
    title: '好民居自助停车',
    path: '/pages/index/index',
  }
})
</script>

<style lang="scss">
.container {
  background-color: $uni-bg-color;
}

.banner {
  width: 100%;
  height: 400rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
}

.banner-image {
  width: 100%;
  height: 100%;
}

.function-grid {
  margin-bottom: 50rpx;
}

.function-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.function-item {
  width: 48%;
  margin: 0 10rpx;
  border-radius: 12rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.function-item:active {
  transform: scale(0.98);
}

.register-box {
  background: linear-gradient(135deg, #2196F3, #0D47A1);
}

.payment-box {
  background: linear-gradient(135deg, #F44336, #B71C1C);
}

.help-box {
  background: linear-gradient(135deg, #4CAF50, #1B5E20);
}

.service-box {
  background: linear-gradient(135deg, #FF9800, #E65100);
}

.function-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.function-text {
  font-size: 34rpx;
  font-weight: bold;
  color: #FFFFFF;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.tech-accent {
  position: absolute;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  bottom: -20rpx;
  right: -20rpx;
}

.tech-accent::before {
  content: "";
  position: absolute;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.05);
  bottom: -30rpx;
  right: -30rpx;
}

.my-parking {
  background: linear-gradient(to right, #f8f9ff, #e8f5ff);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.07);
  border-left: 4rpx solid #2196F3;
  margin-top: 30rpx;
}

.section-title {
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 25rpx;
  color: #333;
  position: relative;
  padding-left: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #2196F3, #0D47A1);
  border-radius: 4rpx;
}

.refresh-btn {
  padding: 8rpx;
  border-radius: 50%;
  background-color: rgba(33, 150, 243, 0.1);
  transition: all 0.3s;
}

.refresh-btn:active {
  transform: scale(0.9);
  background-color: rgba(33, 150, 243, 0.2);
}

.loading-container {
  padding: 40rpx 0;
  text-align: center;
}

// 车位列表
.parking-list-container {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.parking-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.parking-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-bottom: 1rpx solid #dee2e6;
}

.parking-info {
  flex: 1;
}

.garage-name {
  font-size: 24rpx;
  color: #2c3e50;
  margin-bottom: 6rpx;
  font-weight: 500;
}

.parking-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 12rpx;
}

.parking-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-text {
  font-size: 22rpx;
  color: #7f8c8d;
  margin-left: 8rpx;
}

.parking-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.parking-type {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #fff;
  font-weight: 500;
}

.parking-type.public {
  background-color: #52c41a;
}

.parking-type.private {
  background-color: #faad14;
}

.expire-status {
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 18rpx;
  color: #fff;
  font-weight: 500;
}

.status-normal {
  background-color: #52c41a;
}

.status-warning {
  background-color: #faad14;
}

.status-expired {
  background-color: #ff4d4f;
}

.status-unknown {
  background-color: #8c8c8c;
}

.renewal-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s;
}

.renewal-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.4);
}

.renewal-text {
  font-size: 20rpx;
  color: #fff;
  margin-left: 6rpx;
  font-weight: bold;
}

// 车辆部分
.cars-section {
  padding: 0;
}

.cars-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #dee2e6;
}

.cars-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #495057;
}

.cars-list {
  background: #fff;
}

.car-item {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s;
  position: relative;
}

.car-item:last-child {
  border-bottom: none;
}

.car-item:active {
  background-color: #f8f9fa;
  transform: scale(0.98);
}

.car-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}

.car-icon-image {
  width: 60%;
  height: 60%;
  filter: brightness(0) invert(1);
}

.car-info {
  flex: 1;
}

.car-plate {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.car-model {
  font-size: 22rpx;
  color: #7f8c8d;
  margin-bottom: 4rpx;
}

.car-owner {
  font-size: 20rpx;
  color: #8c8c8c;
}

.no-cars-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 24rpx;
  background: #fff;
}

.tip-text {
  font-size: 26rpx;
  color: #8c8c8c;
  margin-top: 16rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #8c8c8c;
  margin-top: 16rpx;
}

.empty-desc {
  font-size: 22rpx;
  color: #bfbfbf;
  margin-top: 8rpx;
}
</style> 