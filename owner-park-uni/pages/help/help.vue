<template>
  <view class="help-container">
    <!-- 头部导航 -->
    <view class="help-header">
      <view class="header-bg">
        <view class="bg-decoration"></view>
      </view>
      <view class="header-content">
        <view class="header-title">使用帮助</view>
        <view class="header-subtitle">好民居自助停车小程序使用指南</view>
      </view>
    </view>

    <!-- 快速导航 -->
    <view class="quick-nav">
      <view class="nav-title">快速导航</view>
      <view class="nav-grid">
        <view
          class="nav-item"
          v-for="(nav, index) in quickNavs"
          :key="index"
          @tap="scrollToSection(nav.id)"
        >
          <view class="nav-icon" :style="{ background: nav.color }">
            <uni-icons :type="nav.icon" size="20" color="#fff"></uni-icons>
          </view>
          <text class="nav-text">{{ nav.title }}</text>
        </view>
      </view>
    </view>

    <!-- 帮助内容 -->
    <view class="help-content">
      <!-- 快速开始 -->
      <view class="help-section" id="quick-start">
        <view class="section-header">
          <view class="section-icon quick-start-icon">
            <uni-icons type="flag" size="24" color="#fff"></uni-icons>
          </view>
          <view class="section-title">快速开始</view>
        </view>
        <view class="section-content">
          <view class="step-list">
            <view class="step-item" v-for="(step, index) in quickStartSteps" :key="index">
              <view class="step-number">{{ index + 1 }}</view>
              <view class="step-content">
                <view class="step-title">{{ step.title }}</view>
                <view class="step-desc">{{ step.desc }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 车位登记流程 -->
      <view class="help-section" id="registration">
        <view class="section-header">
          <view class="section-icon registration-icon">
            <uni-icons type="compose" size="24" color="#fff"></uni-icons>
          </view>
          <view class="section-title">车位登记</view>
        </view>
        <view class="section-content">
          <view class="flow-diagram">
            <view class="flow-step" v-for="(step, index) in registrationFlow" :key="index">
              <view class="flow-icon">
                <uni-icons :type="step.icon" size="20" color="#667eea"></uni-icons>
              </view>
              <view class="flow-content">
                <view class="flow-title">{{ step.title }}</view>
                <view class="flow-desc">{{ step.desc }}</view>
              </view>
              <view class="flow-arrow" v-if="index < registrationFlow.length - 1">
                <uni-icons type="arrowdown" size="16" color="#ccc"></uni-icons>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 车辆管理 -->
      <view class="help-section" id="car-management">
        <view class="section-header">
          <view class="section-icon car-icon">
            <uni-icons type="cart" size="24" color="#fff"></uni-icons>
          </view>
          <view class="section-title">车辆管理</view>
        </view>
        <view class="section-content">
          <view class="feature-list">
            <view class="feature-item" v-for="(feature, index) in carFeatures" :key="index">
              <view class="feature-icon">
                <uni-icons :type="feature.icon" size="18" color="#52c41a"></uni-icons>
              </view>
              <view class="feature-content">
                <view class="feature-title">{{ feature.title }}</view>
                <view class="feature-desc">{{ feature.desc }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 缴费说明 -->
<!--      <view class="help-section" id="payment">-->
<!--        <view class="section-header">-->
<!--          <view class="section-icon payment-icon">-->
<!--            <uni-icons type="wallet" size="24" color="#fff"></uni-icons>-->
<!--          </view>-->
<!--          <view class="section-title">缴费说明</view>-->
<!--        </view>-->
<!--        <view class="section-content">-->
<!--          <view class="payment-types">-->
<!--            <view class="payment-type" v-for="(type, index) in paymentTypes" :key="index">-->
<!--              <view class="type-header">-->
<!--                <view class="type-icon">-->
<!--                  <uni-icons :type="type.icon" size="16" color="#fff"></uni-icons>-->
<!--                </view>-->
<!--                <text class="type-name">{{ type.name }}</text>-->
<!--                <text class="type-price">{{ type.price }}</text>-->
<!--              </view>-->
<!--              <view class="type-desc">{{ type.desc }}</view>-->
<!--            </view>-->
<!--          </view>-->
<!--        </view>-->
<!--      </view>-->

      <!-- 常见问题 -->
      <view class="help-section" id="faq">
        <view class="section-header">
          <view class="section-icon faq-icon">
            <uni-icons type="help" size="24" color="#fff"></uni-icons>
          </view>
          <view class="section-title">常见问题</view>
        </view>
        <view class="section-content">
          <view class="faq-list">
            <view class="faq-item" v-for="(faq, index) in faqList" :key="index">
              <view class="faq-question" @tap="toggleFaq(index)">
                <text class="question-text">{{ faq.question }}</text>
                <view class="question-arrow" :class="{ 'expanded': faq.expanded }">
                  <uni-icons type="arrowdown" size="14" color="#666"></uni-icons>
                </view>
              </view>
              <view class="faq-answer" v-if="faq.expanded">
                <text class="answer-text">{{ faq.answer }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 联系我们 -->
      <view class="help-section" id="contact">
        <view class="section-header">
          <view class="section-icon contact-icon">
            <uni-icons type="phone" size="24" color="#fff"></uni-icons>
          </view>
          <view class="section-title">联系我们</view>
        </view>
        <view class="section-content">
          <view class="contact-list">
            <view class="contact-item" v-for="(contact, index) in contactList" :key="index">
              <view class="contact-icon">
                <uni-icons :type="contact.icon" size="20" color="#667eea"></uni-icons>
              </view>
              <view class="contact-content">
                <view class="contact-title">{{ contact.title }}</view>
                <view class="contact-value" @tap="handleContact(contact)">{{ contact.value }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部信息 -->
    <view class="help-footer">
      <view class="footer-content">
        <text class="footer-text">好民居自助停车管理系统</text>
        <text class="footer-version">版本 v1.0.0</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { onShow ,onLoad ,onShareAppMessage ,onShareTimeline} from "@dcloudio/uni-app"

// 快速导航数据
const quickNavs = ref([
  { id: 'quick-start', title: '快速开始', icon: 'flag', color: 'linear-gradient(135deg, #667eea, #764ba2)' },
  { id: 'registration', title: '车位登记', icon: 'compose', color: 'linear-gradient(135deg, #f093fb, #f5576c)' },
  { id: 'car-management', title: '车辆管理', icon: 'cart', color: 'linear-gradient(135deg, #4facfe, #00f2fe)' },
  { id: 'faq', title: '常见问题', icon: 'help', color: 'linear-gradient(135deg, #fa709a, #fee140)' },
  { id: 'contact', title: '联系我们', icon: 'phone', color: 'linear-gradient(135deg, #a8edea, #fed6e3)' }
]);

// 快速开始步骤
const quickStartSteps = ref([
  {
    title: '打开小程序',
    desc: '扫描二维码或搜索"好民居自助停车"小程序'
  },
  {
    title: '选择车位',
    desc: '在首页选择您需要的车位'
  },
  {
    title: '填写信息',
    desc: '输入车牌号、车型等基本信息'
  },
  {
    title: '选择缴费类型',
    desc: '根据需要选择日租、月租、季租或年租'
  },
  {
    title: '完成支付',
    desc: '使用微信支付完成缴费，开始使用车位'
  }
]);

// 车位登记流程
const registrationFlow = ref([
  {
    title: '进入登记页面',
    desc: '点击首页"车位登记"按钮',
    icon: 'home'
  },
  {
    title: '选择车位',
    desc: '从可用车位中选择合适的车位',
    icon: 'location'
  },
  {
    title: '输入车牌号',
    desc: '使用车牌键盘输入准确的车牌号',
    icon: 'compose'
  },
  {
    title: '选择车型',
    desc: '从下拉列表中选择对应的车型',
    icon: 'navigate'
  },
  {
    title: '选择缴费类型',
    desc: '根据使用需求选择缴费周期',
    icon: 'calendar'
  },
  {
    title: '确认信息',
    desc: '核对所有信息无误后提交',
    icon: 'checkmarkempty'
  },
  {
    title: '完成支付',
    desc: '使用微信支付完成缴费',
    icon: 'wallet'
  }
]);

// 车辆管理功能
const carFeatures = ref([
  {
    title: '添加车辆',
    desc: '为您的车位添加多辆车辆信息',
    icon: 'plus'
  },
  {
    title: '查看车辆',
    desc: '查看车位下所有关联的车辆',
    icon: 'eye'
  },
  {
    title: '删除车辆',
    desc: '移除不再使用的车辆信息',
    icon: 'trash'
  },
  {
    title: '车位续费',
    desc: '为即将到期的车位进行续费',
    icon: 'refreshempty'
  }
]);

// 缴费类型说明
const paymentTypes = ref([
  {
    name: '日租',
    price: '¥10/天',
    desc: '适合短期临时停车，按天计费',
    icon: 'calendar'
  },
  {
    name: '月租',
    price: '¥200/月',
    desc: '适合长期固定停车，性价比高',
    icon: 'calendar'
  },
  {
    name: '季租',
    price: '¥540/季',
    desc: '三个月套餐，享受优惠价格',
    icon: 'calendar'
  },
  {
    name: '年租',
    price: '¥2000/年',
    desc: '全年套餐，最大优惠力度',
    icon: 'calendar'
  }
]);

// 常见问题
const faqList = ref([
  {
    question: '如何选择合适的车位？',
    answer: '您可以在首页查看所有可用车位，根据位置、价格和车位类型选择最适合的车位。公用车位价格相对便宜，私家车位更加私密。',
    expanded: false
  },
  {
    question: '车牌号输入错误怎么办？',
    answer: '如果发现车牌号输入错误，请及时联系客服进行修改。为避免错误，建议使用车牌键盘仔细输入。',
    expanded: false
  },
  {
    question: '可以为一个车位添加多辆车吗？',
    answer: '可以的。一个车位可以关联多辆车，方便家庭用户管理多辆车辆。',
    expanded: false
  },
  {
    question: '如何进行车位续费？',
    answer: '在"我的车位"页面，点击即将到期车位的续费按钮，选择续费周期并完成支付即可。',
    expanded: false
  },
  {
    question: '支付失败怎么处理？',
    answer: '如果支付失败，请检查网络连接和微信支付设置。如果问题持续存在，请联系客服协助处理。',
    expanded: false
  },
  {
    question: '可以申请退款吗？',
    answer: '根据使用条款，在特定条件下可以申请退款。具体退款政策请联系客服咨询。',
    expanded: false
  }
]);

// 联系方式
const contactList = ref([
  {
    title: '客服热线',
    value: '0451-51753591',
    icon: 'phone'
  }
]);

// 方法
const scrollToSection = (sectionId) => {
  uni.pageScrollTo({
    selector: `#${sectionId}`,
    duration: 300
  });
};

const toggleFaq = (index) => {
  faqList.value[index].expanded = !faqList.value[index].expanded;
};

const handleContact = (contact) => {
  if (contact.title === '客服热线') {
    uni.makePhoneCall({
      phoneNumber: contact.value
    });
  } else if (contact.title === '邮箱地址') {
    uni.setClipboardData({
      data: contact.value,
      success: () => {
        uni.showToast({
          title: '邮箱地址已复制',
          icon: 'success'
        });
      }
    });
  } else {
    uni.setClipboardData({
      data: contact.value,
      success: () => {
        uni.showToast({
          title: '内容已复制',
          icon: 'success'
        });
      }
    });
  }
};

onShareAppMessage(() => {
  return {
    title: '使用帮助-好民居自助停车',
    path: '/pages/help/help',
  }
})
onShareTimeline(() => {
  return {
    title: '使用帮助-好民居自助停车',
    path: '/pages/help/help',
  }
})
</script>

<style lang="scss">
.help-container {
  background: linear-gradient(180deg, #f0f2f5 0%, #ffffff 100%);
  min-height: 100vh;
}

// 头部样式
.help-header {
  position: relative;
  height: 300rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.header-content {
  position: absolute;
  bottom: 40rpx;
  left: 30rpx;
  right: 30rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 12rpx;
}

.header-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

// 快速导航
.quick-nav {
  margin: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.nav-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 24rpx;
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  border-radius: 16rpx;
  background: #f8f9fa;
  transition: all 0.3s;
}

.nav-item:active {
  transform: scale(0.95);
  background: #e9ecef;
}

.nav-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.nav-text {
  font-size: 22rpx;
  color: #2c3e50;
  font-weight: 500;
  text-align: center;
}

// 帮助内容
.help-content {
  margin: 0 30rpx 30rpx;
}

.help-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-bottom: 1rpx solid #dee2e6;
}

.section-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.quick-start-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.registration-icon {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.car-icon {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.payment-icon {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.faq-icon {
  background: linear-gradient(135deg, #fa709a, #fee140);
}

.contact-icon {
  background: linear-gradient(135deg, #a8edea, #fed6e3);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.section-content {
  padding: 30rpx;
}

// 步骤列表
.step-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.step-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border-left: 4rpx solid #667eea;
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.step-desc {
  font-size: 24rpx;
  color: #7f8c8d;
  line-height: 1.5;
}

// 流程图
.flow-diagram {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.flow-step {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  position: relative;
}

.flow-icon {
  width: 60rpx;
  height: 60rpx;
  background: rgba(102, 126, 234, 0.1);
  border: 2rpx solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.flow-content {
  flex: 1;
}

.flow-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.flow-desc {
  font-size: 24rpx;
  color: #7f8c8d;
}

.flow-arrow {
  position: absolute;
  bottom: -24rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 32rpx;
  height: 32rpx;
  background: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

// 功能列表
.feature-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.feature-icon {
  width: 48rpx;
  height: 48rpx;
  background: rgba(82, 196, 26, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.feature-content {
  flex: 1;
}

.feature-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #7f8c8d;
}

// 缴费类型
.payment-types {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.payment-type {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
}

.type-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16rpx;
}

.type-icon {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #43e97b, #38f9d7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
}

.type-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.type-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #52c41a;
}

.type-desc {
  font-size: 22rpx;
  color: #7f8c8d;
  line-height: 1.4;
}

// FAQ样式
.faq-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.faq-item {
  background: #f8f9fa;
  border-radius: 16rpx;
  overflow: hidden;
  transition: all 0.3s;
}

.faq-question {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  cursor: pointer;
  transition: all 0.3s;
}

.faq-question:active {
  background: #e9ecef;
}

.question-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #2c3e50;
  flex: 1;
  margin-right: 16rpx;
}

.question-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s;
}

.question-arrow.expanded {
  transform: rotate(180deg);
}

.faq-answer {
  padding: 0 24rpx 24rpx;
  border-top: 1rpx solid #dee2e6;
  background: #fff;
  animation: fadeIn 0.3s ease;
}

.answer-text {
  font-size: 24rpx;
  color: #7f8c8d;
  line-height: 1.6;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 联系我们
.contact-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s;
}

.contact-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.contact-icon {
  width: 60rpx;
  height: 60rpx;
  background: rgba(102, 126, 234, 0.1);
  border: 2rpx solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.contact-content {
  flex: 1;
}

.contact-title {
  font-size: 26rpx;
  color: #7f8c8d;
  margin-bottom: 8rpx;
}

.contact-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
}

// 底部信息
.help-footer {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx 20rpx 0 0;
  margin: 0 30rpx;
  padding: 40rpx;
  text-align: center;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.footer-text {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
}

.footer-version {
  font-size: 22rpx;
  color: #7f8c8d;
}

// 响应式适配
@media screen and (max-width: 375px) {
  .nav-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .payment-types {
    grid-template-columns: 1fr;
  }
}
</style>
