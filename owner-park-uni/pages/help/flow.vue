<template>
  <view class="flow-container">
    <!-- 头部 -->
    <view class="flow-header">
      <view class="header-bg">
        <view class="bg-pattern"></view>
      </view>
      <view class="header-content">
        <view class="header-title">使用流程</view>
        <view class="header-subtitle">简单几步，轻松停车</view>
      </view>
    </view>

    <!-- 流程步骤 -->
    <view class="flow-steps">
      <view class="step-container" v-for="(step, index) in flowSteps" :key="index">
        <!-- 步骤卡片 -->
        <view class="step-card" :class="{ 'active': currentStep >= index }">
          <view class="step-number">
            <text class="number-text">{{ index + 1 }}</text>
          </view>
          
          <view class="step-content">
            <view class="step-icon">
              <uni-icons :type="step.icon" size="32" :color="currentStep >= index ? '#fff' : '#667eea'"></uni-icons>
            </view>
            
            <view class="step-info">
              <view class="step-title">{{ step.title }}</view>
              <view class="step-desc">{{ step.description }}</view>
              
              <!-- 详细步骤 -->
              <view class="step-details" v-if="step.details">
                <view class="detail-item" v-for="(detail, detailIndex) in step.details" :key="detailIndex">
                  <view class="detail-dot"></view>
                  <text class="detail-text">{{ detail }}</text>
                </view>
              </view>
              
              <!-- 操作按钮 -->
              <view class="step-action" v-if="step.action">
                <view class="action-btn" @tap="handleAction(step.action)">
                  <uni-icons type="arrowright" size="16" color="#667eea"></uni-icons>
                  <text class="action-text">{{ step.action.text }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 连接线 -->
        <view class="step-connector" v-if="index < flowSteps.length - 1">
          <view class="connector-line" :class="{ 'active': currentStep > index }"></view>
          <view class="connector-dot" :class="{ 'active': currentStep > index }"></view>
        </view>
      </view>
    </view>

    <!-- 底部操作 -->
    <view class="flow-footer">
      <view class="footer-actions">
        <view class="action-button primary" @tap="startFlow">
          <uni-icons type="flag" size="20" color="#fff"></uni-icons>
          <text class="button-text">开始使用</text>
        </view>
<!--        <view class="action-button secondary" @tap="viewDemo">-->
<!--          <uni-icons type="videocam" size="20" color="#667eea"></uni-icons>-->
<!--          <text class="button-text">观看演示</text>-->
<!--        </view>-->
      </view>
    </view>

    <!-- 演示弹窗 -->
    <uni-popup ref="demoPopup" type="center" background-color="#fff">
      <view class="demo-container">
        <view class="demo-header">
          <text class="demo-title">操作演示</text>
          <view class="close-btn" @tap="closeDemoDialog">
            <uni-icons type="closeempty" size="20" color="#666"></uni-icons>
          </view>
        </view>
        <view class="demo-content">
          <view class="demo-item" v-for="(demo, index) in demoList" :key="index" @tap="playDemo(demo)">
            <view class="demo-icon">
              <uni-icons type="videocam" size="24" color="#667eea"></uni-icons>
            </view>
            <view class="demo-info">
              <text class="demo-name">{{ demo.title }}</text>
              <text class="demo-desc">{{ demo.description }}</text>
            </view>
            <view class="demo-arrow">
              <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { onShow ,onLoad ,onShareAppMessage ,onShareTimeline} from "@dcloudio/uni-app"

// 当前步骤
const currentStep = ref(0);

// 弹窗引用
const demoPopup = ref();

// 流程步骤数据
const flowSteps = ref([
  {
    title: '打开小程序',
    description: '扫码或搜索进入好民居自助停车小程序',
    icon: 'home',
    details: [
      '微信扫描二维码',
      '或在微信中搜索"好民居自助停车"',
      '点击进入小程序'
    ],
    action: {
      type: 'switchTab',
      url: '/pages/index/index',
      text: '进入首页'
    }
  },
  {
    title: '选择车位',
    description: '浏览可用车位，选择合适的停车位置',
    icon: 'location',
    details: [
      '查看车位列表',
      '了解车位位置和价格',
      '选择合适的车位类型'
    ],
    action: {
      type: 'navigate',
      url: '/pages/index/registration',
      text: '选择车位'
    }
  },
  {
    title: '填写信息',
    description: '输入车牌号码和车辆基本信息',
    icon: 'compose',
    details: [
      '使用车牌键盘输入车牌号',
      '选择车辆类型',
      '填写联系方式'
    ]
  },
  {
    title: '选择缴费',
    description: '根据需要选择缴费类型和时长',
    icon: 'calendar',
    details: [
      '选择日租、月租、季租或年租',
      '查看费用明细',
      '确认缴费信息'
    ]
  },
  {
    title: '完成支付',
    description: '使用微信支付完成缴费',
    icon: 'wallet',
    details: [
      '确认支付金额',
      '选择微信支付',
      '完成支付流程'
    ]
  },
  {
    title: '开始使用',
    description: '支付成功后即可使用车位',
    icon: 'checkmarkempty',
    details: [
      '获得车位使用权',
      '查看车位信息',
      '管理车辆信息'
    ],
    action: {
      type: 'navigate',
      url: '/pages/my/parking',
      text: '查看我的车位'
    }
  }
]);

// 演示列表
const demoList = ref([
  {
    title: '车位选择演示',
    description: '如何选择合适的车位',
    video: 'demo1.mp4'
  },
  {
    title: '信息填写演示',
    description: '车牌号和车辆信息填写',
    video: 'demo2.mp4'
  },
  {
    title: '支付流程演示',
    description: '完整的支付操作流程',
    video: 'demo3.mp4'
  }
]);

// 页面加载时的动画
onMounted(() => {
  animateSteps();
});

// 步骤动画
const animateSteps = () => {
  let step = 0;
  const timer = setInterval(() => {
    if (step < flowSteps.value.length) {
      currentStep.value = step;
      step++;
    } else {
      clearInterval(timer);
    }
  }, 500);
};

// 处理操作
const handleAction = (action) => {
  if (action.type === 'navigate') {
    uni.navigateTo({
      url: action.url
    });
  }
  if (action.type === 'switchTab') {
    uni.switchTab({
      url: action.url
    });
  }
};

// 开始使用
const startFlow = () => {
  uni.navigateTo({
    url: '/pages/index/registration'
  });
};

// 查看演示
const viewDemo = () => {
  demoPopup.value.open();
};

// 关闭演示弹窗
const closeDemoDialog = () => {
  demoPopup.value.close();
};

// 播放演示
const playDemo = (demo) => {
  uni.showToast({
    title: `播放${demo.title}`,
    icon: 'none'
  });
  // 这里可以集成视频播放功能
};

onShareAppMessage(() => {
  return {
    title: '使用流程-好民居自助停车',
    path: '/pages/help/flow',
  }
})
onShareTimeline(() => {
  return {
    title: '使用流程-好民居自助停车',
    path: '/pages/help/flow',
  }
})
</script>

<style lang="scss">
.flow-container {
  background: linear-gradient(180deg, #f0f2f5 0%, #ffffff 100%);
  min-height: 100vh;
}

// 头部样式
.flow-header {
  position: relative;
  height: 280rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 10rpx,
    rgba(255, 255, 255, 0.05) 10rpx,
    rgba(255, 255, 255, 0.05) 20rpx
  );
}

.header-content {
  position: absolute;
  bottom: 40rpx;
  left: 30rpx;
  right: 30rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 12rpx;
}

.header-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

// 流程步骤
.flow-steps {
  padding: 40rpx 30rpx;
}

.step-container {
  position: relative;
  margin-bottom: 40rpx;
}

.step-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid transparent;
  transition: all 0.5s ease;
  transform: translateX(-20rpx);
  opacity: 0.6;
}

.step-card.active {
  transform: translateX(0);
  opacity: 1;
  border-color: #667eea;
  box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.2);
}

.step-content {
  display: flex;
  align-items: flex-start;
}

.step-number {
  position: absolute;
  top: -15rpx;
  left: 30rpx;
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);
  z-index: 2;
}

.number-text {
  font-size: 24rpx;
  font-weight: bold;
  color: #fff;
}

.step-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(102, 126, 234, 0.1);
  border: 2rpx solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  transition: all 0.3s;
}

.step-card.active .step-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: #667eea;
}

.step-info {
  flex: 1;
}

.step-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 12rpx;
}

.step-desc {
  font-size: 26rpx;
  color: #7f8c8d;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.step-details {
  margin-bottom: 20rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.detail-dot {
  width: 8rpx;
  height: 8rpx;
  background: #667eea;
  border-radius: 50%;
  margin-right: 16rpx;
}

.detail-text {
  font-size: 24rpx;
  color: #595959;
}

.step-action {
  margin-top: 20rpx;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 24rpx;
  background: rgba(102, 126, 234, 0.1);
  border: 1rpx solid rgba(102, 126, 234, 0.3);
  border-radius: 20rpx;
  transition: all 0.3s;
}

.action-btn:active {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(0.95);
}

.action-text {
  font-size: 22rpx;
  color: #667eea;
  margin-left: 8rpx;
}

// 连接线
.step-connector {
  position: absolute;
  left: 60rpx;
  top: 100%;
  width: 4rpx;
  height: 40rpx;
  z-index: 1;
}

.connector-line {
  width: 100%;
  height: 100%;
  background: #e9ecef;
  transition: background 0.5s ease;
}

.connector-line.active {
  background: linear-gradient(180deg, #667eea, #764ba2);
}

.connector-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12rpx;
  height: 12rpx;
  background: #e9ecef;
  border-radius: 50%;
  transition: background 0.5s ease;
}

.connector-dot.active {
  background: #667eea;
}

// 底部操作
.flow-footer {
  padding: 40rpx 30rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-top: 1rpx solid #f0f0f0;
}

.footer-actions {
  display: flex;
  gap: 20rpx;
}

.action-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  border-radius: 25rpx;
  transition: all 0.3s;
}

.action-button.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);
}

.action-button.primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx rgba(102, 126, 234, 0.3);
}

.action-button.secondary {
  background: rgba(102, 126, 234, 0.1);
  border: 2rpx solid rgba(102, 126, 234, 0.3);
}

.action-button.secondary:active {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(0.95);
}

.button-text {
  font-size: 28rpx;
  font-weight: 500;
  margin-left: 12rpx;
}

.action-button.primary .button-text {
  color: #fff;
}

.action-button.secondary .button-text {
  color: #667eea;
}

// 演示弹窗
.demo-container {
  background: #fff;
  border-radius: 20rpx;
  width: 80vw;
  max-height: 70vh;
  overflow: hidden;
}

.demo-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.demo-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.3s;
}

.close-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.9);
}

.demo-content {
  padding: 30rpx;
  max-height: 50vh;
  overflow-y: auto;
}

.demo-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s;
}

.demo-item:last-child {
  margin-bottom: 0;
}

.demo-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.demo-icon {
  width: 60rpx;
  height: 60rpx;
  background: rgba(102, 126, 234, 0.1);
  border: 2rpx solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.demo-info {
  flex: 1;
}

.demo-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.demo-desc {
  font-size: 24rpx;
  color: #7f8c8d;
}

.demo-arrow {
  margin-left: 16rpx;
}

// 动画效果
@keyframes slideInUp {
  from {
    transform: translateY(30rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.step-card {
  animation: slideInUp 0.5s ease forwards;
}

// 响应式适配
@media screen and (max-width: 375px) {
  .footer-actions {
    flex-direction: column;
  }

  .demo-container {
    width: 90vw;
  }
}
</style>
