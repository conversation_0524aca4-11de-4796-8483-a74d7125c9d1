<template>
  <view class="container">
    <view class="card-section">
      <view class="section-title">确认信息</view>
      <uni-list>
        <uni-list-item title="车牌号：" :note="formData.plateNum">
        </uni-list-item>

        <uni-list-item title="车位" :note="formData.parkNum">
        </uni-list-item>

        <uni-list-item title="计费类型" :note="formData.billingType">
        </uni-list-item>

        <uni-list-item title="开始时间" :note="formData.beginDate">
        </uni-list-item>

        <uni-list-item title="结束时间" :note="formData.endDate">
        </uni-list-item>
      </uni-list>
    </view>

    <view class="fee-container card-section">
      <view class="section-title">费用</view>
      <view class="fee-details">
        <text class="fee-label">应缴</text>
        <text class="fee-text">¥ {{ formData.shouldPay }}</text>
      </view>
    </view>

    <view class="payment-method card-section">
      <view class="section-title">支付方式</view>
      <view class="payment-method-item">
        <view class="method-info">
          <image class="method-icon" src="@/static/wechat-pay.png" mode="aspectFit"></image>
          <text class="method-name">微信支付</text>
        </view>
        <radio checked="true" color="#09bb07"></radio>
      </view>
    </view>

    <button class="pay-btn" @tap="confirmPayment">确认支付</button>
  </view>
</template>

<script setup>
import {ref} from 'vue';
import RegistrationAPI from '@/api/registration';
import {
  onShow,
  onHide,
  onLoad,
  onShareAppMessage,
  onShareTimeline
} from "@dcloudio/uni-app"

const  id = ref(0);

const confirmPayment = () => {
  // 创建支付订单
  const param = {
    registrationId: id.value,
    openid: uni.getStorageSync('openid'),
  };
  RegistrationAPI.createPaymentOrder(param).then((res) => {
    if (res.code === 200 && res.data) {
      // 调起支付
      uni.requestPayment({
        provider: 'wxpay',
        timeStamp: res.data.timeStamp,
        nonceStr: res.data.nonceStr,
        package: res.data.packageValue,
        signType: res.data.signType,
        paySign: res.data.paySign,
        success: function(res) {
          console.log('success:' + JSON.stringify(res));
          uni.switchTab({
            url: '/pages/index/index'
          })
        },
        fail: function(err) {
          console.log('fail:' + JSON.stringify(err));
        }
      });
    }
  });
};

const formData = ref({
  carInfo: '',
  ownerName: '',
  phone: '',
  address: '',
  parkingSpot: '',
  billingType: '月租', // 默认月租
  startTime: new Date().toISOString().split('T')[0], // 格式化为yyyy-MM-dd
  endTime: '', // 由后端计算返回
  periodCount: 1, // 期数（月数、季度数、年数）
  shouldPay: '0.00' // 应缴费用
});

const getDetail = (id) => {
  RegistrationAPI.getRegistrationDetail(id).then((res) => {
    if (res.code === 200 && res.data) {
      formData.value = res.data;
    }
  });
};

onLoad((e) => {
  console.log('e', e);
  id.value = e.registrationId;
  getDetail(e.registrationId);
});

</script>

<style>
.container {
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.card-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 0 0 10rpx 0;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.fee-container {
  padding-bottom: 20rpx;
}

.fee-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
}

.fee-label {
  font-size: 30rpx;
  color: #333;
}

.fee-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6600;
}

.payment-method-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
}

.method-info {
  display: flex;
  align-items: center;
}

.method-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}

.method-name {
  font-size: 28rpx;
}

.pay-btn {
  width: 92%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #09bb07;
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 45rpx;
  margin: 40rpx auto;
  box-shadow: 0 4rpx 10rpx rgba(9, 187, 7, 0.2);
}

.payment-modal {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
}

.modal-header {
  position: relative;
  padding: 30rpx;
  text-align: center;
}

.close-btn {
  position: absolute;
  left: 30rpx;
  top: 30rpx;
  font-size: 40rpx;
  color: #999;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
}

.payment-amount {
  text-align: center;
  padding: 30rpx 0;
}

.amount-symbol {
  font-size: 36rpx;
  font-weight: bold;
}

.amount-value {
  font-size: 60rpx;
  font-weight: bold;
}

.payment-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.status-text {
  font-size: 28rpx;
  color: #666;
}
</style> 