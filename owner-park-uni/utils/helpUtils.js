/**
 * 帮助工具类
 * 提供帮助相关的工具方法
 */

/**
 * 使用手册数据
 */
export const manualData = {
  title: '好民居自助停车小程序使用手册',
  version: 'v1.0.0',
  sections: [
    {
      title: '快速开始',
      content: [
        '1. 打开小程序：扫描二维码或搜索"好民居自助停车"',
        '2. 选择车位：浏览可用车位，选择合适位置',
        '3. 填写信息：输入车牌号和车辆信息',
        '4. 选择缴费：根据需要选择缴费类型',
        '5. 完成支付：使用微信支付完成缴费'
      ]
    },
    {
      title: '车位登记详细流程',
      content: [
        '步骤1：进入登记页面 - 点击首页"车位登记"按钮',
        '步骤2：选择车位 - 从可用车位中选择合适的车位',
        '步骤3：输入车牌号 - 使用车牌键盘输入准确的车牌号',
        '步骤4：选择车型 - 从下拉列表中选择对应的车型',
        '步骤5：选择缴费类型 - 根据使用需求选择缴费周期',
        '步骤6：确认信息 - 核对所有信息无误后提交',
        '步骤7：完成支付 - 使用微信支付完成缴费'
      ]
    },
    {
      title: '车辆管理功能',
      content: [
        '添加车辆：为您的车位添加多辆车辆信息',
        '查看车辆：查看车位下所有关联的车辆',
        '删除车辆：移除不再使用的车辆信息',
        '车位续费：为即将到期的车位进行续费'
      ]
    },
    {
      title: '缴费类型说明',
      content: [
        '日租：¥10/天 - 适合短期临时停车，按天计费',
        '月租：¥200/月 - 适合长期固定停车，性价比高',
        '季租：¥540/季 - 三个月套餐，享受优惠价格',
        '年租：¥2000/年 - 全年套餐，最大优惠力度'
      ]
    },
    {
      title: '常见问题解答',
      content: [
        'Q: 如何选择合适的车位？',
        'A: 您可以在首页查看所有可用车位，根据位置、价格和车位类型选择最适合的车位。',
        '',
        'Q: 车牌号输入错误怎么办？',
        'A: 如果发现车牌号输入错误，请及时联系客服进行修改。',
        '',
        'Q: 可以为一个车位添加多辆车吗？',
        'A: 可以的。一个车位可以关联多辆车，方便家庭用户管理多辆车辆。',
        '',
        'Q: 如何进行车位续费？',
        'A: 在"我的车位"页面，点击即将到期车位的续费按钮，选择续费周期并完成支付即可。'
      ]
    },
    {
      title: '联系我们',
      content: [
        '客服热线：400-123-4567',
        '工作时间：周一至周日 9:00-18:00',
        '邮箱地址：<EMAIL>',
        '官方网站：www.parking.com'
      ]
    }
  ]
}

/**
 * 生成使用手册文本
 */
export const generateManualText = () => {
  let text = `${manualData.title}\n`
  text += `版本：${manualData.version}\n`
  text += `生成时间：${new Date().toLocaleString()}\n\n`
  
  manualData.sections.forEach((section, index) => {
    text += `${index + 1}. ${section.title}\n`
    text += '='.repeat(section.title.length + 4) + '\n\n'
    
    section.content.forEach(item => {
      if (item.trim()) {
        text += `${item}\n`
      } else {
        text += '\n'
      }
    })
    text += '\n'
  })
  
  return text
}

/**
 * 下载使用手册
 */
export const downloadManual = () => {
  const manualText = generateManualText()
  
  // 在小程序中，我们可以将内容复制到剪贴板
  uni.setClipboardData({
    data: manualText,
    success: () => {
      uni.showToast({
        title: '使用手册已复制到剪贴板',
        icon: 'success',
        duration: 2000
      })
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'error'
      })
    }
  })
}

/**
 * 分享使用手册
 */
export const shareManual = () => {
  return {
    title: '好民居自助停车小程序使用手册',
    desc: '简单几步，轻松停车',
    path: '/pages/help/help'
  }
}

/**
 * 获取帮助统计数据
 */
export const getHelpStats = () => {
  return {
    totalSections: manualData.sections.length,
    totalSteps: manualData.sections.reduce((total, section) => {
      return total + section.content.length
    }, 0),
    estimatedReadTime: Math.ceil(manualData.sections.reduce((total, section) => {
      return total + section.content.join(' ').length
    }, 0) / 200) // 假设每分钟阅读200字
  }
}

/**
 * 搜索帮助内容
 */
export const searchHelp = (keyword) => {
  const results = []
  
  manualData.sections.forEach((section, sectionIndex) => {
    if (section.title.includes(keyword)) {
      results.push({
        type: 'title',
        sectionIndex,
        title: section.title,
        content: section.title
      })
    }
    
    section.content.forEach((item, itemIndex) => {
      if (item.includes(keyword)) {
        results.push({
          type: 'content',
          sectionIndex,
          itemIndex,
          title: section.title,
          content: item
        })
      }
    })
  })
  
  return results
}

/**
 * 获取相关帮助建议
 */
export const getHelpSuggestions = (currentPage) => {
  const suggestions = {
    '/pages/index/index': [
      { title: '快速开始', section: 0 },
      { title: '车位登记流程', section: 1 }
    ],
    '/pages/index/registration': [
      { title: '车位登记详细流程', section: 1 },
      { title: '缴费类型说明', section: 3 }
    ],
    '/pages/my/parking': [
      { title: '车辆管理功能', section: 2 },
      { title: '常见问题解答', section: 4 }
    ],
    '/pages/my/payment-record': [
      { title: '缴费类型说明', section: 3 },
      { title: '常见问题解答', section: 4 }
    ]
  }
  
  return suggestions[currentPage] || []
}
