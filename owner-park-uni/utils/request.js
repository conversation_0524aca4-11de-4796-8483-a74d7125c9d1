import Request from '@/utils/luch-request/index.js'
import baseUrl from "@/utils/global.js"
import loginManager from '@/utils/loginManager.js'

const http = new Request()
http.setConfig((config) => { /* 设置全局配置 */
	// config.baseURL = 'http://127.0.0.1:9004/'
	config.baseURL = baseUrl
	config.header = {
		...config.headers,
		'Content-Type': 'application/json'
	}
	return config
})

// 不需要登录的接口列表
const noAuthUrls = [
	'/auth/miniapp/',  // 小程序登录相关接口
	'/sys/dict/data/',   //  字典数据接口
]

// 检查是否需要登录
const needAuth = (url) => {
	return !noAuthUrls.some(noAuthUrl => url.includes(noAuthUrl))
}

http.interceptors.request.use(async (config) => {
	// 如果是需要登录的接口，先确保已登录
	if (needAuth(config.url)) {
		try {
			await loginManager.ensureLogin()
		} catch (error) {
			console.error('登录失败，无法调用接口:', error)
			return Promise.reject({
				...config,
				error: '登录失败，请重试'
			})
		}
	}

	config.header = {
		...config.header,
		'Authorization': getTokenStorage()
	}
	return config
}, (config) => {
	return Promise.reject(config)
})


http.interceptors.response.use(async (response) => {
	const code = response.data.code

	// 处理token过期的情况
	if (code === 401 || code === 403) {
		console.log('Token过期，重新登录')
		try {
			await loginManager.reLogin()
			// 重新发起原请求
			const originalConfig = response.config
			originalConfig.header.Authorization = getTokenStorage()
			return http.request(originalConfig)
		} catch (error) {
			console.error('重新登录失败:', error)
			// 跳转到首页
			uni.reLaunch({
				url: '/pages/index/index'
			})
			return Promise.reject(response)
		}
	}

	return response.data
}, (err) => { // 请求错误做点什么。可以使用async await 做异步操作
	console.error('请求错误:', err)

	// 处理网络错误中的401
	if (err.data && err.data.code === 401) {
		loginManager.clearLoginStatus()
		uni.reLaunch({
			url: '/pages/index/index'
		})
	}

	return Promise.reject(err)
})


const getTokenStorage = () => {
	let token = ''
	try {
		token = uni.getStorageSync('token')
	} catch (e) {
		//TODO handle the exception
	}
	return token
}

export {
	http
}
