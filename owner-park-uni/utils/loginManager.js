import AuthAPI from '@/api/auth.js'

/**
 * 登录管理器
 * 确保在调用业务接口前先完成登录
 */
class LoginManager {
  constructor() {
    this.isLogging = false // 是否正在登录中
    this.loginPromise = null // 登录Promise
    this.isLoggedIn = false // 是否已登录
  }

  /**
   * 检查是否已登录
   */
  checkLoginStatus() {
    const token = uni.getStorageSync('token')
    this.isLoggedIn = !!token
    return this.isLoggedIn
  }

  /**
   * 执行登录
   */
  async doLogin() {
    return new Promise((resolve, reject) => {
      uni.login({
        provider: 'weixin',
        success: async (res) => {
          try {
            console.log('获取到微信code:', res.code)
            const loginResult = await AuthAPI.miniLogin(res.code)
            console.log('登录结果:', loginResult)
            
            if (loginResult.code === 200 && loginResult.data) {
              const { token, tokenType, openid } = loginResult.data
              
              if (token) {
                const fullToken = tokenType + ' ' + token
                uni.setStorageSync('token', fullToken)
                console.log('Token保存成功:', fullToken)
              }
              
              if (openid) {
                uni.setStorageSync('openid', openid)
                console.log('OpenID保存成功:', openid)
              }
              
              this.isLoggedIn = true
              resolve(loginResult)
            } else {
              console.error('登录失败:', loginResult.message)
              reject(new Error(loginResult.message || '登录失败'))
            }
          } catch (error) {
            console.error('登录请求失败:', error)
            reject(error)
          }
        },
        fail: (error) => {
          console.error('微信登录失败:', error)
          reject(error)
        }
      })
    })
  }

  /**
   * 确保已登录
   * 如果未登录则先登录，如果正在登录则等待登录完成
   */
  async ensureLogin() {
    // 如果已经登录，直接返回
    if (this.checkLoginStatus()) {
      return Promise.resolve()
    }

    // 如果正在登录中，返回登录Promise
    if (this.isLogging && this.loginPromise) {
      return this.loginPromise
    }

    // 开始登录
    this.isLogging = true
    this.loginPromise = this.doLogin()
      .then((result) => {
        this.isLogging = false
        return result
      })
      .catch((error) => {
        this.isLogging = false
        this.loginPromise = null
        throw error
      })

    return this.loginPromise
  }

  /**
   * 清除登录状态
   */
  clearLoginStatus() {
    this.isLoggedIn = false
    this.isLogging = false
    this.loginPromise = null
    uni.removeStorageSync('token')
    uni.removeStorageSync('openid')
  }

  /**
   * 重新登录
   */
  async reLogin() {
    this.clearLoginStatus()
    return this.ensureLogin()
  }
}

// 创建单例
const loginManager = new LoginManager()

export default loginManager
