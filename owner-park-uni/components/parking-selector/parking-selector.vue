<template>
  <uni-popup ref="popup" type="bottom" :safe-area="false" @change="onPopupChange">
    <view class="parking-selector-container">
      <!-- 标题栏 -->
      <view class="header">
        <view class="title">选择车位</view>
        <view class="close-btn" @tap="close">
          <uni-icons type="closeempty" size="24" color="#666" />
        </view>
      </view>

      <!-- 车库选择 -->
      <view class="garage-selector">
        <scroll-view class="garage-tabs" scroll-x="true" show-scrollbar="false">
          <view class="garage-tabs-container">
            <view
              v-for="(garage, index) in garageList"
              :key="garage.id"
              class="garage-tab"
              :class="{ 'active': selectedGarage?.id === garage.id }"
              @tap="selectGarage(garage, index)"
            >
              <text class="garage-tab-text">{{ garage.pname }}</text>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 搜索栏 -->
      <view class="search-container" v-if="selectedGarage">
        <uni-search-bar
          v-model="searchKeyword"
          placeholder="搜索车位号"
          @input="onSearchInput"
          @clear="onSearchClear"
        />
      </view>

      <!-- 车位列表 -->
      <view class="parking-list" v-if="selectedGarage">
        <!-- 加载骨架屏 -->
        <view class="skeleton-list" v-if="loading">
          <view class="skeleton-item" v-for="n in 6" :key="n">
            <view class="skeleton-icon"></view>
            <view class="skeleton-content">
              <view class="skeleton-line skeleton-title"></view>
              <view class="skeleton-tags">
                <view class="skeleton-tag"></view>
              </view>
            </view>
          </view>
        </view>

        <!-- 车位列表 -->
        <transition name="fade">
          <view class="parking-grid" v-if="!loading">
            <view
              v-for="(item, index) in filteredParkingList"
              :key="index"
              class="parking-item"
              :class="{ 'selected': selectedParking?.parkName === item.parkName }"
              @tap="selectParking(item)"
            >
            <!-- 选中状态指示器 -->
            <view class="selection-indicator" v-if="selectedParking?.parkName === item.parkName">
              <uni-icons type="checkmarkempty" size="16" color="#fff" />
            </view>

            <!-- 车位图标 -->
            <view class="parking-icon">
              <uni-icons type="navigate" size="24" :color="selectedParking?.parkName === item.parkName ? '#007AFF' : '#666'" />
            </view>

            <!-- 车位信息 -->
            <view class="parking-info">
              <view class="parking-name">{{ item.parkName }}</view>
              <view class="parking-details">
                <view class="parking-status">
                  <text class="status-text available">可用</text>
                </view>
              </view>
            </view>


          </view>
        </view>
        </transition>

        <!-- 空状态 -->
        <view v-if="!loading && filteredParkingList.length === 0" class="empty-state">
          <uni-icons type="info" size="48" color="#999" />
          <text class="empty-text">{{ selectedGarage ? '该车库暂无可用车位' : '暂无可用车位' }}</text>
        </view>
      </view>

      <!-- 未选择车库提示 -->
      <view v-if="!selectedGarage" class="no-garage-state">
        <uni-icons type="home" size="48" color="#999" />
        <text class="empty-text">请先选择车库</text>
      </view>

      <!-- 底部按钮 -->
      <view class="bottom-actions">
        <button class="cancel-btn" @tap="close">取消</button>
        <button class="confirm-btn" :disabled="!selectedParking" @tap="confirmSelection">
          确认选择
        </button>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, reactive, computed, defineEmits, defineExpose } from 'vue';
import RegistrationAPI from '@/api/registration';

// 定义事件
const emit = defineEmits(['select', 'close']);

// 数据定义
const popup = ref(null);
const searchKeyword = ref('');
const loading = ref(false);
const parkingList = ref([]);
const selectedParking = ref(null);
const garageList = ref([]);
const selectedGarage = ref(null);
const selectedGarageIndex = ref(0);
const param = reactive({
  openid: ''
});

// 过滤后的车位列表
const filteredParkingList = computed(() => {
  if (!searchKeyword.value) {
    return parkingList.value;
  }
  return parkingList.value.filter(item =>
    item.parkName?.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    item.poolName?.toLowerCase().includes(searchKeyword.value.toLowerCase())
  );
});

// 获取车库列表
const getGarageList = async () => {
  try {
    const result = await RegistrationAPI.getGarageList();
    if (result.code === 200 && result.data) {
      garageList.value = result.data || [];
      // 如果有车库数据，默认选择第一个
      if (garageList.value.length > 0) {
        selectedGarage.value = garageList.value[0];
        selectedGarageIndex.value = 0;
        // 获取对应车库的车位列表
        getParkingList();
      }
    }
  } catch (error) {
    console.error('获取车库列表失败：', error);
    uni.showToast({
      title: '获取车库列表失败',
      icon: 'error'
    });
  }
};

// 获取车位列表
const getParkingList = async () => {
  if (!selectedGarage.value) {
    parkingList.value = [];
    return;
  }

  loading.value = true;
  try {
    // 将车库信息添加到参数中
    const requestParam = {
      ...param,
      garageId: selectedGarage.value.id
    };

    const result = await RegistrationAPI.getAvailableParkingSpots(requestParam);
    if (result.code === 200 && result.data) {
      parkingList.value = result.data || [];
    }
  } catch (error) {
    console.error('获取车位列表失败：', error);
    uni.showToast({
      title: '获取车位列表失败',
      icon: 'error'
    });
  } finally {
    loading.value = false;
  }
};

// 搜索输入
const onSearchInput = (value) => {
  searchKeyword.value = value;
};

// 清除搜索
const onSearchClear = () => {
  searchKeyword.value = '';
};

// 选择车库
const selectGarage = (garage, index) => {
  if (selectedGarage.value?.id === garage.id) {
    return; // 如果选择的是同一个车库，不做任何操作
  }

  selectedGarage.value = garage;
  selectedGarageIndex.value = index;

  // 清空当前选择的车位
  selectedParking.value = null;

  // 获取新车库的车位列表
  getParkingList();
};

// 选择车位
const selectParking = (item) => {
  selectedParking.value = item;
};

// 确认选择
const confirmSelection = () => {
  if (!selectedParking.value) {
    uni.showToast({
      title: '请选择车位',
      icon: 'none'
    });
    return;
  }

  // 触发选择事件，同时传递车位和车库信息
  emit('select', {
    parking: selectedParking.value,
    garage: selectedGarage.value
  });
  
  // 显示选择成功提示
  uni.showToast({
    title: '选择成功',
    icon: 'success',
    duration: 1000
  });

  // 关闭弹窗
  close();
};

// 打开弹窗
const open = () => {
  selectedParking.value = null;
  searchKeyword.value = '';
  popup.value?.open();

  // 先获取车库列表，然后获取车位列表
  getGarageList();
};

// 关闭弹窗
const close = () => {
  popup.value?.close();
  emit('close');
};

// 弹窗状态变化
const onPopupChange = (e) => {
  if (!e.show) {
    // 弹窗关闭时重置状态
    selectedParking.value = null;
    searchKeyword.value = '';
  }
};

// 暴露方法给父组件
defineExpose({
  open,
  close
});
</script>

<style lang="scss" scoped>
.parking-selector-container {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  padding: 10rpx;
}

// 车库选择器样式
.garage-selector {
  padding: 20rpx 30rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fff;
}

.garage-tabs {
  width: 100%;
  white-space: nowrap;
}

.garage-tabs-container {
  display: flex;
  gap: 16rpx;
  padding: 0 4rpx;
}

.garage-tab {
  flex-shrink: 0;
  padding: 12rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  transition: all 0.3s ease;

  &.active {
    background-color: #007AFF;
    transform: translateY(-2rpx);
    box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
  }

  &:active {
    transform: scale(0.95);
  }
}

.garage-tab-text {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
  white-space: nowrap;

  .garage-tab.active & {
    color: #fff;
  }
}

.search-container {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.parking-list {
  flex: 1;
  padding: 20rpx 30rpx;
  overflow-y: auto;
  max-height: 50vh;
  min-height: 300rpx; // 保持最小高度，避免跳动
}

// 过渡动画
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

// 骨架屏样式
.skeleton-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  column-gap: 20rpx;
}

.skeleton-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  min-height: 140rpx;
}

.skeleton-icon {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 50%;
  margin-bottom: 12rpx;
}

.skeleton-content {
  width: 80%;
  text-align: center;
}

.skeleton-line {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4rpx;
  margin-bottom: 8rpx;
}

.skeleton-title {
  height: 28rpx;
  width: 80%;
  margin: 0 auto 8rpx;
}

.skeleton-tags {
  display: flex;
  justify-content: center;
  gap: 8rpx;
}

.skeleton-tag {
  height: 20rpx;
  width: 50rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 12rpx;
}



@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.parking-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  column-gap: 20rpx;
}

.parking-item {
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 16rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  border: 2rpx solid transparent;
  overflow: hidden;
  min-height: 140rpx;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 122, 255, 0.05), rgba(0, 86, 204, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &.selected {
    border-color: #007AFF;
    background: linear-gradient(135deg, #f0f8ff, #e6f3ff);
    box-shadow: 0 6rpx 20rpx rgba(0, 122, 255, 0.2);
    transform: translateY(-2rpx);

    &::before {
      opacity: 1;
    }
  }

  &:active {
    transform: scale(0.98);
  }
}

// 选中状态指示器
.selection-indicator {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 28rpx;
  height: 28rpx;
  background: linear-gradient(135deg, #007AFF, #0056CC);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
  z-index: 2;
}

// 车位图标
.parking-icon {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.parking-item.selected .parking-icon {
  background: linear-gradient(135deg, #007AFF, #0056CC);

  .uni-icons {
    color: #fff !important;
  }
}

// 车位信息
.parking-info {
  width: 80%;
  text-align: center;
  margin-bottom: 12rpx;
}

.parking-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.parking-pool {
  font-size: 22rpx;
  color: #7f8c8d;
  margin-bottom: 12rpx;
}

.parking-details {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.parking-type {
  display: flex;
  align-items: center;
}

.type-tag {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;

  &.public {
    background: linear-gradient(135deg, #e8f5e8, #d4edda);
    color: #52c41a;
  }

  &.private {
    background: linear-gradient(135deg, #fff2f0, #f8d7da);
    color: #ff4d4f;
  }
}

.parking-status {
  display: flex;
  align-items: center;
}

.status-text {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;

  &.available {
    background: linear-gradient(135deg, #e8f5e8, #d4edda);
    color: #52c41a;
  }
}



.empty-state, .loading-state, .no-garage-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-top: 20rpx;
}

.bottom-actions {
  padding: 20rpx 30rpx;
  display: flex;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fff;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: linear-gradient(135deg, #007AFF, #0056CC);
  color: #fff;

  &:disabled {
    background: #ccc;
    color: #999;
  }
}
</style>
